import argparse
import os
from fvutils.media import get_video_fps
from line_profiler import LineProfiler

from vsc import CompositeLEDInferenceV2, SplitMerge


def parse_args():
    parser = argparse.ArgumentParser("VSC CLI")
    parser.add_argument("-s", "--source_path", type=str, required=True, help="Path to the source video file.")
    parser.add_argument("-l", "--led_path", type=str, required=True, help="Path to the LED file.")
    parser.add_argument("-o", "--output_path", type=str, required=True, help="Path to save the output video file.")
    parser.add_argument("--data_folder", type=str, default=None, help="Path to the temp data folder, Use uuid if None.")
    parser.add_argument("--device", type=str, default="cuda:0", help="Device to use for inference.")
    parser.add_argument("--weights_folder", type=str, default="checkpoints", help="Path to the weights folder.")
    parser.add_argument("--max_interval_duration", type=int, default=60, help="Maximum duration for each sub-session, in seconds.")
    parser.add_argument("--threshold", type=int, default=180, help="Minimum duration for splitting, in seconds.")
    parser.add_argument("--detail", action="store_true", help="Whether to save detail execution time information.")
    return parser.parse_args()


class SplitMergeInference:
    def __init__(
        self,
        max_interval_duration: int = 60,
        threshold: int = 180,
        weight_folder: str = "checkpoints",
        device: str = "cuda:0",
        data_folder: str = None
    ):
        self.split_merge = SplitMerge(data_folder=data_folder, device=device, weights_folder=weight_folder)
        self.pipe = CompositeLEDInferenceV2(device=device, weights_folder=weight_folder)
        self.max_interval_duration = max_interval_duration
        self.threshold = threshold

    def run(self, source_path: str, led_path: str, output_path: str):
        fps = get_video_fps(source_path)
        session_list, metadata = self.split_merge.split(
            video_path=source_path,
            led_path=led_path,
            max_interval_duration=int(self.max_interval_duration * fps),
            threshold=int(self.threshold * fps),
        )

        for session in session_list:
            self.pipe.run(session)

        self.split_merge.merge(session_list, metadata, output_path)
        self.split_merge.release()


def inference(args):
    runner = SplitMergeInference(
        max_interval_duration=args.max_interval_duration,
        threshold=args.threshold,
        weight_folder=args.weights_folder,
        device=args.device,
        data_folder=args.data_folder,
    )

    runner.run(args.source_path, args.led_path, args.output_path)


def detail_inference(args):
    profiler = LineProfiler()
    profiler.add_function(SplitMerge.split)
    profiler.add_function(CompositeLEDInferenceV2.extract_source_corners)
    profiler.add_function(CompositeLEDInferenceV2.run_corners_stabilizing)
    profiler.add_function(CompositeLEDInferenceV2.run_corners_post_processing)
    profiler.add_function(CompositeLEDInferenceV2.run_post_processing_and_blending)
    profiler.add_function(SplitMerge.merge)
    profiler.runcall(inference, args=args)

    with open(os.path.splitext(args.output_path)[0] + ".txt", 'w') as f:
        profiler.print_stats(stream=f)


def main():
    args = parse_args()

    if args.detail:
        detail_inference(args)
    else:
        inference(args)


if __name__ == "__main__":
    main()
