from distutils.core import setup
from typing import List


try:
    from dotenv import load_dotenv
except ImportError:
    import pip

    pip.main(["install", "python-dotenv"])
    from dotenv import load_dotenv

import os

from setuptools import find_packages


def read_version(filename: str) -> str:
    """Read the version number from a file.
    Args:
        filename (str): The file to read the version number from.
    Returns:
        str: The version number.
    """
    if ".env" in filename:
        load_dotenv(filename)
        return os.environ.get('IMAGE_VERSION')
    with open(filename, "r") as v:
        major_minor_patch = v.read().strip()
    return major_minor_patch


def get_requirements(filename: str, get_dependency_links: bool = False) -> List[str]:
    """Get the requirements from a file.

    Args:
        filename (str): The file to get the requirements from.

    Returns:
        List[str]: The requirements.
    """
    line_strings = [dep.strip() for dep in open(filename, "r").readlines()]
    dependency = []
    dependency_links = []
    for line_str in line_strings:
        if "dependency_links" in line_str:
            # Add URLs for dependencies that cannot be found in PyPI
            dependency_links.append(line_str)
        else:
            dependency.append(line_str)

    requirements = dependency_links if get_dependency_links else dependency
    return requirements


setup_dict = dict(
    name="vsc",
    version=read_version(".env.dev"),
    description="Video Screen Composition.",
    author="Ho Nghia Khang",
    author_email="<EMAIL>",
    maintainer="Ho Nghia Khang",
    maintainer_email="<EMAIL>",
    url="https://gitlab.ftech.ai/computer-vision/projects/timi/vsc",
    download_url="http://minio.dev.ftech.ai",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Environment :: Console",
        "Intended Audience :: Science/Research",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: Apache Software License",
        "Natural Language :: English",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    packages=find_packages(exclude=("tests",)),
    include_package_data=True,
    python_requires=">=3.8",
    install_requires=get_requirements("requirements.txt"),
    dependency_links=get_requirements("requirements.txt", get_dependency_links=True),
    tests_require=get_requirements("requirements.txt") + ["pytest", "pytest-cov"],
    setup_requires=["wheel", "setuptools>=63.4.2"],
    zip_safe=False,
    license_files=("LICENSE",),
)


def main() -> None:
    setup(**setup_dict)


if __name__ == "__main__":
    main()
