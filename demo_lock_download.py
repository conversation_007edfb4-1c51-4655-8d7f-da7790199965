from vsc.composite_led_v2.utils.download_utils import download_with_hash_check


def main():
    weight_url = "http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/BiRefNet-matting-epoch_100_ver_2.onnx"
    weight_path = "/data/dwg/temps/vsc/issues/225/debug_download/BiRefNet-matting-epoch_100_ver_2.onnx"
    hash_code = "7418a49b9d3a0357cde61baeb34d035b"
    download_with_hash_check(weight_url, weight_path, hash_code)


if __name__ == "__main__":
    # main()
    from vsc.composite_led_v2.utils.download_utils import calculate_md5
    print(calculate_md5("/data/dwg/projects/vsc/checkpoints/CornerClassification_v1.12.0.onnx"))
