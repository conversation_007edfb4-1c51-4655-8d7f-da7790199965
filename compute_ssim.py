import argparse

import cv2
from skimage.metrics import structural_similarity


def parse_args():
    parser = argparse.ArgumentParser(description='Compare two videos by sampling frames and calculating differences.')
    parser.add_argument('video1', type=str, help='Path to the first video.')
    parser.add_argument('video2', type=str, help='Path to the second video.')
    return parser.parse_args()


def main():
    args = parse_args()
    capture1 = cv2.VideoCapture(args.video1)
    capture2 = cv2.VideoCapture(args.video2)

    results = []
    while True:
        ret1, image1 = capture1.read()
        ret2, image2 = capture2.read()
        if not ret1 or not ret2:
            break
        ssim, _ = structural_similarity(
            cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY),
            cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY),
            full=True
        )
        results.append(ssim)

        print(f"Min: {min(results)}, Max: {max(results)}, Mean: {sum(results) / len(results)}")

if __name__ == "__main__":
    main()
