image: docker:23

services:
  - name: docker:dind

workflow:
  rules:
    - if: $CI_COMMIT_TAG
      variables:
        IMAGE_VERSION: ${CI_COMMIT_TAG}
    # Default rule if no other rule is matched
    - when: always
      variables:
        IMAGE_VERSION: "v0.0.0"

variables:
  REGISTRY_REPO: registry.gitlab.ftech.ai
  TAG: ${IMAGE_VERSION}
  DOCKER_TLS_CERTDIR: ""

stages:
  - linting
  - sonarqube-check
  - build
  - test
  - push

.install_necessary_packages:
  before_script:
    - cp ${DEVELOPMENT_ENV_FILE} .env.dev
    - apk add make
    - apk add --no-cache bash

isort:
  stage: linting
  script:
    - apk add python3 py3-pip
    - pip install isort==5.13.2
    - isort . --check-only

black:
  stage: linting
  script:
    - apk add python3 py3-pip
    - pip install black==24.1.1
    - black --check .

flake8:
  stage: linting
  script:
    - apk add python3 py3-pip
    - pip install flake8==6.0.0
    - flake8 .

sonarqube-check:
  stage: sonarqube-check
  image:
    name: sonarsource/sonar-scanner-cli:5.0
    entrypoint: [""]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true

build_image:
  extends: .install_necessary_packages
  stage: build
  script:
    - make build_image
  needs:
    - isort
    - black
    - flake8

test:
  extends: .install_necessary_packages
  stage: test
  script:
    - make up
    - make test
    - make down
  needs:
    - build_image

push_image:
  extends: .install_necessary_packages
  stage: push
  script:
    - make push_image
  needs:
    - test

push_package:
  extends: .install_necessary_packages
  stage: push
  script:
    - make up
    - make push_package
    - make down
  when: manual
  needs:
    - test
