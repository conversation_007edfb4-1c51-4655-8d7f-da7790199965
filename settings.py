import os

import pandas as pd
from fvutils.media import get_video_properties
from tqdm import tqdm


def get_all_file_paths(root_folder):
    file_paths = []
    for dirpath, _, filenames in os.walk(root_folder):
        for filename in filenames:
            full_path = str(os.path.join(dirpath, filename))
            relative_path = os.path.relpath(full_path, root_folder)
            file_paths.append(relative_path)
    return file_paths


def get_resolution(width, height):
    if max(width, height) < 1500:
        return "HD"
    elif max(width, height) < 2000:
        return "FullHD"
    elif max(width, height) < 3000:
        return "2K"
    else:
        return "4K"


def main():
    videos_folder = "/data/dwg/datasets/vsc_datasets/videos/checklist_v2"
    output_path = "/data/dwg/datasets/vsc_datasets/videos/settings.csv"
    path_list = get_all_file_paths(videos_folder)

    df = pd.DataFrame(columns=["Name", "Resolution", "Duration"])

    for path in tqdm(path_list):
        source_path = os.path.join(videos_folder, path)
        properties = get_video_properties(source_path)
        duration = float(properties["duration"])
        resolution = get_resolution(int(properties["width"]), int(properties["height"]))
        df.loc[len(df.index)] = [path, resolution, duration]

    df.to_csv(output_path, index=False)



if __name__ == "__main__":
    main()
