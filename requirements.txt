python-dotenv~=1.0.1
minio~=7.1.17
kafka-python~=2.0.2
codetiming~=1.4.0
alive-progress~=3.1.4
hydra-core~=1.3.2
ffmpeg-python~=0.2.0
tqdm~=4.66.2
pyyaml~=6.0.1
ruamel.yaml~=0.17.32
python-decouple~=3.8
opencv-python~=4.6.0.66
opencv-contrib-python~=4.10.0.84
onnxruntime-gpu~=1.14.1
pillow~=10.2.0
pymongo~=4.5.0
pymatting~=1.1.10
cupy-cuda11x  # https://pypi.org/project/cupy/
pandas~=2.0.3
deprecation~=2.1.0
pre-commit~=3.4.0
pytest~=8.0.0
build~=1.0.3
https://minio.dev.ftech.ai/axiom-client/axiom_client-1.8.0-py3-none-any.whl # dependency_links
coverage~=7.4.3
pytest-cov~=4.1.0
psutil~=5.9.8
gputil~=1.4.0
seaborn~=0.13.2
vc2-matting@http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/vc2_matting-2.4.7-py3-none-any.whl
fvutils@https://minio.dev.ftech.ai/fvutils-1.0.0-940d282d/fvutils-1.1.16.dev2-py3-none-any.whl
pretty-errors~=1.2.25
scikit-image~=0.25.0

requests~=2.32.3
matplotlib~=3.10.1
ultralytics~=8.3.0
numpy~=1.24.4
numba~=0.61.0
filelock~=3.13.1
scipy~=1.15.2
torch~=2.3.1
torchvision~=0.18.1
line_profiler~=4.2.0
