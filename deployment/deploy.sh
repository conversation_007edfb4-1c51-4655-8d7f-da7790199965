#!/bin/bash

cmd=$1

ENV_FILE=".env.dev"

# Check if we run from Gitlab runner (use Gitlab variables, not .env file)
if [[ ! -f $ENV_FILE ]]
then
    echo ".env.dev file does not exist on your filesystem. Read from environment variables."
else
    source $ENV_FILE
fi

IMAGE_NAME=$PROJECT_REPO$IMAGE_LED
TAG=$IMAGE_VERSION


usage() {
    echo "Available commands:"
    echo " linting                  automated checking and refactor code"
    echo " build_image              build docker image"
    echo " push_image               push docker image to gitlab registry"
    echo " test                     test code"
    echo " build_package            build package"
    echo " push_package             push package to axiom"
    echo " up                       up composite service"
    echo " down                     down composite service"
    echo " run_docker               run and exec to docker environment"

}

if [[ -z "$cmd" ]]; then
    echo "Missing command"
    usage
    exit 1
fi

linting() {
    echo "Automated checking and refactor with Isort, Black, and Flake8"
    pre-commit run --all-files
}

build_image() {
    echo "⚒️ Building docker image..."
    docker build --cache-from $IMAGE_NAME:$TAG --tag $IMAGE_NAME:$TAG -f docker/Dockerfile . || exit 1
    echo "Build ${IMAGE_NAME}:${TAG} completed ✅"
}

push_image() {
    echo "👨‍💻 Login to registry.gitlab.ftech.ai..."
    docker login registry.gitlab.ftech.ai -u khanghn -p ********************

    echo "🚀 Pushing image [$IMAGE_NAME:$TAG]"
    docker push $IMAGE_NAME:$TAG
    echo "Push ${IMAGE_NAME}:${TAG} to registry.gitlab.ftech.ai completed ✅"
}

test() {
    echo "🧪️ Start System & Integration testing ..."
    docker exec -it --tty=false composite_led pytest --cov .
}

clear() {
    echo "Clear temp folder after building..."
    rm -rf build vsc.egg-info
    echo "Done!"
}

build_package() {
    echo "Start build package..."
    python -m build -w
    clear
}

push_package() {
    echo "Pushing package to axiom..."
    docker exec -it --tty=false composite_led python -m build -w
    docker exec -it --tty=false composite_led python vsc/utils/axiom.py
}


up() {
    docker --version
    docker compose version
    docker compose -f ./deployment/docker-compose.yml up composite_led -d
}

down() {
    docker --version
    docker compose version
    docker compose -f ./deployment/docker-compose.yml down composite_led
}

run_docker() {
    docker compose -f ./deployment/docker-compose.yml run composite_led /bin/bash
}


case $cmd in
linting)
    linting "$@"
    ;;
build_image)
    build_image "$@"
    ;;
push_image)
    push_image "$@"
    ;;
test)
    test "$@"
    ;;
build_package)
    build_package "$@"
    ;;
push_package)
    push_package "$@"
    ;;
up)
    up "$@"
    ;;
down)
    down "$@"
    ;;
run_docker)
    run_docker "$@"
    ;;
*)
    echo -n "Unknown command: $cmd"
    usage
    exit 1
    ;;
esac
