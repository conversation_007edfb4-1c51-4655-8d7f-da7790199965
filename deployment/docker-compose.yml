version: '3.9'

services:
  composite_led:
    image: ${PROJECT_REPO}${IMAGE_LED}:${IMAGE_VERSION} # TODO: automation get tag from version file.
    container_name: composite_led
    build:
      context: ${PWD}/docker
      dockerfile: Dockerfile
    environment:
      - IMAGE_VERSION=${IMAGE_VERSION}
      - NVIDIA_VISIBLE_DEVICES=all               # Make all GPUs visible
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility,video
    # volumes:
    #   - ${PWD}:/vsc
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              device_ids: ["${GPU_DEVICE_IDS_LED}"]               # CI runs on GPU-card 2. Set "all" to access all available GPUs
              capabilities: [gpu]
    runtime: nvidia                              # Ensure NVIDIA runtime is used
    command: tail -f /dev/null
