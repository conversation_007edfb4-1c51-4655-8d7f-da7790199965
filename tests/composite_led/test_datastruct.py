import numpy as np
import pytest

from vsc.composite_led.datastruct import Co<PERSON>inates, MaskScreen, MatteTimi


@pytest.mark.parametrize(
    "mask_screen",
    [
        MaskScreen(np.zeros((50, 50, 3), dtype='uint8')),
        MaskScreen(np.zeros((50, 50, 3), dtype='float')),
        MaskScreen(np.zeros((50, 50, 1), dtype='float')),
        MaskScreen(np.zeros((50, 50), dtype='float')),
    ],
)
def test_mask_screen(mask_screen):
    assert mask_screen.data is not None
    assert mask_screen.data.shape == (50, 50)
    assert mask_screen.data.dtype == 'uint8'


@pytest.mark.parametrize(
    "coordinates",
    [
        Coordinates(np.array([[0, 0], [0, 10], [10, 10], [10, 0]])),
        Coordinates(list(np.array([[0, 0], [0, 10], [10, 10], [10, 0]]))),
        Coordinates(np.array([[0, 0], [0, 10], [10, 10], [10, 0.0]])),
        Coordinates([[0, 0], [0, 10], [10, 10], [10, 0]]),
    ],
)
def test_coordinates(coordinates):
    assert coordinates.data is not None
    assert coordinates.data_list is not None
    assert coordinates.data.dtype == 'int32'
    assert coordinates.data_list == [[0, 0], [0, 10], [10, 10], [10, 0]]
    assert np.array_equal(coordinates.data, np.array([[0, 0], [0, 10], [10, 10], [10, 0]]))


@pytest.mark.parametrize(
    "matte",
    [
        MatteTimi(np.random.rand(100, 100).astype('float32')),
        MatteTimi(np.random.rand(100, 100, 1).astype('float32')),
        MatteTimi(np.random.rand(100, 100, 3).astype('float32')),
    ],
)
def test_matte_timi(matte):
    assert matte.data is not None
    assert matte.data.dtype == 'float32'
    assert matte.data.shape == (100, 100)
