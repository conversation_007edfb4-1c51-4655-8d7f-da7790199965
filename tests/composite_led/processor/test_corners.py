from typing import Union

import numpy as np
import pytest

from vsc.composite_led.processor.corners import CornersValidator


corner = CornersValidator()


@pytest.mark.parametrize(
    "bbox, point, expected_result",
    [
        # Test case 0: Inside
        ([0, 0, 10, 10], [1, 1], True),
        # Test case 1: Outside
        ([0, 0, 10, 10], [-1, -1], False),
        # Test case 2: On the edge
        ([0, 0, 10, 10], [10, 10], True),
    ],
)
def test_is_inside_bbox(bbox: list, point: list, expected_result: bool):
    result = corner.is_inside_bbox(bbox, point)
    assert result == expected_result


@pytest.mark.parametrize(
    "points, bbox, expected_result",
    [
        # Test case 0: Dtype input is list
        ([[1, 1], [7, 7]], [[0, 0, 10, 10], [6, 6, 8, 8]], [[1, 1], [7, 7]]),
        # Test case 1: Dtype input is np.ndarray
        (np.array([[1, 1], [7, 7]]), np.array([[0, 0, 10, 10], [6, 6, 8, 8]]), [[1, 1], [7, 7]]),
        ([[1, 1], [7, 7]], np.array([[0, 0, 10, 10], [6, 6, 8, 8]]), [[1, 1], [7, 7]]),
        (np.array([[1, 1], [7, 7]]), [[0, 0, 10, 10], [6, 6, 8, 8]], [[1, 1], [7, 7]]),
        # Test case 2: Left hidden
        ([[2, 1], [7, 7], [0, 0], [1, 0]], [[6, 6, 8, 8]], [[7, 7], [0, 0], [1, 0]]),
        # Test case 2: Right hidden
        ([[2, 1], [7, 7], [1919, 0]], [[6, 6, 8, 8]], [[7, 7], [1919, 0]]),
    ],
)
def test_get_points_inside_bbox(points: Union[list, np.ndarray], bbox: list, expected_result: list):
    frame = np.zeros((1080, 1920, 3))
    result = corner.get_points_inside_bbox(points, bbox, frame)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "coordinates, distance_thresh, expected_result",
    [
        # Test case 0
        (np.array([[0, 0], [1, 0]]), 100, []),
        (np.array([[0, 0], [1, 0], [200, 200]]), 100, [np.array([200, 200])]),
        (np.array([[0, 0], [600, 600], [200, 200]]), 100, list(np.array([[0, 0], [600, 600], [200, 200]]))),
    ],
)
def test_remove_noise(coordinates: np.ndarray, distance_thresh: int, expected_result: list):
    result = corner.remove_noise(coordinates, distance_thresh)
    assert np.array_equal(result, expected_result)
