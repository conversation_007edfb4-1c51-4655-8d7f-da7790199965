from typing import Union

import numpy as np
import pytest

from vsc.composite_led.functions.coordinates import Coordinates


@pytest.mark.parametrize(
    "coordinates, expected_result",
    [
        # Test case 0: C missing
        ([[0, 0], [0, 10], [10, 0]], np.array([10, 10])),
        ([[-5, 0], [-5, 10], [10, 0]], np.array([10, 10])),
        ([[-5, 0], [-5, 10], [1925, 0]], np.array([1925, 10])),
        # Test case 1: B missing
        ([[0, 0], [10, 10], [10, 0]], np.array([0, 10])),
        ([[0, 0], [1000, 10], [1000, 0]], np.array([0, 10])),
        # Test case 2: A missing
        ([[0, 10], [10, 10], [10, 0]], np.array([0, 0])),
        ([[0, 10], [1000, 10], [1000, 0]], np.array([0, 0])),
        # Test case 3: D missing
        ([[0, 0], [0, 10], [10, 10]], np.array([10, 0])),
        ([[0, 0], [0, 10], [1000, 10]], np.array([1000, 0])),
    ],
)
def test_find_corner_missing_parallelogram(coordinates: list, expected_result: np.ndarray):
    result = Coordinates.find_corner_missing_parallelogram(coordinates)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "points, expected_result",
    [
        # Test case 0
        ([[0, 0], [0, 10], [10, 0]], [0, 0]),
    ],
)
def test_find_first_quadrant_value(points: list, expected_result: list):
    result = Coordinates.find_first_quadrant_value(points)
    assert result == expected_result


@pytest.mark.parametrize(
    "coordinates, expected_result",
    [
        # Test case 0: Dtype input in list
        # ([[0, 0], [0, 10], [10, 10], [10, 0]], np.array([[0, 0], [0, 10], [10, 10], [10, 0]])), <-- Failure: input list is no support.
        (np.array([[0, 0], [0, 10], [10, 10], [10, 0]]), np.array([[0, 0], [0, 10], [10, 10], [10, 0]])),
        (np.array([[0, 0], [10, 10], [0, 10], [10, 0]]), np.array([[0, 0], [0, 10], [10, 10], [10, 0]])),
        (np.array([[0, 0], [5, 10], [0, 10], [10, 10], [10, 0]]), np.array([[0, 0], [0, 10], [5, 10], [10, 10], [10, 0]])),
        (np.array([[0, 10], [0, 0]]), np.array([[0, 0], [0, 10]])),
        (np.array([[0, 0]]), np.array([[0, 0]])),
        # Test case 1: Dtype input in list
        # ([[0, 0], [0, 10], [10, 10], [10, 0]], np.array([[0, 0], [0, 10], [10, 10], [10, 0]])), <-- Failure: input list is no support.
    ],
)
def test_sort_coordinates(coordinates: Union[np.ndarray, list], expected_result: np.ndarray):
    result = Coordinates.sort_coordinates(coordinates)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "results, label, expected_result",
    [
        # Test case 0: Get corners label.
        (
            np.array(
                [
                    [1.0, 74.0, 1553.0, 966.0, 1.0, 0.9245675802230835],
                    [1451.0, 84.0, 1529.0, 176.0, 0, 0.6804652810096741],
                    [16.0, 75.0, 121.0, 175.0, 0, 0.4561600387096405],
                    [21.0, 874.0, 120.0, 962.0, 0, 0.4542412757873535],
                ]
            ),
            "corners",
            [
                [1451.0, 84.0, 1529.0, 176.0, 0, 0.6804652810096741],
                [16.0, 75.0, 121.0, 175.0, 0, 0.4561600387096405],
                [21.0, 874.0, 120.0, 962.0, 0, 0.4542412757873535],
            ],
        ),
        # Test case 1: Get screen label.
        (
            np.array(
                [
                    [1.0, 74.0, 1553.0, 966.0, 1.0, 0.9245675802230835],
                    [1451.0, 84.0, 1529.0, 176.0, 0, 0.6804652810096741],
                    [16.0, 75.0, 121.0, 175.0, 0, 0.4561600387096405],
                    [21.0, 874.0, 120.0, 962.0, 0, 0.4542412757873535],
                ]
            ),
            "screen",
            [[1.0, 74.0, 1553.0, 966.0, 1.0, 0.9245675802230835]],
        ),
    ],
)
def test_get_bbox(results: np.ndarray, label: str, expected_result: list):
    classes_name = ["corners", "screen"]
    result = Coordinates.get_bbox(results, label, classes_name)
    assert result == expected_result


@pytest.mark.parametrize(
    "coordinates, threshold, queue, expected_result",
    [
        # Test case 0: len(queue) == 1
        ([np.array([[0, 0], [0, 10], [10, 10], [10, 0]])], 20, [], [np.array([[0, 0], [0, 10], [10, 10], [10, 0]])]),
        # Test case 0: len(queue) == 2
        # ([np.array([[0, 0], [0, 10], [10, 10], [10, 0]])], 20, [[[0, 0], [0, 10], [10, 10], [10, 0]]], [np.array([[0, 0], [0, 10], [10, 10], [10, 0]])]),
    ],
)
def test_stabilize_coordinates(coordinates: np.ndarray, threshold: int, queue: list, expected_result: list):
    Coordinates.coordinates_queue = queue
    result = Coordinates.stabilize_coordinates(coordinates, threshold)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "distance_list, threshold_noise, expected_result",
    [
        # Test case 0: No outliers
        ([1, 1, 2, 0], 6, False),
        # Test case 1: Has outliers
        ([1, 1, 7, 0], 6, True),
    ],
)
def test_detect_outliers(distance_list: list, threshold_noise: int, expected_result: bool):
    result = Coordinates.detect_outliers(distance_list, threshold_noise)
    assert result == expected_result


@pytest.mark.parametrize(
    "point1, point2, expected_result",
    [
        # Test case 0
        ([0, 0], [5, 0], 5),
        ([0, 0], [5, 5], 50**0.5),
    ],
)
def test_cal_distance(point1: list, point2: list, expected_result: float):
    result = Coordinates.cal_distance(point1, point2)
    assert result == expected_result


@pytest.mark.parametrize(
    "coordinates, expected_result",
    [
        # Test case 0
        (np.array([[0, 0], [0, 10], [10, 0]]), (90.0, "A")),
        (np.array([[0, 0], [10, 10], [10, 0]]), (90.0, "D")),
        ([[0, 0], [10, 10], [10, 0]], (90.0, "D")),
    ],
)
def test_calculate_largest_angle(coordinates: np.ndarray, expected_result: tuple):
    result = Coordinates.calculate_largest_angle(coordinates)
    assert result == expected_result


@pytest.mark.parametrize(
    "point1_1, point1_2, point2_1, point2_2, expected_result",
    [
        # Test case 0
        ([0, 10], [5, 10], [10, 0], [10, 5], np.array([10, 10])),
        ([0, 10], [5, 10], [10, 5], [10, 0], np.array([10, 10])),
        # ([0, 10], [5, 10], [0, 0], [10, 0], np.array([10, 10])),
    ],
)
def test_find_corner_missing_intersection(
    point1_1: list, point1_2: list, point2_1: list, point2_2: list, expected_result: np.ndarray
):
    result = Coordinates.find_corner_missing_intersection(point1_1, point1_2, point2_1, point2_2)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "coordinates, points_inside_rectangles, expected_result",
    [
        # Test case 0
        (np.array([[0, 0], [10, 0], [10, 10], [5, 5]]), [np.array([10, 10]), np.array([5, 5])], [[0, 0], [10, 0]]),
    ],
)
def test_get_point_on_timi(coordinates: np.ndarray, points_inside_rectangles: list[np.ndarray], expected_result: list):
    result = Coordinates.get_point_on_timi(coordinates, points_inside_rectangles)
    assert result == expected_result


@pytest.mark.parametrize(
    "points, point, parallel_line, axis_name, expected_result",
    [
        # Test case 0
        ([[0, 0], [10, 10], [5, 5]], [1000, 1000], 10, "y", np.array([10, 10])),
        ([[0, 0], [10, 10], [5, 5]], [1000, 1000], 4, "x", np.array([5, 5])),
    ],
)
def test_find_nearest_point(points: list, point: list, parallel_line: int, axis_name: str, expected_result: np.ndarray):
    result = Coordinates.find_nearest_point(points, point, parallel_line, axis_name)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "coordinates, points_inside_rectangles, mask_screen, expected_result",
    [
        # Test case 0
        (
            np.array([[0, 0], [0, 10], [5, 10], [10, 5], [10, 0]]),
            [np.array([0, 0]), np.array([0, 10]), np.array([10, 0])],
            np.zeros((1080, 1920, 3)),
            (np.array([10, 10]), "C"),
        )
    ],
)
def test_find_corner_missing(
    coordinates: np.ndarray, points_inside_rectangles: list, mask_screen: np.ndarray, expected_result: np.ndarray
):
    result = Coordinates.find_corner_missing(coordinates, points_inside_rectangles, mask_screen)
    assert np.array_equal(result[0], expected_result[0])
    assert result[1] == expected_result[1]


@pytest.mark.parametrize(
    "coordinates_ori, coordinates_validated, mask_screen, expected_result",
    [
        # Test case 0
        (
            np.array([[0, 0], [0, 10], [5, 10], [10, 5], [10, 0]]),
            [np.array([0, 0]), np.array([0, 10]), np.array([10, 0])],
            np.zeros((1080, 1920, 3)),
            ([np.array([0, 0]), np.array([0, 10]), np.array([10, 0]), np.array([10, 10])], "C"),
        ),
        # Test case 1
        (
            np.array([[69, 124], [72, 917], [1455, 905], [1385, 788], [1411, 568], [1487, 517], [1487, 130]]),
            [np.array([69, 124]), np.array([72, 917]), np.array([1487, 130])],
            np.zeros((1080, 1920, 3)),
            ([np.array([69, 124]), np.array([72, 917]), np.array([1487, 130]), np.array([1487, 905])], "C"),
        ),
    ],
)
def test_validate_coordinate_of_screen(
    coordinates_ori: np.ndarray, coordinates_validated: list, mask_screen: np.ndarray, expected_result: list[np.ndarray]
):
    Coordinates.coordinates_queue = []  # Reset queue
    result = Coordinates.validate_coordinate_of_screen(coordinates_ori, coordinates_validated, mask_screen)
    assert np.array_equal(result[0], expected_result[0])
    assert result[1] == expected_result[1]


@pytest.mark.parametrize(
    'coordinates, expected_result',
    [
        # Test case 0
        (np.array([[0, 0], [0, 10], [10, 10], [10, 0]]), 100.0),
        (np.array([[0, 10], [10, 10], [10, 0], [0, 0]]), 100.0),
        (np.array([[0, 10], [10, 10], [10, 0]]), 50.0),
        (np.array([[0, 10], [10, 10]]), 0.0),
        (np.array([[0, 10]]), 0.0),
        (np.array([[0, 0], [0, 10], [10, 10], [20, 0]]), 150.0),
    ],
)
def test_cal_area(coordinates: np.ndarray, expected_result: float):
    result = Coordinates.cal_area(coordinates)
    assert result == expected_result


@pytest.mark.parametrize(
    'frame, coordinates_ori, coordinates_validated, is_dev, expected_result',
    [
        # Test case 0
        (
            np.zeros((1080, 1920, 3)),
            np.array([[0, 0], [0, 10], [5, 10], [10, 5], [10, 0]]),
            [np.array([0, 0]), np.array([0, 10]), np.array([10, 0])],
            False,
            (np.array([[0, 0], [0, 10], [10, 10], [10, 0]]), "C"),
        ),
    ],
)
def test_get_coordinates(
    frame: np.ndarray,
    coordinates_ori: np.ndarray,
    coordinates_validated: list[np.ndarray],
    is_dev: bool,
    expected_result: np.ndarray,
):
    mask_screen = np.zeros_like(frame)
    result = Coordinates.get_coordinates(frame, mask_screen, coordinates_ori, coordinates_validated, is_dev)
    assert np.array_equal(result[0], expected_result[0])
    assert result[1] == expected_result[1]
