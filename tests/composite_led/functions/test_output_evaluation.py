from unittest.mock import MagicMock, patch

import cv2
import numpy as np
import pytest

from scripts.tools.compare_video_utils import (
    calculate_image_difference,
    check_similarity_multithreaded,
    compute_edge_discrepancy,
    compute_nrmse,
)


@pytest.mark.parametrize(
    "img1, img2",
    [
        (
            np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8),
            np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8),
        ),
        (
            np.random.randint(0, 256, size=(200, 200), dtype=np.uint8),
            np.random.randint(0, 256, size=(200, 200), dtype=np.uint8),
        ),
    ],
)
def test_compute_nrmse(img1, img2):
    result1 = compute_nrmse(img1, img2)
    result2 = compute_nrmse(img2, img2)
    assert 0 <= result1 <= 1, "NRMSE should be in the range [0, 1]."
    assert result2 == 0, "NRMSE should be 0 with similar inputs."


@pytest.mark.parametrize(
    "image1, image2",
    [
        (
            np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8),
            np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8),
        ),
        (
            np.random.randint(0, 256, size=(100, 100), dtype=np.uint8),
            np.random.randint(0, 256, size=(100, 100), dtype=np.uint8),
        ),
    ],
)
def test_compute_edge_discrepancy(image1, image2):
    result1 = compute_edge_discrepancy(image1, image2)
    result2 = compute_edge_discrepancy(image1, image1)
    assert 0 <= result1 <= 1, "Edge discrepancy should be in the range [0, 1]."
    assert result2 == 0, "Edge discrepancy with similar inputs should be 0."


@pytest.mark.parametrize(
    "image1, image2",
    [
        (
            np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8),
            np.random.randint(0, 256, size=(100, 100, 3), dtype=np.uint8),
        ),
        (
            np.random.randint(0, 256, size=(100, 100), dtype=np.uint8),
            np.random.randint(0, 256, size=(100, 100), dtype=np.uint8),
        ),
    ],
)
def test_calculate_image_difference(image1, image2):
    nrmse, ssim, gradient_diff = calculate_image_difference(image1, image2)
    assert 0 <= nrmse <= 1, "NRMSE should be in the range [0, 1]."
    assert -1 <= ssim <= 1, "SSIM should be in the range [-1, 1]"
    assert 0 <= gradient_diff <= 1, "Gradient difference should be in the range [0, 1]"


def generate_synthetic_video_frame(width=800, height=800):
    """
    Generate a random synthetic video frame.
    """
    return np.random.randint(0, 256, (height, width, 3), dtype=np.uint8)


def test_check_similarity_multithreaded():
    with patch("cv2.VideoCapture") as MockVideoCapture:
        mock_cap = MagicMock()
        MockVideoCapture.return_value = mock_cap

        # Mock `get` for frame count, width, and height
        total_frames = 10
        mock_cap.get.side_effect = lambda x: {
            cv2.CAP_PROP_FRAME_COUNT: total_frames,
            cv2.CAP_PROP_FRAME_WIDTH: 1920,
            cv2.CAP_PROP_FRAME_HEIGHT: 1080,
        }.get(x, None)

        # Mock `read` to generate synthetic frames
        def mock_read():
            if mock_read.call_count < total_frames:
                mock_read.call_count += 1
                return True, generate_synthetic_video_frame()
            else:
                return False, None  # End of video stream

        mock_read.call_count = 0
        mock_cap.read.side_effect = mock_read

        # Mock `isOpened` to return True
        mock_cap.isOpened.return_value = True

        # Call the function with mocked video paths
        _, nrmse_max, ssim_min, gradient_diff_max, _ = check_similarity_multithreaded(
            "mock_video1.mp4", "mock_video2.mp4", sampling_factor=2, num_threads=2
        )

        # Assert the results
        assert 0 <= nrmse_max <= 1, "NRMSE max should be non-negative"
        assert -1 <= ssim_min <= 1, "SSIM min should be in the range [0, 1]"
        assert gradient_diff_max >= 0, "Gradient difference max should be non-negative"


# Run the test
test_check_similarity_multithreaded()
