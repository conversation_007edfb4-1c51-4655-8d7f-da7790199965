import numpy as np
import pytest

from vsc.composite_led.functions.screen import Screen


@pytest.mark.parametrize(
    "coordinates, frame, expected_result",
    [
        # Test case 1: No hidden
        (np.array([[10, 10], [10, 10], [26, 19], [26, 19]]), np.zeros((1080, 1920, 3), dtype=np.uint8), (False, False)),
        # # Test case 1.1: No hidden - Error: Root case from Screen.is_hidden()
        # (
        #         np.array([[0, 10], [10, 20], [20, 20], [20, 10]]),
        #         np.zeros((1080, 1920, 3), dtype=np.uint8),
        #         (False, False)
        # ),
        # Test case 2: Left hidden
        (np.array([[0, 10], [0, 19], [10, 19], [10, 10]]), np.zeros((1080, 1920, 3), dtype=np.uint8), (True, False)),
        # Test case 3: Right hidden
        (np.array([[1910, 10], [1910, 19], [1919, 19], [1919, 10]]), np.zeros((1080, 1920, 3), dtype=np.uint8), (<PERSON>als<PERSON>, True)),
        # Add more test cases as needed
    ],
)
def test_is_hidden(coordinates: np.ndarray, frame: np.ndarray, expected_result: tuple):
    result = Screen.is_hidden(coordinates, frame)
    assert result == expected_result


@pytest.mark.parametrize(
    "coordinates, frame, expected_result",
    [
        # Test case 1: No hidden
        (
            np.array([[10, 10], [10, 10], [26, 19], [26, 19]]),
            np.zeros((1080, 1920, 3), dtype=np.uint8),
            None,
        ),
        # # Test case 1.1: No hidden - Error: Root case from Screen.is_hidden()
        # (
        #         np.array([[0, 10], [10, 20], [20, 20], [20, 10]]),
        #         np.zeros((1080, 1920, 3), dtype=np.uint8),
        #         np.array([[0, 10], [10, 20], [20, 20], [20, 10]]),
        # ),
        # Test case 2: Left hidden
        (
            np.array([[0, 10], [0, 19], [10, 19], [10, 10]]),
            np.zeros((1080, 1920, 3), dtype=np.uint8),
            "left",
        ),
        # Test case 3: Right hidden
        (
            np.array([[1910, 10], [1910, 19], [1919, 19], [1919, 10]]),
            np.zeros((1080, 1920, 3), dtype=np.uint8),
            "right",
        ),
        # Add more test cases as needed
    ],
)
def test_find_hidden_coordinates(coordinates: np.ndarray, frame: np.ndarray, expected_result: np.ndarray):
    result = Screen.find_hidden_name(coordinates, frame)

    assert np.array_equal(result, expected_result)
