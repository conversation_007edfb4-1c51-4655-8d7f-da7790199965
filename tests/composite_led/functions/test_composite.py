import numpy as np
import pytest

from vsc.composite_led.functions.composite.simple_composite import composite_image


@pytest.mark.parametrize(
    "foreground, background, mask, factor",
    [
        (
            [
                np.random.randint(0, 256, size=(256, 256, 1), dtype=np.uint8),
                np.random.randint(0, 256, size=(256, 256, 3), dtype=np.uint8),
            ],
            [
                np.random.randint(0, 256, size=(256, 256, 1), dtype=np.uint8),
                np.random.randint(0, 256, size=(256, 256, 3), dtype=np.uint8),
            ],
            [np.random.rand(256, 256, 3), np.random.rand(256, 256, 1), np.random.rand(256, 256)],
            [1, 255],
        ),
    ],
)
def test_composite_image(foreground, background, mask, factor):
    for f in factor:
        for fg, bg in zip(foreground, background):
            for m in mask:
                result = composite_image(fg, bg, m * f)
                assert result is not None
