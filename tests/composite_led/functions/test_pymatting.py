import numpy as np
import pytest

from vsc.composite_led.functions.composite.pymatting import (
    composite_with_pymatting,
    pre_processing_input,
)


@pytest.fixture
def example_input():
    fg = np.ones((100, 100, 3), dtype=np.uint8) * 255
    bg = np.zeros((100, 100, 3), dtype=np.uint8)
    mask = np.ones((100, 100), dtype=np.uint8) * 255
    return fg, bg, mask


def test_pre_processing_input(example_input):
    fg, bg, mask = example_input
    processed_fg, processed_bg, processed_mask = pre_processing_input(fg, bg, mask)
    assert isinstance(processed_fg, np.ndarray)
    assert isinstance(processed_bg, np.ndarray)
    assert isinstance(processed_mask, np.ndarray)
    assert processed_fg.shape == fg.shape
    assert processed_bg.shape == bg.shape
    assert processed_mask.shape == mask.shape
    assert processed_fg.max() <= 1.0
    assert processed_bg.max() <= 1.0
    assert processed_mask.max() <= 1.0


def test_composite_with_pymatting(example_input):
    fg, bg, mask = example_input
    comp = composite_with_pymatting(fg, bg, mask)
    assert isinstance(comp, np.ndarray)
    assert comp.dtype == np.uint8
    assert comp.shape == fg.shape
