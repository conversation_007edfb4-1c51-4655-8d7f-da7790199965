import math

import numpy as np
import pandas as pd
import pytest

from vsc.composite_led.functions.coordinates import PostProcessing


@pytest.mark.parametrize(
    "row, expected_result",
    [
        (pd.Series([[0, 0], [0, 5]]), 5),
        (pd.Series([[1, 2], [3, 4]]), math.sqrt(8)),
    ],
)
def test_calculate_distance(row, expected_result):
    result = PostProcessing.calculate_distance(row, 0, 1)
    assert result == expected_result


def test_read_csv():
    pass


@pytest.mark.parametrize(
    "coordinates, factor, expected_result",
    [
        (
            [np.array([100.0, 100.0]), np.array([100.0, 200.0]), np.array([200.0, 200.0]), np.array([200.0, 100.0])],
            5,
            [np.array([95, 95]), np.array([95, 205]), np.array([205, 205]), np.array([205, 95])],
        )
    ],
)
def test_expanding(coordinates, factor, expected_result):
    result = PostProcessing.expanding(coordinates, factor)
    assert np.array_equal(result, expected_result)


@pytest.mark.parametrize(
    "coordinates, factor, expected_result",
    [
        (
            [np.array([-10, -10]), np.array([-10, 10]), np.array([10, 10]), np.array([10, -10])],
            10,
            [np.array([0, 0]), np.array([0, 0]), np.array([0, 0]), np.array([0, 0])],
        ),
    ],
)
def test_shrinking(coordinates, factor, expected_result):
    result = PostProcessing.shrinking(coordinates, factor)
    assert np.array_equal(result, expected_result)


@pytest.fixture(scope="module")
def df_get_value_in_db():
    """Creates a DataFrame used in get_value_in_db test cases."""
    return pd.DataFrame(
        {
            "frame_id": [0, 1],
            "A": ["[69, 124]", "[69, 124]"],
            "B": ["[72, 917]", "[72, 917]"],
            "C": ["[1490, 923]", "[1490, 923]"],
            "D": ["[1487, 130]", "[1487, 130]"],
            "timi_on_screen": [False, True],
        }
    )


@pytest.mark.parametrize(
    "col, frame_id, expected_result",
    [
        (
            "timi_on_screen",
            0,
            False,
        ),
        (
            "A",
            0,
            "[69, 124]",
        ),
        (
            "coordinates",
            1,
            ['[69, 124]', '[72, 917]', '[1490, 923]', '[1487, 130]'],
        ),
    ],
)
def test_get_value_in_df(df_get_value_in_db: pd.DataFrame, col: str, frame_id: int, expected_result):
    result = PostProcessing.get_value_in_df(df_get_value_in_db, col, frame_id)
    assert result == expected_result


@pytest.mark.parametrize(
    "coordinates, frame_resolution, validation_threshold, expected_result",
    [
        # Test case 0: Rectangle
        (
            np.array([[0, 0], [0, 10], [10, 10], [10, 0]]),
            (1920, 1080),
            20,
            False,
        ),
        # Test case 1: Parallelogram
        (
            np.array([[0, 0], [5, 10], [15, 10], [10, 0]]),
            (2560, 1280),
            20,
            False,
        ),
        (
            np.array([[200, 200], [150, 200], [672, 183], [670, 205]]),
            (1920, 1080),
            20,
            True,
        ),
    ],
)
def test_validate_coordinates(coordinates, frame_resolution, validation_threshold, expected_result):
    result = PostProcessing.validate_coordinates(coordinates, frame_resolution, validation_threshold)
    assert result == expected_result


@pytest.fixture(scope="module")
def df_handle_unusual():
    """Creates a DataFrame used in handle_unusual test cases."""
    return pd.DataFrame(
        {
            "frame_id": [0, 1, 2],
            "A": [[69, 124], [0, 0], [69, 124]],
            "B": [[72, 917], [0, 0], [72, 917]],
            "C": [[1490, 923], [0, 0], [1490, 923]],
            "D": [[1487, 130], [0, 0], [1487, 130]],
            "timi_on_screen": [False, True, True],
        }
    )


@pytest.mark.parametrize(
    "coordinates, frame_id, expected_result",
    [
        # Test case 0: Has unusual
        (np.array([[0, 0], [0, 0], [0, 0], [0, 0]]), 1, np.array([[69, 124], [72, 917], [1490, 923], [1487, 130]])),
        # Test case 2: Unusual but frame_id invalid
        (
            np.array([[0, 0], [0, 0], [0, 0], [0, 0]]),
            0,
            np.array([[0, 0], [0, 0], [0, 0], [0, 0]]),
        ),
        # Test case 3: Hasn't unusual
        (
            np.array([[0, 0], [0, 0], [0, 0], [0, 1]]),
            1,
            np.array([[0, 0], [0, 0], [0, 0], [0, 1]]),
        ),
    ],
)
def test_handle_unusual_dummy(coordinates, df_handle_unusual, frame_id, expected_result):
    result = PostProcessing.handle_unusual_dummy(coordinates, df_handle_unusual, frame_id)
    assert np.array_equal(result, expected_result)


def test_smoothing_coordinates():
    pass


def test_stabilize_coordinates_interpolation():
    pass
