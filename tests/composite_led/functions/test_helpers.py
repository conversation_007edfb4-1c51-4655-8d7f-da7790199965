import os
import tempfile

import numpy as np
import pytest

from vsc.composite_led.functions import helpers
from vsc.utils import cfg


def test_create_dummy_value():
    dummy_value = 0
    result = helpers.create_dummy_value(dummy_value)
    assert isinstance(result, list)
    assert len(result) == 4
    assert [np.all(element == dummy_value) for element in result]


@pytest.mark.parametrize(
    "coordinates, dummy_value, expected_result",
    [
        ([[0, 0, 0], [0, 0, 0], [0, 0, 0]], 0, True),  # Test case 1: Dummy value is 0
        ([[1, 1, 1], [1, 1, 1], [1, 1, 1]], 1, True),  # Test case 2: Dummy value is 1
        ([[0, 0, 0], [0, 1, 0], [0, 0, 0]], 0, False),  # Test case 3: Non-dummy value
        ([], 0, True),  # Test case 4: Empty list
        ([[]], 0, True),  # Test case 5: Nested empty lists
        ([[0, 0], [0, 0], [0, 0]], 0, True),  # Test case 6: Nested non-empty lists
    ],
)
def test_is_dummy_value(coordinates, dummy_value, expected_result):
    assert helpers.is_dummy_value(coordinates, dummy_value) == expected_result


def test_save_coordinates_to_csv_success():
    save_path = tempfile.NamedTemporaryFile(suffix=".csv").name
    frame_id = 0
    coordinates = [np.array([1, 2]), np.array([3, 4]), np.array([5, 6]), np.array([7, 8])]
    coordinates_based_segment = np.array([[1, 2], [3, 4], [5, 6]])
    coordinates_based_segment_low_epsilon = np.array([[1, 2], [3, 4]])
    corner_missing = "C"
    hidden_name = "left"

    helpers.save_coordinates_to_csv(
        frame_id,
        coordinates,
        coordinates_based_segment,
        coordinates_based_segment_low_epsilon,
        corner_missing,
        hidden_name,
        save_path,
    )

    assert os.path.isfile(save_path)

    with open(save_path, "r") as f:
        lines = f.readlines()
        assert lines[0] == "frame_id,A,B,C,D,corner_missing,hidden_name,coordinates_low_epsilon,coordinates_based_segment\n"
        print(lines[1])
        assert (
            lines[1]
            == f'{frame_id},"[1, 2]","[3, 4]","[5, 6]","[7, 8]",{corner_missing},{hidden_name},"[[1, 2], [3, 4]]","[[1, 2], [3, 4], [5, 6]]"\n'
        )
    os.remove(save_path)


def test_clear_temp_file():
    with tempfile.NamedTemporaryFile(delete=False) as temp_file1:
        temp_file1_path = temp_file1.name
    with tempfile.NamedTemporaryFile(delete=False) as temp_file2:
        temp_file2_path = temp_file2.name
    with tempfile.NamedTemporaryFile(delete=False) as temp_file3:
        temp_file3_path = temp_file3.name

    assert os.path.exists(temp_file1_path)
    assert os.path.exists(temp_file2_path)
    assert os.path.exists(temp_file3_path)

    helpers.clear_temp_file(temp_file1_path, temp_file2_path, temp_file3_path)

    assert not os.path.exists(temp_file1_path)
    assert not os.path.exists(temp_file2_path)
    assert not os.path.exists(temp_file3_path)


@pytest.mark.parametrize(
    "coordinates, expected_result",
    [
        # Test case 1: Square coordinates
        (np.array([[0, 0], [0, 10], [10, 10], [10, 0]]), (10.0, 10.0)),
        # Test case 2: Parallelogram coordinates
        (np.array([[0, 0], [3, 5], [13, 5], [10, 0]]), (10.0, 5.0)),
        # Test case 3: Rectangle coordinates
        (np.array([[0, 0], [0, 8], [12, 8], [12, 0]]), (12.0, 8.0)),
        # Add more test cases as needed
    ],
)
def test_get_dimension(coordinates: np.ndarray, expected_result: tuple):
    result = helpers.get_dimension(coordinates)

    assert result == expected_result


# -------------------------
# Test cases using pytest
# -------------------------


def test_successful_initialisation(tmp_path):
    """
    Test init_model_with_lock
    Case 1: No status file exists and the init function succeeds.
    """
    model_folder = tmp_path / "model"
    model_folder.mkdir()

    def init_func():
        return {"model": "dummy"}

    result = helpers.init_model_with_lock(str(model_folder), init_func)
    assert result == {"model": "dummy"}

    status_file = model_folder / cfg.led.status_filename
    with open(status_file, "r") as f:
        content = f.read().strip()
    assert content == cfg.led.download_success_msg


def test_existing_success_status(tmp_path):
    """
    Test init_model_with_lock
    Case 2: A status file exists with the success message; the init function is still called.
    """
    model_folder = tmp_path / "model"
    model_folder.mkdir()
    status_file = model_folder / cfg.led.status_filename
    status_file.write_text(cfg.led.download_success_msg)

    def init_func():
        return {"model": "dummy2"}

    result = helpers.init_model_with_lock(str(model_folder), init_func)
    assert result == {"model": "dummy2"}

    with open(status_file, "r") as f:
        content = f.read().strip()
    assert content == cfg.led.download_success_msg


def test_existing_failure_status(tmp_path):
    """
    Test init_model_with_lock
    Case 3: A status file exists with a failure message (non-success), so the folder is cleaned
            before initialisation.
    """
    model_folder = tmp_path / "model"
    model_folder.mkdir()
    # Create an extra file that should be cleaned up.
    dummy_file = model_folder / "dummy.txt"
    dummy_file.write_text("dummy")

    status_file = model_folder / cfg.led.status_filename
    status_file.write_text("incomplete")  # Not the success message.

    def init_func():
        return {"model": "dummy3"}

    result = helpers.init_model_with_lock(str(model_folder), init_func)
    assert result == {"model": "dummy3"}

    # The dummy file should have been removed.
    assert not dummy_file.exists()

    with open(status_file, "r") as f:
        content = f.read().strip()
    assert content == cfg.led.download_success_msg
