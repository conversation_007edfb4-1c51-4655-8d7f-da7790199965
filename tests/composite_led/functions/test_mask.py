import numpy as np
import pytest

from vsc.composite_led.functions.mask import MaskScreen


@pytest.fixture(scope="session")
def image():
    return np.zeros((100, 100, 3), dtype=np.uint8)


def test_with_green_screen(image):
    image[20:80, 20:80] = (0, 255, 0)  # Green color

    mask, coordinates = MaskScreen.get_screen_mask_and_coordinates(image)

    assert mask.sum() > 0
    assert len(coordinates) > 0


def test_no_green_screen():
    pass


def test_with_different_color():
    pass


@pytest.mark.parametrize(
    "zero_mask, region_value, value, stretch, expected_result",
    [
        (np.zeros((100, 100, 3), dtype=np.uint8), (50, 50, 70, 70), (1, 1, 1), 0, (50, 50, 69, 69)),
        (np.zeros((100, 100, 3), dtype=np.uint8), (50, 50, 70, 70), (1, 1, 1), 10, (40, 40, 79, 79)),
        (np.zeros((100, 100, 3), dtype=np.uint8), (50, 50, 70, 70), (1, 1, 1), 40, (10, 10, 99, 99)),
        (np.zeros((100, 100, 3), dtype=np.uint8), (50, 50, 70, 70), (1, 1, 1), 60, (0, 0, 99, 99)),
        (np.zeros((100, 100, 3), dtype=np.uint8), (50, 50, 70, 70), (255, 255, 255), 60, (0, 0, 99, 99)),
        (np.zeros((100, 100, 1), dtype=np.uint8), (50, 50, 70, 70), (255), 60, (0, 0, 99, 99)),
        (np.zeros((100, 100), dtype=np.uint8), (50, 50, 70, 70), (255), 60, (0, 0, 99, 99)),
        (np.zeros((100, 100), dtype=np.uint8), (0, 200, 200, 200), (255), 60, (0, 0, 0, 0)),
        (np.zeros((100, 100, 3), dtype=np.uint8), (50, 50, 70, 70), (1, 1, 1), -5, (55, 55, 64, 64)),
    ],
)
def test_extract_region(zero_mask, region_value, value, stretch, expected_result):
    x1, y1, x2, y2 = region_value
    zero_mask[y1:y2, x1:x2] = value
    result = MaskScreen.extract_region(zero_mask, stretch)
    assert result == expected_result


@pytest.mark.parametrize(
    "mask, kernel_size, crop_mode, expected_result",
    [
        (np.zeros((100, 100), dtype=np.uint8), 5, False, (100, 100)),
        (np.zeros((100, 100), dtype=np.uint8), 5, True, (100, 100)),  # Empty cropped mask
    ],
)
def test_dilate(mask, kernel_size, crop_mode, expected_result):
    result = MaskScreen.dilate(mask, kernel_size, crop_mode)
    assert result.shape == expected_result


if __name__ == "__main__":
    i = np.zeros((100, 100, 3), dtype=np.uint8)
    i[50:70, 50:70] = (1, 2, 1)
    res = MaskScreen.dilate
