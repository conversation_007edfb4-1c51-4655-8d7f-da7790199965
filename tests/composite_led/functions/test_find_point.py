from typing import Union

import pytest

from vsc.composite_led.functions.coordinates import FindPoint


@pytest.mark.parametrize(
    "A, C, D, d, expected_result",
    [
        # Test case 0
        ([0, 0], [0, 0], [0, 5], 10, [0, 10]),
        ([0, 0], [0, 1], [0, 5], 10, [0, 10]),
        ([0, 0], [0, 0], [1, 1], 10, [7, 7]),
    ],
)
def test_based_on_length_and_direction(A: list, C: list, D: list, d: Union[float, int], expected_result: list):
    result = FindPoint.based_on_length_and_direction(A, C, D, d)
    assert result == expected_result
