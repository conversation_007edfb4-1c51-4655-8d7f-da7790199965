import cv2
import numpy as np
import pytest
import torch

import sys

sys.path.insert(0, "")
from vsc.composite_led.processor.stage3 import Stage3Processor
from vsc.composite_led.utilizer import VideoReaderProcessor, VideoWriterProcessor


@pytest.fixture
def default_device():
    return 'cuda:0' if torch.cuda.is_available() else 'cpu'


@pytest.fixture
def template_led_image():
    led_image = cv2.imread('data/test/daophuquy.jpg')
    return led_image


@pytest.fixture
def template_occluded_timi_matting():
    occluded_timi_matting = np.load('data/test/occluded_timi_matting.npy')
    return occluded_timi_matting


@pytest.fixture
def template_non_occluded_timi_matting():
    non_occluded_timi_matting = np.load('data/test/non_occluded_timi_matting.npy')
    return non_occluded_timi_matting


@pytest.fixture
def writer():
    return VideoWriterProcessor()


@pytest.fixture
def reader():
    return VideoReaderProcessor()


@pytest.fixture
def stage3(reader, writer, default_device):
    return Stage3Processor(reader, writer, default_device)
