import os
import tempfile

import pytest
from fvutils.media import (
    MergeTrimMode,
    concat_audios,
    get_media_duration,
    get_video_fps,
    get_video_properties,
    has_audio,
    merge_audio_and_video_ffmpeg_simple,
    merge_video_with_audio,
    split_audio_from_video,
    trim_video,
)


@pytest.fixture
def sample_data():
    properties = {
        "video": "data/test/video.mp4",
        "duration_video": 4.3,
        "fps": 30.0,
        "has_audio": True,
        "video_properties": {
            "index": 1,
            "codec_name": "h264",
            "codec_long_name": "H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10",
            "profile": "High",
            "codec_type": "video",
            "codec_tag_string": "avc1",
            "codec_tag": "0x31637661",
            "width": 1920,
            "height": 1080,
            "coded_width": 1920,
            "coded_height": 1080,
            "closed_captions": 0,
            "has_b_frames": 2,
            "sample_aspect_ratio": "1:1",
            "display_aspect_ratio": "16:9",
            "pix_fmt": "yuv420p",
            "level": 40,
            "chroma_location": "left",
            "refs": 1,
            "is_avc": "true",
            "nal_length_size": "4",
            "r_frame_rate": "30/1",
            "avg_frame_rate": "30/1",
            "time_base": "1/15360",
            "start_pts": 0,
            "start_time": "0.000000",
            "duration_ts": 66048,
            "duration": "4.300000",
            "bit_rate": "5016682",
            "bits_per_raw_sample": "8",
            "nb_frames": "129",
            "disposition": {
                "default": 1,
                "dub": 0,
                "original": 0,
                "comment": 0,
                "lyrics": 0,
                "karaoke": 0,
                "forced": 0,
                "hearing_impaired": 0,
                "visual_impaired": 0,
                "clean_effects": 0,
                "attached_pic": 0,
                "timed_thumbnails": 0,
            },
            "tags": {
                "language": "und",
                "handler_name": "VideoHandler",
                "vendor_id": "[0][0][0][0]",
                "encoder": "Lavc59.37.100 libx264",
            },
        },
        "audio": "data/test/audio.mov",
        "duration_audio": 4.312,
    }
    return properties


def test_get_media_duration(sample_data):
    # Test case 0: video
    assert sample_data["duration_video"] == get_media_duration(sample_data["video"])

    # Test case 1: audio
    assert sample_data["duration_audio"] == get_media_duration(sample_data["audio"])


def test_get_video_fps(sample_data):
    assert sample_data["fps"] == get_video_fps(sample_data["video"])


def test_get_video_properties(sample_data):
    assert sample_data["video_properties"] == get_video_properties(sample_data["video"])


def test_split_audio_from_video(sample_data):
    temp_output = tempfile.NamedTemporaryFile(suffix=".mov").name
    split_audio_from_video(sample_data["video"], temp_output)
    assert os.path.exists(temp_output)
    os.remove(temp_output)


def test_concat_audios(sample_data):
    temp_output = tempfile.NamedTemporaryFile(suffix=".mov").name
    concat_audios([sample_data["audio"], sample_data["audio"]], temp_output)
    assert os.path.exists(temp_output)
    os.remove(temp_output)


def test_trim_video(sample_data):
    temp_output = tempfile.NamedTemporaryFile(suffix=".mp4").name
    trim_video(sample_data["video"], temp_output, trim_duration=1)
    assert os.path.exists(temp_output)
    os.remove(temp_output)


def test_merge_video_with_audio(sample_data):
    temp_output = tempfile.NamedTemporaryFile(suffix=".mp4").name
    merge_video_with_audio(
        sample_data["video"],
        sample_data["audio"],
        temp_output,
        trim_mode=MergeTrimMode.NO_TRIM,
        check_duration=False,
        keep_video_audio=False,
    )
    assert os.path.exists(temp_output)
    os.remove(temp_output)


def test_has_audio(sample_data):
    assert sample_data["has_audio"] == has_audio(sample_data["video"])


def test_merge_audio_and_video_ffmpeg_simple(sample_data):
    temp_output = tempfile.NamedTemporaryFile(suffix=".mp4").name
    merge_audio_and_video_ffmpeg_simple(sample_data["video"], sample_data["audio"], temp_output)
    assert os.path.exists(temp_output)
    os.remove(temp_output)


if __name__ == "__main__":
    properties = {
        "video": "data/test/video.mp4",
        "duration_video": 4.3,
        "fps": 30.0,
        "has_audio": True,
        "video_properties": {
            'index': 1,
            'codec_name': 'h264',
            'codec_long_name': 'H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10',
            'profile': 'High',
            'codec_type': 'video',
            'codec_time_base': '1/60',
            'codec_tag_string': 'avc1',
            'codec_tag': '0x31637661',
            'width': 1920,
            'height': 1080,
            'coded_width': 1920,
            'coded_height': 1088,
            'has_b_frames': 2,
            'sample_aspect_ratio': '1:1',
            'display_aspect_ratio': '16:9',
            'pix_fmt': 'yuv420p',
            'level': 40,
            'chroma_location': 'left',
            'refs': 1,
            'is_avc': 'true',
            'nal_length_size': '4',
            'r_frame_rate': '30/1',
            'avg_frame_rate': '30/1',
            'time_base': '1/15360',
            'start_pts': 0,
            'start_time': '0.000000',
            'duration_ts': 66048,
            'duration': '4.300000',
            'bit_rate': '5016682',
            'bits_per_raw_sample': '8',
            'nb_frames': '129',
            'disposition': {
                'default': 1,
                'dub': 0,
                'original': 0,
                'comment': 0,
                'lyrics': 0,
                'karaoke': 0,
                'forced': 0,
                'hearing_impaired': 0,
                'visual_impaired': 0,
                'clean_effects': 0,
                'attached_pic': 0,
                'timed_thumbnails': 0,
            },
            'tags': {'language': 'und', 'handler_name': 'VideoHandler', 'encoder': 'Lavc59.37.100 libx264'},
        },
        "audio": "data/test/audio.mov",
        "duration_audio": 4.312,
    }
    print(get_video_properties(properties["video"]))
