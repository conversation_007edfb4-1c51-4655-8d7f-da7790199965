import os
import tempfile

import numpy as np
import pytest
from fvutils.videoio import FVideoCapture, FVideoWriter, PixelFormat


@pytest.fixture
def sample_data():
    properties = {
        "video": "data/test/video.mp4",
    }

    return properties


def test_video_capture(sample_data):
    video_capture = FVideoCapture(sample_data["video"], output_pixel_format=PixelFormat.BGR24)

    video_properties = video_capture.get_video_properties()
    assert isinstance(video_properties, dict)
    assert "width" in video_properties
    assert "height" in video_properties
    assert "bit_rate" in video_properties
    assert "r_frame_rate" in video_properties

    for ret, frame in video_capture.read():
        assert isinstance(frame, np.ndarray)
        assert frame.shape[0] == video_properties["height"]
        assert frame.shape[1] == video_properties["width"]
        assert frame.shape[2] == 3

    video_capture.release()


def test_video_writer(sample_data):
    sample_output_video_path = tempfile.NamedTemporaryFile(suffix=".mp4").name
    video_capture = FVideoCapture(sample_data["video"], output_pixel_format=PixelFormat.BGR24)
    video_properties = video_capture.get_video_properties()
    video_writer = FVideoWriter(
        output_path=sample_output_video_path,
        frame_size=(video_properties["width"], video_properties["height"]),
        frame_pixel_format=PixelFormat.BGR24,
        output_pixel_format=PixelFormat.YUV420P,
        fps=video_properties["r_frame_rate"],
        bitrate=video_properties["bit_rate"],
    )

    for ret, frame in video_capture.read():
        video_writer.write(frame)

    video_capture.release()
    video_writer.release()

    assert os.path.exists(sample_output_video_path)
    os.remove(sample_output_video_path)
