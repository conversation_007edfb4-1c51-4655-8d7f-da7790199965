import logging
import os
import unittest
from datetime import datetime

from vsc.utils.logger import Logger


class TestLogging(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.path_file_log = "test_log.log"
        cls.name_log = "Test Log"
        cls.level = logging.DEBUG

        now = datetime.now()
        year = now.strftime("%Y")
        month = now.strftime("%m")
        day = now.strftime("%d")
        cls.full_path_file_log = os.path.join("logs", year, month, day, cls.path_file_log)

    def test_order1_how_to_use(self):
        logger = Logger(name=self.name_log, path_file=self.path_file_log, level=self.level).logger
        logger.debug("DEBUG")
        logger.info("INFO")
        logger.warning("WARNING")
        logger.error("ERROR")
        logger.critical("CRITICAL")

    def test_order2_check_log_path(self):
        assert os.path.exists(self.full_path_file_log)

    def test_order3_check_log_content(self):
        with open(self.full_path_file_log, "r") as file:
            content = file.read()

        assert all(level in content for level in ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"])
        os.remove(self.full_path_file_log)


if __name__ == "__main__":
    unittest.main()
