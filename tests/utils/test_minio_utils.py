import os
import unittest

from fvutils.minio import download

from vsc.utils.config import cfg
from vsc.utils.minio_utils import upload


class TestMinIO(unittest.TestCase):
    @classmethod
    def setUpClass(cls):
        cls.file_upload = "requirements.txt"  # file_path in local
        cls.object_name = f"{cfg.minio.public_dir}/test/requirements.txt"  # file_path in MinIO
        cls.save_folder = "tests"  # file_folder when download (for test download)

        if cfg.minio.secure:
            minio_protocol = "https"
        else:
            minio_protocol = "http"
        cls.url_test = f"{minio_protocol}://{cfg.minio.minio_endpoint}/{cfg.minio.bucket_name}/{cfg.minio.public_dir}/test/requirements.txt"

    @staticmethod
    def compare_text_files(file1_path, file2_path):
        with open(file1_path, "r") as file1:
            content1 = file1.read()

        with open(file2_path, "r") as file2:
            content2 = file2.read()

        if content1 == content2:
            return True
        else:
            return False

    def test_order1_upload(self):
        url = upload(self.object_name, self.file_upload)
        assert url == self.url_test

    def test_order2_download(self):
        save_path = download(self.url_test, self.save_folder)
        assert TestMinIO.compare_text_files(self.file_upload, save_path)
        os.remove(save_path)


if __name__ == "__main__":
    unittest.main()
