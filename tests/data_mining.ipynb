{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "import seaborn as sns\n", "import os\n", "import math\n", "from collections import deque\n", "%matplotlib inline\n", "import ast\n", "\n", "sns.set_theme() # Apply the default theme\n", "sns.set(rc={'figure.figsize':(18, 7)})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Read and visualize data"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["csv_file = \"/home/<USER>/Coding/vsc/results/led/issue_21/template_collected_13032024/C29/C29_Shot1_6_LED_1.csv\"\n", "\n", "# Read the CSV file into a DataFrame\n", "df = pd.read_csv(csv_file)\n", "\n", "cols = [\"A\", \"B\", \"C\", \"D\"]\n", "\n", "# Convert string representation to list of coordinates\n", "for col in cols:\n", "    df[col] = df[col].apply(ast.literal_eval)\n", "\n", "# Sort the points A, B, C, D\n", "# df = df.apply(lambda x: sorted(x, key=lambda y: (y[0], y[1])))\n", "\n", "# Create a scatter plot for each point A, B, C, D\n", "for col in cols:\n", "    points = df[col].tolist()\n", "    x = [point[0] for point in points]\n", "    y = [point[1] for point in points]\n", "    plt.scatter(x, y, label=col)\n", "\n", "\n", "# Add title and labels\n", "plt.title('Data Points A, B, C, D Visualization')\n", "plt.xlabel('X-axis')\n", "plt.ylabel('Y-axis')\n", "plt.legend()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame_id</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "      <th>corner_missing</th>\n", "      <th>hidden_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>176</td>\n", "      <td>[216, 140]</td>\n", "      <td>[216, 883]</td>\n", "      <td>[1531, 875]</td>\n", "      <td>[1529, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>177</td>\n", "      <td>[238, 140]</td>\n", "      <td>[236, 881]</td>\n", "      <td>[1551, 875]</td>\n", "      <td>[1549, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>178</td>\n", "      <td>[258, 142]</td>\n", "      <td>[258, 881]</td>\n", "      <td>[1571, 873]</td>\n", "      <td>[1569, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>179</td>\n", "      <td>[280, 142]</td>\n", "      <td>[280, 881]</td>\n", "      <td>[1589, 873]</td>\n", "      <td>[1589, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>180</td>\n", "      <td>[300, 144]</td>\n", "      <td>[300, 881]</td>\n", "      <td>[1607, 873]</td>\n", "      <td>[1605, 162]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>181 rows × 7 columns</p>\n", "</div>"], "text/plain": ["     frame_id            A             B             C            D  \\\n", "0           0  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "1           1  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "2           2  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "3           3  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "4           4  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "..        ...          ...           ...           ...          ...   \n", "176       176   [216, 140]    [216, 883]   [1531, 875]  [1529, 164]   \n", "177       177   [238, 140]    [236, 881]   [1551, 875]  [1549, 164]   \n", "178       178   [258, 142]    [258, 881]   [1571, 873]  [1569, 164]   \n", "179       179   [280, 142]    [280, 881]   [1589, 873]  [1589, 164]   \n", "180       180   [300, 144]    [300, 881]   [1607, 873]  [1605, 162]   \n", "\n", "    corner_missing hidden_name  \n", "0              NaN         NaN  \n", "1              NaN         NaN  \n", "2              NaN         NaN  \n", "3              NaN         NaN  \n", "4              NaN         NaN  \n", "..             ...         ...  \n", "176            NaN         NaN  \n", "177            NaN         NaN  \n", "178            NaN         NaN  \n", "179            NaN         NaN  \n", "180            NaN         NaN  \n", "\n", "[181 rows x 7 columns]"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"data": {"text/plain": ["'\\nCase1: Apply ratio screen from frame before start hide.\\n- id_start > 0.\\n- frame before is not dummy data.\\n\\nCase2: Apply ratio screen after frame from hide to appear.\\n- id\\n'"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["\"\"\"\n", "Case1: Apply ratio screen from frame before start hide. is_hiding.\n", "- id_start > 0.\n", "- frame before is not dummy data.\n", "\n", "Case2: Apply ratio screen after frame from hide to appear. is_appearing\n", "- id_end < total_frame\n", "- frame before is dummy data.\n", "\"\"\"\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["def is_consecutive(frame_ids):\n", "    return all(x == y - 1 for x, y in zip(frame_ids[:-1], frame_ids[1:]))\n", "\n", "def is_consecutive_hidden(df):\n", "    hidden_left_values = df[\"frame_id\"]\n", "    if hidden_left_values.empty:\n", "        return False\n", "    frame_ids = hidden_left_values.sort_values().tolist()\n", "    return is_consecutive(frame_ids)\n", "\n", "def is_dummy_value(coordinates: list, dummy_value: int = 0) -> bool:\n", "    return all(all(value == dummy_value for value in sublist) for sublist in coordinates)\n", "\n", "\n", "def get_dimension(coordinates: np.n<PERSON>ray):\n", "    \"\"\"TODO:\n", "    - Assert coordinates was sorted (counter-clockwise direction).\n", "    \"\"\"\n", "    width_screen = (coordinates[3][0] - coordinates[0][0] + coordinates[2][0] - coordinates[1][0]) / 2\n", "    height_screen = (coordinates[1][1] - coordinates[0][1] + coordinates[2][1] - coordinates[3][1]) / 2\n", "    return width_screen, height_screen\n", "\n", "def get_ratio_for_hiding(df, df_hidden) -> bool:\n", "    \"\"\"\n", "        - id_start > 0.\n", "        - is_dummy_data(coordinates[frame_id = id_start] - 1) --> False.\n", "    \"\"\"\n", "    frame_id_start = df_hidden.iloc[0][\"frame_id\"]\n", "\n", "    if frame_id_start == 0:\n", "        return None\n", "\n", "    coordinates_frame_before_hide = df[df[\"frame_id\"] == frame_id_start - 1][[\"A\", \"B\", \"C\", \"D\"]].to_numpy().tolist()[0]\n", "\n", "    if is_dummy_value(coordinates_frame_before_hide):\n", "        return None\n", "\n", "    width_screen, height_screen = get_dimension(coordinates_frame_before_hide)\n", "\n", "    return width_screen / height_screen\n", "\n", "\n", "def get_ratio_for_appearing(df, df_hidden) -> bool:\n", "    \"\"\"\n", "        - id_stop < total_frame\n", "        - is_dummy_data(coordinates[frame_id = id_end] + 1) --> False.\n", "\n", "    \"\"\"\n", "    frame_id_end = df_hidden.iloc[-1][\"frame_id\"]\n", "    if frame_id_end == len(df):\n", "        return None\n", "\n", "    coordinates_frame_after_hide = df[df[\"frame_id\"] == frame_id_end + 1][[\"A\", \"B\", \"C\", \"D\"]].to_numpy().tolist()[0]\n", "\n", "    if is_dummy_value(coordinates_frame_after_hide):\n", "        return None\n", "\n", "    width_screen, height_screen = get_dimension(coordinates_frame_after_hide)\n", "\n", "    return width_screen / height_screen\n", "\n", "def based_on_length_and_direction(A: list, C: list, D: list, d: float) -> list:\n", "    CD = (D[0] - C[0], D[1] - C[1])  # CD = (x_d - x_c, y_d - y_c)\n", "    length_CD = math.sqrt(CD[0] ** 2 + CD[1] ** 2)  # ||CD|| = sqrt((x_d - x_c)^2 + (y_d - y_c)^2)\n", "    v = (CD[0] / length_CD, CD[1] / length_CD)  # v = CD / ||CD||\n", "    AB = (d * v[0], d * v[1])  # AB = d * v\n", "    B = [int(A[0] + AB[0]), int(A[1] + AB[1])]  # B(x_c + AB_x, y_c + AB_y)\n", "    return B\n", "\n", "def get_ratio(df, df_hidden):\n", "    ratio = get_ratio_for_appearing(df, df_hidden)\n", "    if ratio is None:\n", "        ratio = get_ratio_for_hiding(df, df_hidden)\n", "    return ratio"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Is consecutive: True\n", "1.7037037037037037\n", "1.8278688524590163\n"]}], "source": ["# <PERSON><PERSON> dụng hàm\n", "df_hidden_left = df[df[\"hidden_name\"] == \"left\"]\n", "df_hidden_right = df[df[\"hidden_name\"] == \"right\"]\n", "result = is_consecutive_hidden(df_hidden_left)\n", "print(\"Is consecutive:\", result)\n", "ratio_hidden_right = get_ratio(df, df_hidden_right)\n", "print(ratio_hidden_right)\n", "\n", "ratio_hidden_left = get_ratio(df, df_hidden_left)\n", "print(ratio_hidden_left)\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["df.loc[df[\"hidden_name\"] == \"left\", \"ratio_hidden\"] = ratio_hidden_left\n", "df.loc[df[\"hidden_name\"] == \"right\", \"ratio_hidden\"] = ratio_hidden_right\n"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["frame_id                    80\n", "A                  [2398, 558]\n", "B                 [2395, 1039]\n", "C                 [3250, 1049]\n", "D                  [3254, 558]\n", "corner_missing             <PERSON><PERSON>\n", "hidden_name              right\n", "ratio_hidden          1.703704\n", "Name: 80, dtype: object"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.<PERSON><PERSON>[80]"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["df_copy = df.copy()"]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [], "source": ["\n", "def recalculate_hidden_coordinates(row):\n", "    columns = [\"A\", \"B\", \"C\", \"D\"]\n", "    coordinates = np.array([row[corner] for corner in columns])\n", "    ratio = row[\"ratio_hidden\"]\n", "    hidden_name = row[\"hidden_name\"]\n", "    \n", "    if row[\"frame_id\"] == 80:\n", "        print(coordinates)\n", "\n", "    if hidden_name == \"left\":\n", "        coordinates[0][0] = 0\n", "        coordinates[1][0] = 0\n", "\n", "        width_screen, height_screen = get_dimension(coordinates)\n", "\n", "        width_actual = int(ratio * height_screen)\n", "\n", "        coordinates[0] = based_on_length_and_direction(\n", "            coordinates[3], coordinates[3], coordinates[0], width_actual\n", "        )\n", "        coordinates[1] = based_on_length_and_direction(\n", "            coordinates[2], coordinates[2], coordinates[1], width_actual\n", "        )\n", "    if hidden_name == \"right\":\n", "        coordinates[2][0] = 2560 - 1\n", "        coordinates[3][0] = 2560 - 1\n", "\n", "        width_screen, height_screen = get_dimension(coordinates)\n", "\n", "        width_actual = int(ratio * height_screen)\n", "\n", "        coordinates[2] = based_on_length_and_direction(\n", "            coordinates[1], coordinates[1], coordinates[2], width_actual\n", "        )\n", "        coordinates[3] = based_on_length_and_direction(\n", "            coordinates[0], coordinates[0], coordinates[3], width_actual\n", "        )\n", "    return coordinates\n"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[[2398  558]\n", " [2395 1039]\n", " [3250 1049]\n", " [3254  558]]\n"]}], "source": ["df[\"coordinates_hidden\"] = df.apply(recalculate_hidden_coordinates, axis=1)"]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [], "source": ["df_copy = df.copy()"]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [], "source": ["def replace_hidden_value(row):\n", "    if row['hidden_name'] == 'left':\n", "        row[\"A\"] = row[\"coordinates_hidden\"][0]\n", "        row[\"B\"] = row[\"coordinates_hidden\"][1]\n", "    if row['hidden_name'] == 'right':\n", "        row[\"C\"] = row[\"coordinates_hidden\"][2]\n", "        row[\"D\"] = row[\"coordinates_hidden\"][3]\n", "    return row\n", "\n", "# Áp dụng hàm replace_A_with_zero cho mỗi hàng của DataFrame\n", "df = df.apply(replace_hidden_value, axis=1)"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame_id</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "      <th>corner_missing</th>\n", "      <th>hidden_name</th>\n", "      <th>ratio_hidden</th>\n", "      <th>coordinates_hidden</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[1342, 580], [1340, 1019], [2046, 1007], [204...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[1342, 580], [1340, 1019], [2046, 1007], [204...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[1342, 580], [1340, 1019], [2046, 1007], [204...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[1342, 580], [1340, 1019], [2046, 1007], [204...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[1342, 580], [1340, 1019], [2046, 1007], [204...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>176</td>\n", "      <td>[216, 140]</td>\n", "      <td>[216, 883]</td>\n", "      <td>[1531, 875]</td>\n", "      <td>[1529, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[216, 140], [216, 883], [1531, 875], [1529, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>177</td>\n", "      <td>[238, 140]</td>\n", "      <td>[236, 881]</td>\n", "      <td>[1551, 875]</td>\n", "      <td>[1549, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[238, 140], [236, 881], [1551, 875], [1549, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>178</td>\n", "      <td>[258, 142]</td>\n", "      <td>[258, 881]</td>\n", "      <td>[1571, 873]</td>\n", "      <td>[1569, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[258, 142], [258, 881], [1571, 873], [1569, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>179</td>\n", "      <td>[280, 142]</td>\n", "      <td>[280, 881]</td>\n", "      <td>[1589, 873]</td>\n", "      <td>[1589, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[280, 142], [280, 881], [1589, 873], [1589, 1...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>180</td>\n", "      <td>[300, 144]</td>\n", "      <td>[300, 881]</td>\n", "      <td>[1607, 873]</td>\n", "      <td>[1605, 162]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>[[300, 144], [300, 881], [1607, 873], [1605, 1...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>181 rows × 9 columns</p>\n", "</div>"], "text/plain": ["     frame_id            A             B             C            D  \\\n", "0           0  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "1           1  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "2           2  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "3           3  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "4           4  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "..        ...          ...           ...           ...          ...   \n", "176       176   [216, 140]    [216, 883]   [1531, 875]  [1529, 164]   \n", "177       177   [238, 140]    [236, 881]   [1551, 875]  [1549, 164]   \n", "178       178   [258, 142]    [258, 881]   [1571, 873]  [1569, 164]   \n", "179       179   [280, 142]    [280, 881]   [1589, 873]  [1589, 164]   \n", "180       180   [300, 144]    [300, 881]   [1607, 873]  [1605, 162]   \n", "\n", "    corner_missing hidden_name  ratio_hidden  \\\n", "0              NaN         NaN           NaN   \n", "1              NaN         NaN           NaN   \n", "2              NaN         NaN           NaN   \n", "3              NaN         NaN           NaN   \n", "4              NaN         NaN           NaN   \n", "..             ...         ...           ...   \n", "176            NaN         NaN           NaN   \n", "177            NaN         NaN           NaN   \n", "178            NaN         NaN           NaN   \n", "179            NaN         NaN           NaN   \n", "180            NaN         NaN           NaN   \n", "\n", "                                    coordinates_hidden  \n", "0    [[1342, 580], [1340, 1019], [2046, 1007], [204...  \n", "1    [[1342, 580], [1340, 1019], [2046, 1007], [204...  \n", "2    [[1342, 580], [1340, 1019], [2046, 1007], [204...  \n", "3    [[1342, 580], [1340, 1019], [2046, 1007], [204...  \n", "4    [[1342, 580], [1340, 1019], [2046, 1007], [204...  \n", "..                                                 ...  \n", "176  [[216, 140], [216, 883], [1531, 875], [1529, 1...  \n", "177  [[238, 140], [236, 881], [1551, 875], [1549, 1...  \n", "178  [[258, 142], [258, 881], [1571, 873], [1569, 1...  \n", "179  [[280, 142], [280, 881], [1589, 873], [1589, 1...  \n", "180  [[300, 144], [300, 881], [1607, 873], [1605, 1...  \n", "\n", "[181 rows x 9 columns]"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [{"data": {"text/plain": ["frame_id                                                             80\n", "A                                                           [2398, 558]\n", "B                                                          [2395, 1039]\n", "C                                                          [3250, 1049]\n", "D                                                           [3254, 558]\n", "corner_missing                                                      <PERSON><PERSON>\n", "hidden_name                                                       right\n", "ratio_hidden                                                   1.703704\n", "coordinates_hidden    [[2398, 558], [2395, 1039], [3221, 1089], [322...\n", "Name: 80, dtype: object"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["df_copy.iloc[80]"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [{"data": {"text/plain": ["frame_id                                                             80\n", "A                                                           [2398, 558]\n", "B                                                          [2395, 1039]\n", "C                                                          [3221, 1089]\n", "D                                                           [3226, 558]\n", "corner_missing                                                      <PERSON><PERSON>\n", "hidden_name                                                       right\n", "ratio_hidden                                                   1.703704\n", "coordinates_hidden    [[2398, 558], [2395, 1039], [3221, 1089], [322...\n", "Name: 80, dtype: object"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["df.<PERSON><PERSON>[80]"]}, {"cell_type": "code", "execution_count": 64, "metadata": {}, "outputs": [], "source": ["df.to_csv(\"recalculate_hidden.csv\", index=False)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["a = np.array([[1 , 1], [2, 2]])\n", "a[0] = [0, 0]"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["array([[0, 0],\n", "       [2, 2]])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": 70, "metadata": {}, "outputs": [{"ename": "KeyError", "evalue": "\"['coordinates_hiddens'] not found in axis\"", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                  <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[70], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m df_t \u001b[38;5;241m=\u001b[39m \u001b[43mdf\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdrop\u001b[49m\u001b[43m(\u001b[49m\u001b[43m[\u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mratio_hidden\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mcoordinates_hiddens\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;241;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/frame.py:5258\u001b[0m, in \u001b[0;36mDataFrame.drop\u001b[0;34m(self, labels, axis, index, columns, level, inplace, errors)\u001b[0m\n\u001b[1;32m   5110\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdrop\u001b[39m(\n\u001b[1;32m   5111\u001b[0m     \u001b[38;5;28mself\u001b[39m,\n\u001b[1;32m   5112\u001b[0m     labels: IndexLabel \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   5119\u001b[0m     errors: IgnoreRaise \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mraise\u001b[39m\u001b[38;5;124m\"\u001b[39m,\n\u001b[1;32m   5120\u001b[0m ) \u001b[38;5;241m-\u001b[39m\u001b[38;5;241m>\u001b[39m DataFrame \u001b[38;5;241m|\u001b[39m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m   5121\u001b[0m \u001b[38;5;250m    \u001b[39m\u001b[38;5;124;03m\"\"\"\u001b[39;00m\n\u001b[1;32m   5122\u001b[0m \u001b[38;5;124;03m    Drop specified labels from rows or columns.\u001b[39;00m\n\u001b[1;32m   5123\u001b[0m \n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   5256\u001b[0m \u001b[38;5;124;03m            weight  1.0     0.8\u001b[39;00m\n\u001b[1;32m   5257\u001b[0m \u001b[38;5;124;03m    \"\"\"\u001b[39;00m\n\u001b[0;32m-> 5258\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdrop\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   5259\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlabels\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5260\u001b[0m \u001b[43m        \u001b[49m\u001b[43maxis\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5261\u001b[0m \u001b[43m        \u001b[49m\u001b[43mindex\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mindex\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5262\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcolumns\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcolumns\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5263\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlevel\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5264\u001b[0m \u001b[43m        \u001b[49m\u001b[43minplace\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43minplace\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5265\u001b[0m \u001b[43m        \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   5266\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/generic.py:4549\u001b[0m, in \u001b[0;36mNDFrame.drop\u001b[0;34m(self, labels, axis, index, columns, level, inplace, errors)\u001b[0m\n\u001b[1;32m   4547\u001b[0m \u001b[38;5;28;01mfor\u001b[39;00m axis, labels \u001b[38;5;129;01min\u001b[39;00m axes\u001b[38;5;241m.\u001b[39mitems():\n\u001b[1;32m   4548\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m labels \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mN<PERSON>\u001b[39;00m:\n\u001b[0;32m-> 4549\u001b[0m         obj \u001b[38;5;241m=\u001b[39m \u001b[43mobj\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_drop_axis\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43maxis\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mlevel\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlevel\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   4551\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m inplace:\n\u001b[1;32m   4552\u001b[0m     \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_update_inplace(obj)\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/generic.py:4591\u001b[0m, in \u001b[0;36mNDFrame._drop_axis\u001b[0;34m(self, labels, axis, level, errors, only_slice)\u001b[0m\n\u001b[1;32m   4589\u001b[0m         new_axis \u001b[38;5;241m=\u001b[39m axis\u001b[38;5;241m.\u001b[39mdrop(labels, level\u001b[38;5;241m=\u001b[39mlevel, errors\u001b[38;5;241m=\u001b[39merrors)\n\u001b[1;32m   4590\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 4591\u001b[0m         new_axis \u001b[38;5;241m=\u001b[39m \u001b[43maxis\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mdrop\u001b[49m\u001b[43m(\u001b[49m\u001b[43mlabels\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43merrors\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43merrors\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   4592\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m axis\u001b[38;5;241m.\u001b[39mget_indexer(new_axis)\n\u001b[1;32m   4594\u001b[0m \u001b[38;5;66;03m# Case for non-unique axis\u001b[39;00m\n\u001b[1;32m   4595\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n", "File \u001b[0;32m~/.local/lib/python3.10/site-packages/pandas/core/indexes/base.py:6699\u001b[0m, in \u001b[0;36mIndex.drop\u001b[0;34m(self, labels, errors)\u001b[0m\n\u001b[1;32m   6697\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m mask\u001b[38;5;241m.\u001b[39many():\n\u001b[1;32m   6698\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m errors \u001b[38;5;241m!=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mignore\u001b[39m\u001b[38;5;124m\"\u001b[39m:\n\u001b[0;32m-> 6699\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01m<PERSON>eyError\u001b[39;00m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlist\u001b[39m(labels[mask])\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m not found in axis\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   6700\u001b[0m     indexer \u001b[38;5;241m=\u001b[39m indexer[\u001b[38;5;241m~\u001b[39mmask]\n\u001b[1;32m   6701\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mdelete(indexer)\n", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON>r\u001b[0m: \"['coordinates_hiddens'] not found in axis\""]}], "source": ["df_t = df.drop([\"ratio_hidden\", \"coordinates_hiddens\"], axis=1)"]}, {"cell_type": "code", "execution_count": 68, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>frame_id</th>\n", "      <th>A</th>\n", "      <th>B</th>\n", "      <th>C</th>\n", "      <th>D</th>\n", "      <th>corner_missing</th>\n", "      <th>hidden_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>3</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4</td>\n", "      <td>[1342, 580]</td>\n", "      <td>[1340, 1019]</td>\n", "      <td>[2046, 1007]</td>\n", "      <td>[2048, 593]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>176</th>\n", "      <td>176</td>\n", "      <td>[216, 140]</td>\n", "      <td>[216, 883]</td>\n", "      <td>[1531, 875]</td>\n", "      <td>[1529, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>177</th>\n", "      <td>177</td>\n", "      <td>[238, 140]</td>\n", "      <td>[236, 881]</td>\n", "      <td>[1551, 875]</td>\n", "      <td>[1549, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>178</th>\n", "      <td>178</td>\n", "      <td>[258, 142]</td>\n", "      <td>[258, 881]</td>\n", "      <td>[1571, 873]</td>\n", "      <td>[1569, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>179</th>\n", "      <td>179</td>\n", "      <td>[280, 142]</td>\n", "      <td>[280, 881]</td>\n", "      <td>[1589, 873]</td>\n", "      <td>[1589, 164]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>180</th>\n", "      <td>180</td>\n", "      <td>[300, 144]</td>\n", "      <td>[300, 881]</td>\n", "      <td>[1607, 873]</td>\n", "      <td>[1605, 162]</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>181 rows × 7 columns</p>\n", "</div>"], "text/plain": ["     frame_id            A             B             C            D  \\\n", "0           0  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "1           1  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "2           2  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "3           3  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "4           4  [1342, 580]  [1340, 1019]  [2046, 1007]  [2048, 593]   \n", "..        ...          ...           ...           ...          ...   \n", "176       176   [216, 140]    [216, 883]   [1531, 875]  [1529, 164]   \n", "177       177   [238, 140]    [236, 881]   [1551, 875]  [1549, 164]   \n", "178       178   [258, 142]    [258, 881]   [1571, 873]  [1569, 164]   \n", "179       179   [280, 142]    [280, 881]   [1589, 873]  [1589, 164]   \n", "180       180   [300, 144]    [300, 881]   [1607, 873]  [1605, 162]   \n", "\n", "    corner_missing hidden_name  \n", "0              NaN         NaN  \n", "1              NaN         NaN  \n", "2              NaN         NaN  \n", "3              NaN         NaN  \n", "4              NaN         NaN  \n", "..             ...         ...  \n", "176            NaN         NaN  \n", "177            NaN         NaN  \n", "178            NaN         NaN  \n", "179            NaN         NaN  \n", "180            NaN         NaN  \n", "\n", "[181 rows x 7 columns]"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["df_t"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "VideoScreenComposition", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}