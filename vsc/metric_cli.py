import argparse
import os
import sys

import pandas as pd


sys.path.insert(0, "")
from scripts.tools.compare_video_utils import check_similarity_multithreaded


def get_all_file_paths(root_folder):
    file_paths = []
    for dirpath, _, filenames in os.walk(root_folder):
        for filename in filenames:
            full_path = str(os.path.join(dirpath, filename))
            relative_path = os.path.relpath(full_path, root_folder)
            file_paths.append(relative_path)
    return file_paths


def parse_args():
    parser = argparse.ArgumentParser(description='Compare two videos by sampling frames and calculating differences.')
    parser.add_argument('vid1_dir', type=str, help='Path to the first video root folder.')
    parser.add_argument('vid2_dir', type=str, help='Path to the second video root folder.')
    parser.add_argument('--output_benchmark_path', type=str, required=True, help='Output file name.')
    parser.add_argument('--sampling_factor', type=int, default=7, help='Sampling factor for frames.')

    return parser.parse_args()


def main():
    args = parse_args()
    path_list = get_all_file_paths(args.vid1_dir)
    os.makedirs(os.path.dirname(args.output_benchmark_path), exist_ok=True)
    result_df = pd.DataFrame(
        columns=[
            "Name",
            "Min_NMRSE",
            "Ave_NMRSE",
            "Max_NMRSE",
            "Min_SSIM",
            "Ave_SSIM",
            "Max_SSIM",
            "Min_Grad_Diff",
            "Ave_Grad_Diff",
            "Max_Grad_Diff",
        ]
    )

    for path in path_list:
        path1 = str(os.path.join(args.vid1_dir, path))
        path2 = str(os.path.join(args.vid2_dir, path))

        # for legacy output
        if not os.path.exists(path1):
            path1 = os.path.dirname(path1) + "/processed_" + os.path.basename(path1)
            assert os.path.exists(path1), f"File {path1} does not exist."
        if not os.path.exists(path2):
            path2 = os.path.dirname(path2) + "/processed_" + os.path.basename(path2)
            assert os.path.exists(path2), f"File {path2} does not exist."

        results, nmrse_max, ssim_min, gradient_diff_max, peculiar_frame_ids = check_similarity_multithreaded(
            path1, path2, args.sampling_factor
        )

        result_df.loc[len(result_df.index)] = [
            path,
            min([r["nrmse"] for r in results], default=0),
            sum([r["nrmse"] for r in results]) / len(results) if results else 0,
            max([r["nrmse"] for r in results], default=0),
            min([r["ssim"] for r in results], default=1),
            sum([r["ssim"] for r in results]) / len(results) if results else 0,
            max([r["ssim"] for r in results], default=1),
            min([r["gradient_difference"] for r in results], default=0),
            sum([r["gradient_difference"] for r in results]) / len(results) if results else 0,
            max([r["gradient_difference"] for r in results], default=0),
        ]

    result_df.to_csv(args.output_benchmark_path, index=False)


if __name__ == "__main__":
    main()
