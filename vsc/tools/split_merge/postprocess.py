import os
import shutil

import cv2
from fvutils import media
from fvutils.videoio import FVideoCapture, PixelFormat

from vsc.composite_led.constants import SAVE_PATH_SUFFIXES
from vsc.composite_led.functions.helpers import clear_temp_file
from vsc.utils.logger import logger


class VSC_Postprocess:
    @staticmethod
    def merge_videos(segment_paths: list[str], overlap_frames: int, output_path: str) -> None:
        # Get video properties from the first segment

        os.makedirs(os.path.split(output_path)[0], exist_ok=True)

        first_cap = FVideoCapture(video_path=segment_paths[0], output_pixel_format=PixelFormat.BGR24)
        properties = first_cap.get_video_properties()
        width, height = int(properties["width"]), int(properties["height"])
        fps = eval(properties["avg_frame_rate"])

        # OpenCV VideoWriter for output
        out = cv2.VideoWriter(output_path, cv2.VideoWriter_fourcc(*"MP4V"), fps, (width, height))

        # Write the first segment completely
        for ret, frame in first_cap.read():
            if not ret:
                break
            out.write(frame)

        first_cap.release()
        # Write subsequent segments, skipping overlap frames
        for segment_path in segment_paths[1:]:
            cap = FVideoCapture(video_path=segment_path, output_pixel_format=PixelFormat.BGR24)
            frame_count = 0

            for ret, frame in cap.read():
                if not ret:
                    break
                if frame_count >= overlap_frames:
                    out.write(frame)
                frame_count += 1
            cap.release()
        out.release()

    @staticmethod
    def cleanup_intermediates(processed_segments: list) -> list:
        """
        Collect intermediate results from processed segments with suffixes defined in constants.py,
        e.g. A.csv, A_mask.avi, A_final.wav.

        This function iterates over the list of processed segment file paths and collects the paths
        for both the original processed segments and their associated intermediate files. For each
        processed segment, it computes the base filename (without extension) and appends additional
        filenames by adding each suffix specified in SAVE_PATH_SUFFIXES.

        Args:
            processed_segments (list): A list of file paths (strings) representing processed video segments.

        Returns:
            list: A list of file paths (strings) including the original processed segments and their
                corresponding intermediate files, which are marked for deletion.
        """
        deleted_files = []
        for processed_segment in processed_segments:
            deleted_files.append(processed_segment)
            base, _ = os.path.splitext(processed_segment)
            for suffix in SAVE_PATH_SUFFIXES:
                deleted_files.append(f"{base}{suffix}")
        return deleted_files

    @staticmethod
    def cleanup_segments(video_led_pairs: list[tuple], video_path: str, led_path: str) -> list:
        """
        Collect segment files from video_led_pairs excluding the original video and LED files.

        This function iterates over the list of tuples in video_led_pairs, where each tuple contains
        two file paths: one for a video segment and one for a corresponding LED file. It filters out
        the original video_path and led_path so that only segments trimmed from the original video_path
        are collected for deletion.

        Args:
            video_led_pairs (list[tuple]): A list of tuples, each containing a pair of file paths
                                        (video segment file, LED file).
            video_path (str): The file path of the original video that should be preserved.
            led_path (str): The file path of the original LED file that should be preserved.

        Returns:
            list: A list of file paths (strings) from video_led_pairs that are not the original video or LED files,
                representing additional segments marked for deletion.
        """
        deleted_files = []
        for segment_file, led_file in video_led_pairs:
            if segment_file != video_path:
                deleted_files.append(segment_file)
            if led_file != led_path:
                deleted_files.append(led_file)
        return deleted_files

    @staticmethod
    def run(
        video_path: str,
        led_path: str,
        processed_segments: list,
        video_led_pairs: list[tuple],
        num_overlap_frames: int,
        final_output_path: str,
        cleanup_mode: str = "preserve",
    ) -> None:
        if len(processed_segments) == 0:
            logger.debug(f"Processed_segments list is empty: {processed_segments}. VSC.Postprocess will not proceed.")
            return

        if len(processed_segments) == 1:
            logger.debug(
                "Processed_segments list contains only 1 sample (Full-sequence mode). Therefore, VSC.PostProcess will only move the segment to the final output path!"
            )
            os.makedirs(os.path.split(final_output_path)[0], exist_ok=True)
            shutil.move(processed_segments[0], final_output_path)
            return

        logger.debug(f"Processed_segments has {len(processed_segments)} segments. VSC.Postprocess is concatenating ...")
        VSC_Postprocess.merge_videos(processed_segments, num_overlap_frames, final_output_path)
        final_output_with_audio = final_output_path.replace(".mp4", "_has_audio.mp4")

        media.merge_video_with_audio(
            video_path=final_output_path,
            audio_path=video_path,
            output_path=final_output_with_audio,
            check_duration=False,
            keep_video_audio=True,
            trim_mode=media.MergeTrimMode.VIDEO_LENGTH,
        )

        os.replace(final_output_with_audio, final_output_path)

        if media.has_audio(led_path):
            media.merge_video_with_audio(
                video_path=final_output_path,
                audio_path=led_path,
                output_path=final_output_with_audio,
                check_duration=False,
                keep_video_audio=True,
                trim_mode=media.MergeTrimMode.VIDEO_LENGTH,
            )
            os.replace(final_output_with_audio, final_output_path)

        logger.debug(
            f"VSC.Postprocess has sucessfully merged audio from {video_path} and {led_path} into the {final_output_path}."
        )

        deleted_files = []
        if cleanup_mode in ("all", "intermediates"):
            logger.debug(f"Cleanup mode: {cleanup_mode}. Deleting intermediate processed segments ...")
            deleted_files.extend(VSC_Postprocess.cleanup_intermediates(processed_segments))

        if cleanup_mode in ("all", "segments"):
            logger.debug(f"Cleanup mode: {cleanup_mode}. Deleting segments from video_led_pairs ...")
            deleted_files.extend(VSC_Postprocess.cleanup_segments(video_led_pairs, video_path, led_path))

        if cleanup_mode not in ("all", "segments", "intermediates"):
            logger.debug(
                f"Cleanup mode: {cleanup_mode} is not recognised or indicates preservation. All files from split-and-merge will be preserved."
            )

        clear_temp_file(*deleted_files)
        logger.debug("Cleanup process is finished!!!")


if __name__ == "__main__":
    pass
