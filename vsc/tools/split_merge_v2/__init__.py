import os
import shutil
import uuid
from typing import List, Optional, Tuple

from vsc.composite_led_v2.datastruct import <PERSON>adata
from vsc.composite_led_v2.session import Session
from vsc.tools.split_merge_v2.merge import Merger
from vsc.tools.split_merge_v2.split import Splitter


class SplitMerge:
    """A class to split videos into sub-sessions for processing and merge them back.

    This class provides functionality to split input videos into sub-sessions for separate
    processing and then merge them back into a single output file. It handles temporary
    storage and cleanup of intermediate files.

    Args:
        data_folder (str, optional): Directory to store temporary files. Defaults to UUID.
        device (str): Device to run models on. Defaults to "cuda:0".
        weights_folder (str): Directory containing model weights. Defaults to "checkpoints".
    """

    def __init__(
        self,
        data_folder: Optional[str] = None,
        device: str = "cuda:0",
        weights_folder: str = "checkpoints",
    ):
        """Initialize the SplitMerge instance.

        Args:
            data_folder (str, optional): Directory to store temporary files. Defaults to UUID.
            device (str): Device to run models on. Defaults to "cuda:0".
            weights_folder (str): Directory containing model weights. Defaults to "checkpoints".
        """
        self.data_folder = data_folder if data_folder is not None else f"{str(uuid.uuid4())}"
        self.splitter = Splitter(data_folder=self.data_folder, device=device, weights_folder=weights_folder)
        self.merger = Merger(data_folder=self.data_folder)

    def split(
        self, video_path: str, led_path: str, max_interval_duration: int, threshold: int
    ) -> Tuple[List[Session], Metadata]:
        """Split a video into sub-sessions for processing.

        Args:
            video_path (str): Path to input video file
            led_path (str): Path to LED overlay file
            max_interval_duration (int): Maximum duration for each sub-session
            threshold (int): Threshold duration for splitting

        Returns:
            Tuple[List[Session], Metadata]: List of split sessions and video metadata
        """
        return self.splitter.run(video_path, led_path, max_interval_duration, threshold)

    def merge(self, session_list: List[Session], metadata: Metadata, output_path: str):
        """Merge processed sub-sessions back into a single video.

        Args:
            session_list (List[Session]): List of processed sessions to merge
            metadata (Metadata): Original video metadata
            output_path (str): Path to save merged output video
        """
        return self.merger.run(session_list, metadata, output_path)

    def release(self):
        """Clean up temporary storage by removing the data folder."""
        if os.path.isdir(self.data_folder):
            shutil.rmtree(self.data_folder)
