import os
import shutil
import uuid
from typing import List

from vsc.composite_led_v2.datastruct import Metadata
from vsc.composite_led_v2.session import Session
from vsc.composite_led_v2.utils.video_utils import merge_audio_pipeline
from vsc.composite_led_v2.video_player import VideoPlayer
from vsc.utils.exception import EmptySessionsMergeError


class Merger:
    """A class to merge multiple video sessions into a single output video with audio.

    This class handles merging multiple video sessions, combining their intermediate frames
    and audio into a single output video file.

    Attributes:
        data_folder (str): Path to folder for storing temporary merge files.
    """

    def __init__(self, data_folder: str = None):
        """Initialize the Merger.

        Args:
            data_folder (str, optional): Path to folder for storing temporary merge files.
                If None, a random UUID folder will be created. Defaults to None.
        """
        self.data_folder = data_folder if data_folder is not None else f"{str(uuid.uuid4())}"
        os.makedirs(self.data_folder, exist_ok=True)

    def run(self, session_list: List[Session], metadata: Metadata, output_path: str):
        """Run the merge process to combine multiple sessions into a single output video.

        Merges the intermediate frames from all sessions sequentially and combines with
        the audio from metadata to create the final output video.

        Args:
            session_list (List[Session]): List of Session objects to merge
            metadata (Metadata): Metadata containing audio paths and other info
            output_path (str): Path where the final merged video will be saved

        Raises:
            EmptySessionsMergeError: If session_list is empty
        """
        intermediate_path = os.path.join(self.data_folder, "merge", "intermediate.mp4")
        os.makedirs(os.path.dirname(intermediate_path), exist_ok=True)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        if len(session_list) > 1:
            output_video_player: VideoPlayer = VideoPlayer(
                video_path=intermediate_path,
                fps=session_list[0].source.get_source_properties().fps,
                size=(
                    session_list[0].source.get_source_properties().frame_width,
                    session_list[0].source.get_source_properties().frame_height,
                ),
                bitrate=session_list[0].source.get_source_properties().bit_rate,
            )

            output_video_player.init_writer()
            for session in session_list:
                session.init_intermediate_reader()

                for _ in range(session.source.get_source_properties().total_frames):
                    frame = session.get_intermediate_frame_data()
                    output_video_player.write(frame)

                session.release_intermediate_reader()

            output_video_player.release_writer()
        elif len(session_list) == 1:
            shutil.copy(session_list[0].get_intermediate_path(), intermediate_path)
        else:
            raise EmptySessionsMergeError("Session list is empty!")

        merge_audio_pipeline(
            led_path=metadata.led_audio_path,
            audio_path=metadata.audio_path,
            video_path=intermediate_path,
            output_path=output_path,
        )

        os.remove(intermediate_path)
        for session in session_list:
            session.release()
