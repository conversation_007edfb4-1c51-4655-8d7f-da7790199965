import copy
import os
import uuid
from typing import List, Optional, Tuple

import numpy as np
from fvutils.media import get_video_fps, get_video_properties
from tqdm import tqdm

from vsc.composite_led_v2.datastruct import (
    LedType,
    Metadata,
    ScreenOrientation,
    VideoProperties,
)
from vsc.composite_led_v2.models.contour_detector.cv2_contour_detector import (
    ContourDetectorInference,
)
from vsc.composite_led_v2.models.corners_detector.corners_detector import (
    CornersDetector,
)
from vsc.composite_led_v2.models.occluded_detector.occluded_corners_detector import (
    OccludedCornersDetector,
)
from vsc.composite_led_v2.models.screen_checker.screen_checker import <PERSON><PERSON>hecker
from vsc.composite_led_v2.models.screen_segmentor.hsv import HSVScreenSegmentorInference
from vsc.composite_led_v2.session import Session
from vsc.composite_led_v2.utils.corner_utils import extract_bbox_from_contour
from vsc.utils import cfg
from vsc.utils.logger import logger


class Splitter:
    """A class to split video processing into sub-sessions and handle static/moving screens.

    This class processes input videos by segmenting screens, detecting contours, and handling
    screen orientations. It can split long videos into smaller chunks for processing.

    Args:
        data_folder (str, optional): Directory to store temporary files. Defaults to UUID.
        device (str): Device to run models on. Defaults to "cuda:0".
        weights_folder (str): Directory containing model weights. Defaults to "checkpoints".
    """

    def __init__(
        self,
        data_folder: Optional[str] = None,
        device: str = "cuda:0",
        weights_folder: str = "checkpoints",
    ):
        self.data_folder = data_folder if data_folder is not None else f"{str(uuid.uuid4())}"
        self.device = device
        self.weights_folder = weights_folder

        self.screen_segmentor = HSVScreenSegmentorInference(
            color_range=cfg.led.mask.color_range,
        )

        self.contour_detector = ContourDetectorInference(low_epsilon=cfg.led.mask.low_epsilon)

        self.__mask_area_threshold = 0.001
        self.screen_checker = ScreenChecker(mask_area_threshold=self.__mask_area_threshold)

        self.__bbox_stretch = 20
        self.corners_detector = CornersDetector(bbox_stretch=self.__bbox_stretch)

        self.occluded_corners_detector = OccludedCornersDetector(
            matting_appearance_threshold=cfg.led.matte.matte_appearance_threshold,
            stable_distance_threshold=cfg.led.post_processing.stable_threshold,
            angle_distortion_threshold=cfg.led.angle_distortion_threshold,
            distance_noise_threshold=cfg.led.post_processing.distance_noise,
        )

        self.__first_image: Optional[np.ndarray] = None
        self.__first_contour: Optional[np.ndarray] = None

    def extract_metadata(self, audio_path: str, led_audio_path: str) -> Metadata:
        """Extract metadata from the first frame and contour.

        Args:
            audio_path (str): Path to the source audio file
            led_audio_path (str): Path to the LED audio file

        Returns:
            Metadata: Object containing audio paths and detected corners
        """
        width, height, _ = self.__first_image.shape
        corners = self.corners_detector.run(self.__first_contour, width, height)

        return Metadata(audio_path, led_audio_path, corners)

    def run(
        self, video_path: str, led_path: str, max_interval_duration: int, threshold: int
    ) -> Tuple[List[Session], Metadata]:
        """Main entry point to process a video with LED data.

        Args:
            video_path (str): Path to the source video
            led_path (str): Path to the LED video/image
            max_interval_duration (int): Maximum duration for split intervals
            threshold (int): Threshold for minimum video length to trigger splitting

        Returns:
            Tuple[List[Session], Metadata]: List of processing sessions and video metadata
        """

        properties = get_video_properties(video_path)
        video_properties = VideoProperties(
            fps=get_video_fps(video_path),
            frame_width=properties['width'],
            frame_height=properties['height'],
            bit_rate=f"{int(int(properties['bit_rate']) / 1000)}k",
            total_frames=int(properties['nb_frames']),
        )

        split_flag = 0 < max_interval_duration < threshold <= video_properties.total_frames
        session = Session(
            video_path=video_path,
            led_path=led_path,
            output_path=os.path.join(self.data_folder, "full", "output.mp4"),
            temp_dir=os.path.join(self.data_folder, "full"),
            session_id="resources",
        )

        if not split_flag:
            return self.run_without_split(session)

        split_nb_frames_list = [
            max_interval_duration for _ in range(video_properties.total_frames // max_interval_duration)
        ] + [video_properties.total_frames % max_interval_duration]
        sub_session_list = []
        for i, split_nb_frames in enumerate(split_nb_frames_list):
            sub_source_properties = copy.deepcopy(session.source.get_source_properties())
            sub_source_properties.total_frames = split_nb_frames

            sub_led_properties = copy.deepcopy(session.led.get_led_properties())
            if sub_led_properties.type is LedType.VIDEO:
                sub_led_properties.properties.nb_frames = split_nb_frames

            sub_session = Session(
                video_path=os.path.join(self.data_folder, f"split_{i}", f"video{os.path.splitext(video_path)[1]}"),
                led_path=os.path.join(self.data_folder, f"split_{i}", f"led{os.path.splitext(led_path)[1]}"),
                output_path=os.path.join(self.data_folder, f"split_{i}", "output.mp4"),
                source_properties=sub_source_properties,
                led_properties=sub_led_properties,
                temp_dir=os.path.join(self.data_folder, f"split_{i}"),
                session_id="resources",
            )
            sub_session_list.append(sub_session)

        return self.run_with_split(session, sub_session_list)

    def extract_with_split(self, session: Session, sub_session_list: List[Session]):
        """Extract frame data and screen information when splitting into sub-sessions.

        Args:
            session (Session): Main processing session
            sub_session_list (List[Session]): List of sub-sessions for split processing
        """
        width = session.source.get_source_properties().frame_width
        height = session.source.get_source_properties().frame_height
        session.source.init_source_reader()
        session.source.init_screen_mask_writer()
        session.led.init_reader()
        session_key = 0
        for i, sub_session in enumerate(sub_session_list):
            sub_session.source.init_source_writer()
            sub_session.source.init_screen_mask_writer()
            sub_session.led.init_writer()

            for key in tqdm(range(sub_session.source.get_source_properties().total_frames), desc=f"Process sub-session {i}: "):
                frame = session.source.get_frame_data()
                led_frame = session.led.get_frame_data()

                screen_mask = self.screen_segmentor.run([frame], None)[0]
                contours_list, low_epsilon_contours_list = self.contour_detector.run([screen_mask])
                contours, low_epsilon_contours = contours_list[0], low_epsilon_contours_list[0]

                # exist check
                is_exist = self.screen_checker.exist_screen(mask=screen_mask, contour=contours)

                # screen detection
                if is_exist:
                    x0, y0, x1, y1 = extract_bbox_from_contour(contours, width, height, self.__bbox_stretch)
                    screen_box = [np.array([x0, y0, x1, y1], dtype=np.int32)]
                else:
                    screen_box = [np.array([0, 0, width, height], dtype=np.int32)]

                # save first frame
                if self.__first_image is None:
                    self.__first_image = frame.copy()

                if self.__first_contour is None:
                    self.__first_contour = contours.copy()

                # save sub-session data
                sub_session.source.set_contour_data(key, contours)
                sub_session.source.set_low_epsilon_contour_data(key, low_epsilon_contours)
                sub_session.source.set_bounding_box_data(key, screen_box)
                sub_session.source.set_screen_exist_data(key, is_exist)

                sub_session.source.set_screen_mask_data(screen_mask)
                sub_session.source.set_frame_data(frame)
                sub_session.led.set_frame_data(led_frame)

                # save session data
                session.source.set_screen_mask_data(screen_mask)

                session.source.set_contour_data(session_key, contours)
                session.source.set_low_epsilon_contour_data(session_key, low_epsilon_contours)
                session.source.set_bounding_box_data(session_key, screen_box)
                session.source.set_screen_exist_data(session_key, is_exist)

                session_key += 1

            sub_session.source.release_source_writer()
            sub_session.source.release_screen_mask_writer()
            sub_session.led.release_writer()

        session.source.release_source_reader()
        session.source.release_screen_mask_writer()
        session.led.release_reader()

    def extract_without_split(self, session: Session):
        """Extract frame data and screen information without splitting into sub-sessions.

        Args:
            session (Session): Processing session
        """
        width = session.source.get_source_properties().frame_width
        height = session.source.get_source_properties().frame_height
        session.source.init_source_reader()
        session.source.init_screen_mask_writer()

        for key in tqdm(session.source.keys, desc="Process session: "):
            frame = session.source.get_frame_data()

            # screen segmentation
            screen_mask = self.screen_segmentor.run([frame], None)[0]

            # contour detection
            contours_list, low_epsilon_contours_list = self.contour_detector.run([screen_mask])
            contours, low_epsilon_contours = contours_list[0], low_epsilon_contours_list[0]

            # exist check
            is_exist = self.screen_checker.exist_screen(mask=screen_mask, contour=contours)

            # screen detection
            if is_exist:
                x0, y0, x1, y1 = extract_bbox_from_contour(contours, width, height, self.__bbox_stretch)
                screen_box = [np.array([x0, y0, x1, y1], dtype=np.int32)]
            else:
                screen_box = [np.array([0, 0, width, height], dtype=np.int32)]

            session.source.set_screen_mask_data(screen_mask)

            session.source.set_contour_data(key, contours)
            session.source.set_low_epsilon_contour_data(key, low_epsilon_contours)
            session.source.set_bounding_box_data(key, screen_box)
            session.source.set_screen_exist_data(key, is_exist)

        session.source.release_source_reader()
        session.source.release_screen_mask_writer()

    def check_static(
        self, session: Session, sub_session_list: List[Session], split_flag: bool
    ) -> Tuple[List[Session], Metadata]:
        """Check if screen is static and prepare metadata accordingly.

        Args:
            session (Session): Main processing session
            sub_session_list (List[Session]): List of sub-sessions
            split_flag (bool): Whether video was split into sub-sessions

        Returns:
            Tuple[List[Session], Metadata]: List of sessions and video metadata
        """
        is_static = self.screen_checker.static_check(session.source.get_all_contour_data())
        screen_boxes, nb = session.source.list_bounding_box_data(ins=False)
        have_screen = (
            np.sum((screen_boxes[:, 2] - screen_boxes[:, 0]) * (screen_boxes[:, 3] - screen_boxes[:, 1])).astype(np.float32)
            / nb
            / session.source.get_source_properties().frame_width
            / session.source.get_source_properties().frame_height
        ) > 0.0011
        if split_flag and is_static:
            metadata = self.extract_metadata(session.source.get_audio_path(), session.led.get_audio_path())
            session_list = sub_session_list
        else:
            metadata = Metadata(session.source.get_audio_path(), session.led.get_audio_path())
            session_list = [session]

        for s in session_list:
            s.set_exist_screen(have_screen)
            s.set_screen_orientation(ScreenOrientation.STATIC if is_static else ScreenOrientation.MOVING)
            s.source.set_metadata(metadata)

        logger.info(f"Screen is {'not ' if not is_static else ''}static")
        return session_list, metadata

    def run_with_split(self, session: Session, sub_session_list: List[Session]) -> Tuple[List[Session], Metadata]:
        """Process video with splitting into sub-sessions.

        Args:
            session (Session): Main processing session
            sub_session_list (List[Session]): List of sub-sessions

        Returns:
            Tuple[List[Session], Metadata]: Processed sessions and metadata
        """
        self.extract_with_split(session, sub_session_list)
        return self.check_static(session, sub_session_list, True)

    def run_without_split(self, session: Session) -> Tuple[List[Session], Metadata]:
        """Process video without splitting into sub-sessions.

        Args:
            session (Session): Processing session

        Returns:
            Tuple[List[Session], Metadata]: Processed session and metadata
        """
        self.extract_without_split(session)
        return self.check_static(session, [], False)
