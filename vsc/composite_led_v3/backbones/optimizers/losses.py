import cv2
import numpy as np


def apply_affine_transform(points, affine_params):
    """
    Apply affine transformation to points

    Args:
        points: Points to transform (Nx2)
        affine_params: [a, b, c, d, tx, ty] representing:
                      [x'] = [a b] [x] + [tx]
                      [y']   [c d] [y]   [ty]

    Returns:
        Transformed points
    """
    a, b, c, d, tx, ty = affine_params

    # Affine transformation matrix
    A = np.array([[a, b], [c, d]])
    t = np.array([tx, ty])

    # Apply transformation: p' = A*p + t
    transformed = (A @ points.T).T + t

    return transformed


def mask_coverage_loss(points, mask, weight=1.0):
    """
    Loss based on how well points cover the mask region
    """
    # Create convex hull of points
    try:
        hull = cv2.convexHull(points.astype(np.float32))
        hull_points = hull.reshape(-1, 2)

        # Create mask from hull
        hull_mask = np.zeros_like(mask)
        cv2.fillPoly(hull_mask, [hull_points.astype(np.int32)], 1)

        # Compute intersection and union
        intersection = np.logical_and(mask, hull_mask)
        union = np.logical_or(mask, hull_mask)

        # IoU-based loss
        if np.sum(union) == 0:
            return 1e6

        iou = np.sum(intersection) / np.sum(union)
        return weight * (1.0 - iou)

    except:
        return weight * 1e6
