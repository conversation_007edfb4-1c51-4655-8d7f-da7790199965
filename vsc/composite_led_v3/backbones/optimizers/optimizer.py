import numpy as np
from scipy.optimize import minimize
from typing import Optional, List, Literal, Dict

from vsc.composite_led_v3.backbones.optimizers.losses import mask_coverage_loss, apply_affine_transform


class Optimizer:
    def __init__(
        self,
        method: Literal["L-BFGS-B", "SLSQP"] = "L-BFGS-B",
        max_iterations: int = 20,
        convergence_threshold: float = 1e-4,
        loss_weights: Optional[Dict] = None
    ):
        self.method = method
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold
        self.loss_weights = loss_weights or {
            'mask_coverage': 1.0
        }
        # self.bounds = [
        #     (0.5, 2.0),  # a: scale/rotation component
        #     (-0.5, 0.5),  # b: shear/rotation component
        #     (-0.5, 0.5),  # c: shear/rotation component
        #     (0.5, 2.0),  # d: scale/rotation component
        #     (-50, 50),  # tx: translation x
        #     (-50, 50)  # ty: translation y
        # ]

    def _calculate_loss(self, affine_params, mask: np.ndarray, list_previous_points: List[np.ndarray], points: np.ndarray):
        new_points = apply_affine_transform(points, affine_params)

        coverage_loss = mask_coverage_loss(new_points, mask, weight=self.loss_weights['mask_coverage'])
        if len(list_previous_points) == 0:
            return coverage_loss

        return coverage_loss

    def _optimize_step(self, mask: np.ndarray, list_previous_points: List[np.ndarray], points: np.ndarray):
        initial_affine = np.array([1.0, 0.0, 0.0, 1.0, 0.0, 0.0])  # [a, b, c, d, tx, ty]
        result = minimize(
            fun=lambda params: self._calculate_loss(params, mask, list_previous_points, points),
            x0=initial_affine,
            method=self.method,
            # bounds=self.bounds,
            options={'maxiter': 100}
        )

        best_affine = result.x
        new_points = apply_affine_transform(points, best_affine)

        return new_points, best_affine, result.fun

    def run(
        self,
        mask: np.ndarray,
        list_previous_points: List[np.ndarray],
        initial_points: Optional[np.ndarray] = None,
    ):
        height, width = mask.shape[0], mask.shape[1]
        points = initial_points.copy() if initial_points else np.array(
            [[0, 0], [0, height - 1], [width - 1, height - 1], [width - 1, 0]],
            dtype=np.float32
        )
        prev_loss = float('inf')

        for iteration in range(self.max_iterations):
            new_points, affine_params, loss_value = self._optimize_step(mask, list_previous_points, points)

            if prev_loss - loss_value < self.convergence_threshold and iteration > 0:
                break

            points = new_points
            prev_loss = loss_value

        return points
