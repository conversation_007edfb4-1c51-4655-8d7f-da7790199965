import numpy as np
import cv2


class DifferentiableIoULoss:
    def __init__(self, mask_height, mask_width, sigma=1.0, sample_density=1.0):
        """
        Differentiable IoU loss between mask and quadrilateral

        Args:
            mask_height, mask_width: Dimensions of the segmentation mask
            sigma: Gaussian smoothing parameter for differentiability
            sample_density: Sampling density for quadrilateral rasterization (1.0 = pixel-level)
        """
        self.H, self.W = mask_height, mask_width
        self.sigma = sigma
        self.sample_density = sample_density

        # Pre-compute sampling grid for efficiency
        self._setup_sampling_grid()

    def _setup_sampling_grid(self):
        """Create dense sampling grid for smooth quadrilateral representation"""
        # Create sampling points with sub-pixel precision
        step = 1.0 / self.sample_density
        x_coords = np.arange(0, self.W, step)
        y_coords = np.arange(0, self.H, step)

        self.grid_x, self.grid_y = np.meshgrid(x_coords, y_coords)
        self.grid_points = np.stack([self.grid_x.ravel(), self.grid_y.ravel()], axis=1)
        self.grid_shape = self.grid_x.shape

        print(f"Sampling grid: {self.grid_shape[0]}x{self.grid_shape[1]} points")

    def point_in_quadrilateral(self, points, quad_vertices):
        """
        Vectorized point-in-quadrilateral test using cross products

        Args:
            points: Nx2 array of points to test
            quad_vertices: 4x2 array of quadrilateral vertices in order

        Returns:
            N-length boolean array indicating if each point is inside
        """
        # Ensure quadrilateral vertices are in correct order (counter-clockwise)
        quad = self._ensure_counter_clockwise(quad_vertices)

        # For each edge of the quadrilateral, check if points are on the "inside" side
        inside = np.ones(len(points), dtype=bool)

        for i in range(4):
            # Current edge from vertex i to vertex (i+1)%4
            v1 = quad[i]
            v2 = quad[(i + 1) % 4]

            # Vector from v1 to v2 (edge vector)
            edge = v2 - v1

            # Vector from v1 to each test point
            to_points = points - v1

            # Cross product: if positive, point is on left side of edge
            # For counter-clockwise quadrilateral, inside points should be on left side
            cross = edge[0] * to_points[:, 1] - edge[1] * to_points[:, 0]
            inside &= (cross >= 0)

        return inside

    def _ensure_counter_clockwise(self, vertices):
        """Ensure vertices are ordered counter-clockwise"""
        # Compute signed area using shoelace formula
        x, y = vertices[:, 0], vertices[:, 1]
        signed_area = 0.5 * np.sum(x[:-1] * y[1:] - x[1:] * y[:-1])
        signed_area += 0.5 * (x[-1] * y[0] - x[0] * y[-1])  # Close the polygon

        if signed_area < 0:  # Clockwise ordering
            return vertices[::-1]  # Reverse order
        return vertices

    def soft_point_in_quadrilateral(self, points, quad_vertices):
        """
        Soft (differentiable) version of point-in-quadrilateral test
        Uses distance-based soft boundaries instead of hard binary decisions
        """
        quad = self._ensure_counter_clockwise(quad_vertices)

        # Initialize soft membership (starts at 1.0, gets reduced by each edge)
        soft_inside = np.ones(len(points))

        for i in range(4):
            v1 = quad[i]
            v2 = quad[(i + 1) % 4]

            # Edge vector and normal
            edge = v2 - v1
            edge_normal = np.array([-edge[1], edge[0]])  # Perpendicular to edge
            edge_normal = edge_normal / (np.linalg.norm(edge_normal) + 1e-8)

            # Distance from each point to the edge line
            to_points = points - v1
            distances = np.dot(to_points, edge_normal)

            # Soft sigmoid function: inside if distance > 0, outside if distance < 0
            # Using sigmoid for differentiability: sigmoid(x/sigma)
            soft_inside *= 1.0 / (1.0 + np.exp(-distances / self.sigma))

        return soft_inside

    def rasterize_quadrilateral_soft(self, quad_vertices):
        """
        Create soft (differentiable) rasterization of quadrilateral

        Returns:
            2D array representing soft quadrilateral mask
        """
        # Get soft membership for all grid points
        soft_membership = self.soft_point_in_quadrilateral(self.grid_points, quad_vertices)

        # Reshape back to grid
        soft_quad_mask = soft_membership.reshape(self.grid_shape)

        # Resize to match target mask dimensions if needed
        if self.sample_density != 1.0:
            soft_quad_mask = cv2.resize(soft_quad_mask, (self.W, self.H),
                                        interpolation=cv2.INTER_LINEAR)

        return soft_quad_mask

    def compute_iou_loss(self, quad_vertices, target_mask):
        """
        Compute differentiable IoU loss between quadrilateral and mask

        Args:
            quad_vertices: 4x2 array of quadrilateral vertices
            target_mask: HxW binary/probability mask

        Returns:
            loss: Scalar IoU loss (1 - IoU)
            grad: 4x2 array of gradients w.r.t. quad vertices
        """
        # Generate soft quadrilateral mask
        pred_mask = self.rasterize_quadrilateral_soft(quad_vertices)

        # Compute soft IoU
        intersection = np.sum(pred_mask * target_mask)
        union = np.sum(pred_mask) + np.sum(target_mask) - intersection

        # Add small epsilon for numerical stability
        epsilon = 1e-6
        iou = intersection / (union + epsilon)
        loss = 1.0 - iou

        # Compute gradients using finite differences for robustness
        gradients = self._compute_gradients_finite_diff(quad_vertices, target_mask)

        return loss, gradients

    def _compute_gradients_finite_diff(self, quad_vertices, target_mask, h=1e-4):
        """
        Compute gradients using finite differences
        More stable than analytical gradients for complex soft rasterization
        """
        base_loss, _ = self._compute_iou_loss_only(quad_vertices, target_mask)  # Fixed: use _compute_iou_loss_only
        gradients = np.zeros_like(quad_vertices)

        for i in range(4):  # For each vertex
            for j in range(2):  # For each coordinate (x, y)
                # Perturb coordinate slightly
                quad_perturbed = quad_vertices.copy()
                quad_perturbed[i, j] += h

                # Compute perturbed loss
                perturbed_loss, _ = self._compute_iou_loss_only(quad_perturbed, target_mask)

                # Finite difference gradient
                gradients[i, j] = (perturbed_loss - base_loss) / h

        return gradients

    def _compute_iou_loss_only(self, quad_vertices, target_mask):
        """Helper function that only computes loss (for finite differences)"""
        pred_mask = self.rasterize_quadrilateral_soft(quad_vertices)

        intersection = np.sum(pred_mask * target_mask)
        union = np.sum(pred_mask) + np.sum(target_mask) - intersection

        epsilon = 1e-6
        iou = intersection / (union + epsilon)
        return 1.0 - iou, None


class QuadrilateralOptimizer:
    """
    Gradient descent optimizer for quadrilateral fitting using IoU loss
    """

    def __init__(self, target_mask, quad_vertices = None, learning_rate=0.01, sigma=1.0):
        self.target_mask = target_mask.astype(np.float32)
        self.lr = learning_rate

        # Initialize IoU loss computer
        h, w = target_mask.shape
        self.iou_loss = DifferentiableIoULoss(h, w, sigma=sigma)

        # Initialize quadrilateral (centered square)
        center_x, center_y = w // 2, h // 2
        size = min(w, h) // 4
        self.quad_vertices = quad_vertices if quad_vertices is not None else np.array([
            [center_x - size, center_y - size],  # Bottom-left
            [center_x + size, center_y - size],  # Bottom-right
            [center_x + size, center_y + size],  # Top-right
            [center_x - size, center_y + size]  # Top-left
        ], dtype=np.float32)

    def optimize(self, max_iterations=1000, tolerance=1e-6):
        """Run gradient descent optimization"""
        losses = []

        for iteration in range(max_iterations):
            # Compute loss and gradients
            loss, gradients = self.iou_loss.compute_iou_loss(self.quad_vertices, self.target_mask)
            losses.append(loss)

            # Gradient descent update
            self.quad_vertices -= self.lr * gradients

            # Check convergence
            if len(losses) > 1 and abs(losses[-2] - losses[-1]) < tolerance:
                print(f"Converged at iteration {iteration}")
                break

            if iteration % 2 == 0:
                iou = 1.0 - loss
                print(f"Iteration {iteration}: IoU = {iou:.4f}, Loss = {loss:.6f}")

        return losses

    def get_vertices(self):
        """Get current quadrilateral vertices"""
        return self.quad_vertices.copy()


# Example usage and testing
def iou_optimization():
    """Test the IoU optimization with a synthetic target"""
    mask = cv2.imread("/data/dwg/temps/vsc/issues/develop_v3/debugs/debug_01/mask_0.png", cv2.IMREAD_GRAYSCALE).astype(np.float32) / 255.0

    # Find bounding box of non-zero values
    rows = np.any(mask > 0, axis=1)
    cols = np.any(mask > 0, axis=0)
    ymin, ymax = np.where(rows)[0][[0, -1]]
    xmin, xmax = np.where(cols)[0][[0, -1]]

    # Initialize optimizer with bounding box vertices
    init_quad_vertices = np.array([
        [xmin, ymin],  # Top-left
        [xmax, ymin],  # Top-right
        [xmax, ymax],  # Bottom-right
        [xmin, ymax]  # Bottom-left
    ], dtype=np.float64)

    optimizer = QuadrilateralOptimizer(mask, quad_vertices=init_quad_vertices, learning_rate=1000, sigma=2.0)

    print(f"\nInitial quadrilateral vertices:")
    print(optimizer.get_vertices())

    # Compute initial IoU
    initial_loss, _ = optimizer.iou_loss.compute_iou_loss(optimizer.quad_vertices, mask)
    print(f"Initial IoU: {1.0 - initial_loss:.4f}")

    # Run optimization
    losses = optimizer.optimize(max_iterations=500)

    # Results
    final_vertices = optimizer.get_vertices()
    final_iou = 1.0 - losses[-1]

    print(f"\nOptimization Results:")
    print(f"Final quadrilateral vertices:")
    print(final_vertices)
    print(f"Final IoU: {final_iou:.4f}")
    print(f"IoU improvement: {final_iou - (1.0 - initial_loss):.4f}")

    return optimizer, losses


if __name__ == "__main__":
    print("Testing Differentiable IoU Loss for Quadrilateral Optimization")
    print("=" * 70)

    optimizer, losses = iou_optimization()

    print(f"Optimized quadrilateral vertices:")
    print(optimizer.get_vertices())

    print(f"\nLoss progression (last 10 iterations):")
    for i, loss in enumerate(losses[-10:]):
        iou = 1.0 - loss
        print(f"  Iter {len(losses) - 10 + i}: IoU = {iou:.4f}")

    # Plot target mask and optimized quadrilateral
    import matplotlib.pyplot as plt
    vertices = optimizer.get_vertices()

    plt.imshow(optimizer.target_mask)
    plt.plot(vertices[[0,1,2,3,0], 0], vertices[[0,1,2,3,0], 1], 'r-')
    plt.title('Optimized Mask & Quadrilateral')

    plt.tight_layout()
    plt.show()
