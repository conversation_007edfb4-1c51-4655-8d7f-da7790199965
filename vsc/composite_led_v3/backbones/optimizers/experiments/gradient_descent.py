import numpy as np
import cv2
from scipy.ndimage import gaussian_filter


class AffineOptimizer:
    def __init__(self, template_points, segmentation_mask, learning_rate=0.01):
        """
        template_points: 4x2 array of template points
        segmentation_mask: 2D binary/probability mask
        """
        self.template_points = np.array(template_points)  # Shape: (4, 2)
        self.mask = segmentation_mask.astype(np.float32)
        self.lr = learning_rate

        # Compute mask gradients for backprop
        self.grad_x = cv2.Sobel(self.mask, cv2.CV_32F, 1, 0, ksize=3)
        self.grad_y = cv2.Sobel(self.mask, cv2.CV_32F, 0, 1, ksize=3)

        # Initialize affine parameters [a, b, c, d, tx, ty]
        # Start with identity transform
        self.params = np.array([1.0, 0.0, 0.0, 1.0, 0.0, 0.0])

    def transform_points(self, points, params):
        """Apply affine transform to points"""
        a, b, c, d, tx, ty = params

        # Affine matrix
        A = np.array([[a, b], [c, d]])
        t = np.array([tx, ty])

        # Transform: x' = Ax + t
        transformed = points @ A.T + t
        return transformed

    def sample_mask(self, points):
        """Sample segmentation mask at given points using bilinear interpolation"""
        h, w = self.mask.shape

        # Clip to valid coordinates
        x = np.clip(points[:, 0], 0, w - 1)
        y = np.clip(points[:, 1], 0, h - 1)

        # Bilinear interpolation
        x0, y0 = np.floor([x, y]).astype(int)
        x1, y1 = x0 + 1, y0 + 1

        # Clip indices
        x0 = np.clip(x0, 0, w - 1)
        x1 = np.clip(x1, 0, w - 1)
        y0 = np.clip(y0, 0, h - 1)
        y1 = np.clip(y1, 0, h - 1)

        # Interpolation weights
        wa = (x1 - x) * (y1 - y)
        wb = (x - x0) * (y1 - y)
        wc = (x1 - x) * (y - y0)
        wd = (x - x0) * (y - y0)

        # Sample values
        values = (wa * self.mask[y0, x0] +
                  wb * self.mask[y0, x1] +
                  wc * self.mask[y1, x0] +
                  wd * self.mask[y1, x1])

        return values

    def sample_gradients(self, points):
        """Sample mask gradients at given points"""
        h, w = self.mask.shape

        x = np.clip(points[:, 0], 0, w - 1)
        y = np.clip(points[:, 1], 0, h - 1)

        # Simple nearest neighbor sampling for gradients
        xi = np.round(x).astype(int)
        yi = np.round(y).astype(int)

        grad_x_vals = self.grad_x[yi, xi]
        grad_y_vals = self.grad_y[yi, xi]

        return np.column_stack([grad_x_vals, grad_y_vals])

    def compute_loss(self, params):
        """Compute fitting loss"""
        transformed_points = self.transform_points(self.template_points, params)
        mask_values = self.sample_mask(transformed_points)

        # Loss: want high mask values (close to 1)
        loss = np.sum((1 - mask_values) ** 2)
        return loss

    def compute_gradients(self, params):
        """Compute gradients w.r.t. affine parameters"""
        transformed_points = self.transform_points(self.template_points, params)
        mask_values = self.sample_mask(transformed_points)
        mask_grads = self.sample_gradients(transformed_points)

        # dL/dparams
        param_grads = np.zeros(6)

        for i in range(len(self.template_points)):
            x, y = self.template_points[i]
            mask_val = mask_values[i]
            grad_x, grad_y = mask_grads[i]

            # Chain rule: dL/dparam = dL/dmask_val * dmask_val/dxy * dxy/dparam
            dL_dmask = -2 * (1 - mask_val)

            # Transform jacobians
            # x' = ax + by + tx, y' = cx + dy + ty
            dxy_dparams = np.array([
                [x, y, 0, 0, 1, 0],  # dx'/dparams
                [0, 0, x, y, 0, 1]  # dy'/dparams
            ])

            # Gradient contribution
            dmask_dxy = np.array([grad_x, grad_y])
            param_grads += dL_dmask * dmask_dxy @ dxy_dparams

        return param_grads

    def optimize(self, max_iterations=1000, tolerance=1e-6):
        """Run gradient descent optimization"""
        losses = []

        for iteration in range(max_iterations):
            # Compute loss and gradients
            loss = self.compute_loss(self.params)
            grads = self.compute_gradients(self.params)

            losses.append(loss)

            # Update parameters
            self.params -= self.lr * grads

            # Check convergence
            if len(losses) > 1 and abs(losses[-2] - losses[-1]) < tolerance:
                print(f"Converged at iteration {iteration}")
                break

            if iteration % 100 == 0:
                print(f"Iteration {iteration}: Loss = {loss:.6f}")

        return losses

    def get_transformed_points(self):
        """Get final transformed points"""
        return self.transform_points(self.template_points, self.params)

    def get_transform_matrix(self):
        """Get the 3x3 affine transformation matrix"""
        a, b, c, d, tx, ty = self.params
        return np.array([
            [a, b, tx],
            [c, d, ty],
            [0, 0, 1]
        ])


# Example usage
def example_usage():
    # Create a synthetic segmentation mask
    mask = np.zeros((200, 200))
    # Add a rotated rectangle region
    center = (100, 100)
    rect_points = np.array([
        [80, 80], [120, 80], [120, 120], [80, 120]
    ])

    # Rotate the mask region
    angle = np.pi / 6
    cos_a, sin_a = np.cos(angle), np.sin(angle)
    R = np.array([[cos_a, -sin_a], [sin_a, cos_a]])
    rotated_rect = (rect_points - center) @ R.T + center

    # Fill the rotated rectangle in mask
    cv2.fillPoly(mask, [rotated_rect.astype(int)], 1.0)

    # Define template points (square)
    template_points = np.array([
        [0, 0], [40, 0], [40, 40], [0, 40]
    ])

    # Initialize optimizer
    optimizer = AffineOptimizer(template_points, mask, learning_rate=0.1)

    # Run optimization
    losses = optimizer.optimize(max_iterations=500)

    # Get results
    final_points = optimizer.get_transformed_points()
    transform_matrix = optimizer.get_transform_matrix()

    print("Template points:", template_points)
    print("Optimized points:", final_points)
    print("Transform matrix:\n", transform_matrix)

    return optimizer, losses


# Advanced: Adding regularization and constraints
class RegularizedAffineOptimizer(AffineOptimizer):
    def __init__(self, template_points, segmentation_mask, learning_rate=0.01,
                 reg_weight=0.01):
        super().__init__(template_points, segmentation_mask, learning_rate)
        self.reg_weight = reg_weight

    def compute_loss(self, params):
        """Compute loss with regularization"""
        base_loss = super().compute_loss(params)

        # Add regularization to prevent extreme deformation
        a, b, c, d, tx, ty = params

        # Encourage orthogonality and uniform scaling
        reg_loss = (a * d - b * c - 1) ** 2  # Determinant should be close to 1
        reg_loss += (a ** 2 + d ** 2 - 2) ** 2  # Diagonal elements close to 1
        reg_loss += (b ** 2 + c ** 2)  # Off-diagonal elements small

        return base_loss + self.reg_weight * reg_loss

    def compute_gradients(self, params):
        """Compute gradients with regularization"""
        base_grads = super().compute_gradients(params)

        # Regularization gradients
        a, b, c, d, tx, ty = params

        det_term = 2 * (a * d - b * c - 1)
        diag_term = 2 * (a ** 2 + d ** 2 - 2)

        reg_grads = np.array([
            det_term * d + diag_term * 2 * a,  # da
            det_term * (-c) + 2 * b,  # db
            det_term * (-b) + 2 * c,  # dc
            det_term * a + diag_term * 2 * d,  # dd
            0,  # dtx
            0  # dty
        ])

        return base_grads + self.reg_weight * reg_grads


if __name__ == "__main__":
    optimizer, losses = example_usage()