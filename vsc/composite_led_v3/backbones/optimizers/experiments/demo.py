import cv2
import numpy as np


class Optimizer:
    def __init__(self):
        pass

    @staticmethod
    def transform_points(points, params):
        """Apply affine transform to points"""
        a, b, c, d, tx, ty = params

        # Affine matrix
        A = np.array([[a, b], [c, d]])
        t = np.array([tx, ty])

        # Transform: x' = Ax + t
        transformed = points @ A.T + t
        return transformed

    def calculate_loss(self, params, points, mask):
        transformed_points = self.transform_points(points, params)


    def optimize(self, mask, max_iteration=1000, tolerance=1e-6):
        width, height = mask.shape[1], mask.shape[0]
        points = np.array([[0, 0], [0, height], [width, height], [width, 0]])

        losses = []

        for iteration in range(max_iteration):
            loss = self._calculate_loss()
            losses.append(loss)
            if loss < tolerance:
                break
            self._update_parameters()
        return losses


def main():
    mask = cv2.imread("/data/dwg/temps/vsc/issues/develop_v3/debugs/debug_01/mask_0.png")
    points = np.array([[0, 0], [0, 1079], [1919, 1079], [1919, 0]])



if __name__ == "__main__":
    main()
