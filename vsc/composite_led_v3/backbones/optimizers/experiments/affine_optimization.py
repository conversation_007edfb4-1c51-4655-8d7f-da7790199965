import numpy as np
import cv2
from scipy.optimize import minimize
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt
from matplotlib.patches import Polygon
from skimage import morphology, measure
import warnings

warnings.filterwarnings('ignore')


class AffineMaskOptimizer:
    """
    Optimize four points to fit a quadrilateral mask using affine transformations
    """

    def __init__(self, mask, initial_points):
        """
        Initialize optimizer with mask and initial points

        Args:
            mask: Binary mask (2D numpy array) where True/1 represents the quadrilateral
            initial_points: Initial 4 points [(x1,y1), (x2,y2), (x3,y3), (x4,y4)]
        """
        self.mask = mask.astype(bool)
        self.mask_height, self.mask_width = mask.shape
        self.initial_points = np.array(initial_points, dtype=np.float64)

        # Extract mask boundary points for distance computation
        self.boundary_points = self._extract_boundary_points()

        # Store optimization history
        self.history = []

    def _extract_boundary_points(self):
        """Extract boundary points from the mask"""
        # Find contours
        contours, _ = cv2.findContours(
            self.mask.astype(np.uint8),
            cv2.RETR_EXTERNAL,
            cv2.CHAIN_APPROX_NONE
        )

        if not contours:
            raise ValueError("No contours found in mask")

        # Get the largest contour (main quadrilateral)
        largest_contour = max(contours, key=cv2.contourArea)
        boundary_points = largest_contour.reshape(-1, 2)

        return boundary_points

    def _apply_affine_transform(self, points, affine_params):
        """
        Apply affine transformation to points

        Args:
            points: Points to transform (Nx2)
            affine_params: [a, b, c, d, tx, ty] representing:
                          [x'] = [a b] [x] + [tx]
                          [y']   [c d] [y]   [ty]

        Returns:
            Transformed points
        """
        a, b, c, d, tx, ty = affine_params

        # Affine transformation matrix
        A = np.array([[a, b], [c, d]])
        t = np.array([tx, ty])

        # Apply transformation: p' = A*p + t
        transformed = (A @ points.T).T + t

        return transformed

    def _points_to_mask_distance(self, points):
        """
        Compute distance from points to mask boundary

        Args:
            points: Points to evaluate (4x2)

        Returns:
            Total distance to mask boundary
        """
        if len(self.boundary_points) == 0:
            return 1e6

        # Compute distance from each point to nearest boundary point
        distances = cdist(points, self.boundary_points)
        min_distances = np.min(distances, axis=1)

        return np.sum(min_distances)

    def _mask_coverage_loss(self, points, weight=1.0):
        """
        Loss based on how well points cover the mask region
        """
        # Create convex hull of points
        try:
            hull = cv2.convexHull(points.astype(np.float32))
            hull_points = hull.reshape(-1, 2)

            # Create mask from hull
            hull_mask = np.zeros_like(self.mask)
            cv2.fillPoly(hull_mask, [hull_points.astype(np.int32)], 1)

            # Compute intersection and union
            intersection = np.logical_and(self.mask, hull_mask)
            union = np.logical_or(self.mask, hull_mask)

            # IoU-based loss
            if np.sum(union) == 0:
                return 1e6

            iou = np.sum(intersection) / np.sum(union)
            return weight * (1.0 - iou)

        except:
            return weight * 1e6

    def _inside_mask_bonus(self, points, weight=0.5):
        """
        Bonus for points that are inside the mask region
        """
        bonus = 0
        for point in points:
            x, y = int(round(point[0])), int(round(point[1]))
            if (0 <= x < self.mask_width and
                    0 <= y < self.mask_height and
                    self.mask[y, x]):
                bonus += weight

        return -bonus  # Negative because we want to minimize

    def _quadrilateral_shape_loss(self, points, weight=0.1):
        """
        Encourage quadrilateral shape properties
        """
        if len(points) != 4:
            return weight * 1e6

        # Check if quadrilateral is convex
        def cross_product_2d(o, a, b):
            return (a[0] - o[0]) * (b[1] - o[1]) - (a[1] - o[1]) * (b[0] - o[0])

        cross_products = []
        for i in range(4):
            o = points[i]
            a = points[(i + 1) % 4]
            b = points[(i + 2) % 4]
            cross_products.append(cross_product_2d(o, a, b))

        # Penalty for non-convex quadrilateral
        positive = sum(1 for cp in cross_products if cp > 0)
        negative = sum(1 for cp in cross_products if cp < 0)

        if positive == 4 or negative == 4:
            convexity_loss = 0  # Convex
        else:
            convexity_loss = weight * min(positive, negative)

        # Penalty for degenerate quadrilateral (very small area)
        area = 0.5 * abs(
            (points[0][0] * (points[1][1] - points[3][1]) +
             points[1][0] * (points[2][1] - points[0][1]) +
             points[2][0] * (points[3][1] - points[1][1]) +
             points[3][0] * (points[0][1] - points[2][1]))
        )

        area_loss = weight * max(0, 100 - area) / 100  # Penalty if area < 100

        return convexity_loss + area_loss

    def _combined_loss(self, affine_params, current_points, loss_weights=None):
        """
        Combined loss function for optimization

        Args:
            affine_params: Affine transformation parameters
            current_points: Current point positions
            loss_weights: Dictionary of loss weights

        Returns:
            Total loss
        """
        if loss_weights is None:
            loss_weights = {
                'boundary_distance': 1.0,
                'mask_coverage': 2.0,
                'inside_bonus': 0.5,
                'shape_quality': 0.1
            }

        # Apply affine transform to get new points
        new_points = self._apply_affine_transform(current_points, affine_params)

        # Compute individual losses
        boundary_loss = self._points_to_mask_distance(new_points)
        coverage_loss = self._mask_coverage_loss(new_points)
        inside_bonus = self._inside_mask_bonus(new_points)
        shape_loss = self._quadrilateral_shape_loss(new_points)

        # Combine losses
        total_loss = (
                loss_weights['boundary_distance'] * boundary_loss +
                loss_weights['mask_coverage'] * coverage_loss +
                loss_weights['inside_bonus'] * inside_bonus +
                loss_weights['shape_quality'] * shape_loss
        )

        return total_loss

    def optimize_step(self, current_points, loss_weights=None, method='L-BFGS-B'):
        """
        Perform one optimization step with affine transformation

        Args:
            current_points: Current positions of the 4 points
            loss_weights: Loss function weights
            method: Optimization method

        Returns:
            (new_points, affine_params, loss_value)
        """
        # Initial guess: identity transformation
        initial_affine = [1.0, 0.0, 0.0, 1.0, 0.0, 0.0]  # [a, b, c, d, tx, ty]

        # Bounds to prevent extreme transformations
        bounds = [
            (0.5, 2.0),  # a: scale/rotation component
            (-0.5, 0.5),  # b: shear/rotation component
            (-0.5, 0.5),  # c: shear/rotation component
            (0.5, 2.0),  # d: scale/rotation component
            (-50, 50),  # tx: translation x
            (-50, 50)  # ty: translation y
        ]

        # Optimize affine parameters
        result = minimize(
            fun=lambda params: self._combined_loss(params, current_points, loss_weights),
            x0=initial_affine,
            method=method,
            bounds=bounds,
            options={'maxiter': 100}
        )

        # Apply best transformation
        best_affine = result.x
        new_points = self._apply_affine_transform(current_points, best_affine)

        return new_points, best_affine, result.fun

    def optimize_iterative(self, max_iterations=20, loss_weights=None,
                           convergence_threshold=1e-4, verbose=True):
        """
        Iteratively optimize points to fit mask using affine transformations

        Args:
            max_iterations: Maximum number of optimization steps
            loss_weights: Loss function weights
            convergence_threshold: Stop if improvement is below this threshold
            verbose: Print progress

        Returns:
            Final optimized points
        """
        current_points = self.initial_points.copy()
        self.history = [current_points.copy()]

        prev_loss = float('inf')

        for iteration in range(max_iterations):
            # Perform optimization step
            new_points, affine_params, loss_value = self.optimize_step(
                current_points, loss_weights
            )

            # Check for convergence
            improvement = prev_loss - loss_value
            if improvement < convergence_threshold and iteration > 0:
                if verbose:
                    print(f"Converged at iteration {iteration} (improvement: {improvement:.6f})")
                break

            # Update for next iteration
            current_points = new_points
            self.history.append(current_points.copy())
            prev_loss = loss_value

            if verbose:
                print(f"Iteration {iteration + 1}: Loss = {loss_value:.4f}, "
                      f"Improvement = {improvement:.6f}")
                print(f"  Affine params: a={affine_params[0]:.3f}, b={affine_params[1]:.3f}, "
                      f"c={affine_params[2]:.3f}, d={affine_params[3]:.3f}, "
                      f"tx={affine_params[4]:.1f}, ty={affine_params[5]:.1f}")

        return current_points

    def optimize_with_different_strategies(self, strategies=None):
        """
        Try different optimization strategies and return the best result

        Args:
            strategies: List of strategy dictionaries

        Returns:
            Best optimized points and strategy used
        """
        if strategies is None:
            strategies = [
                {
                    'name': 'Boundary Focus',
                    'weights': {'boundary_distance': 2.0, 'mask_coverage': 1.0,
                                'inside_bonus': 0.3, 'shape_quality': 0.1},
                    'iterations': 15
                },
                {
                    'name': 'Coverage Focus',
                    'weights': {'boundary_distance': 1.0, 'mask_coverage': 3.0,
                                'inside_bonus': 0.5, 'shape_quality': 0.2},
                    'iterations': 15
                },
                {
                    'name': 'Balanced',
                    'weights': {'boundary_distance': 1.5, 'mask_coverage': 1.5,
                                'inside_bonus': 0.4, 'shape_quality': 0.15},
                    'iterations': 20
                }
            ]

        best_points = None
        best_loss = float('inf')
        best_strategy = None

        for strategy in strategies:
            print(f"\n--- Trying Strategy: {strategy['name']} ---")

            # Reset to initial points
            result_points = self.optimize_iterative(
                max_iterations=strategy['iterations'],
                loss_weights=strategy['weights'],
                verbose=False
            )

            # Evaluate final loss
            final_loss = self._combined_loss([1, 0, 0, 1, 0, 0], result_points, strategy['weights'])

            print(f"Final loss: {final_loss:.4f}")

            if final_loss < best_loss:
                best_loss = final_loss
                best_points = result_points.copy()
                best_strategy = strategy['name']

        print(f"\nBest strategy: {best_strategy} (loss: {best_loss:.4f})")
        return best_points, best_strategy


def create_sample_mask(shape=(200, 200)):
    """Create a sample quadrilateral mask for testing"""
    mask = np.zeros(shape, dtype=bool)

    # Define a quadrilateral
    quad_points = np.array([
        [50, 40],
        [150, 30],
        [160, 160],
        [40, 170]
    ], dtype=np.int32)

    # Fill the quadrilateral
    cv2.fillPoly(mask.astype(np.uint8), [quad_points], 1)
    mask = mask.astype(bool)

    return mask, quad_points


def visualize_optimization_process(optimizer, final_points, mask):
    """Visualize the optimization process"""
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))

    # Show mask
    axes[0, 0].imshow(mask, cmap='gray', alpha=0.7)
    axes[0, 0].set_title('Target Mask')
    axes[0, 0].set_aspect('equal')

    # Show initial points
    axes[0, 1].imshow(mask, cmap='gray', alpha=0.3)
    initial_quad = Polygon(optimizer.initial_points, fill=False,
                           edgecolor='red', linewidth=2, linestyle='--')
    axes[0, 1].add_patch(initial_quad)
    axes[0, 1].scatter(*zip(*optimizer.initial_points), color='red', s=50, zorder=5)
    axes[0, 1].set_title('Initial Points')
    axes[0, 1].set_aspect('equal')

    # Show final result
    axes[1, 0].imshow(mask, cmap='gray', alpha=0.3)
    final_quad = Polygon(final_points, fill=False,
                         edgecolor='green', linewidth=2)
    axes[1, 0].add_patch(final_quad)
    axes[1, 0].scatter(*zip(*final_points), color='green', s=50, zorder=5)
    axes[1, 0].set_title('Final Optimized Points')
    axes[1, 0].set_aspect('equal')

    # Show optimization history
    axes[1, 1].imshow(mask, cmap='gray', alpha=0.3)

    # Plot evolution of points
    colors = ['red', 'blue', 'orange', 'purple']
    for i in range(4):
        point_history = [points[i] for points in optimizer.history]
        x_hist, y_hist = zip(*point_history)
        axes[1, 1].plot(x_hist, y_hist, color=colors[i], marker='o',
                        markersize=3, alpha=0.7, label=f'Point {i + 1}')

    axes[1, 1].set_title('Optimization History')
    axes[1, 1].legend()
    axes[1, 1].set_aspect('equal')

    plt.tight_layout()
    plt.show()


def test_affine_mask_optimizer():
    """Test the affine mask optimizer"""
    print("Testing Affine Transform Mask Optimizer\n")

    # Create sample mask and poor initial points
    mask, true_quad = create_sample_mask()

    # Create initial points that are far from optimal
    initial_points = np.array([
        [80, 80],  # Center-ish points
        [120, 80],
        [120, 120],
        [80, 120]
    ], dtype=np.float64)

    print("=== Test 1: Basic Optimization ===")

    # Initialize optimizer
    optimizer = AffineMaskOptimizer(mask, initial_points)

    # Run optimization
    final_points = optimizer.optimize_iterative(
        max_iterations=15,
        verbose=True
    )

    print(f"\nInitial points:\n{initial_points}")
    print(f"\nFinal points:\n{final_points}")
    print(f"\nTrue quadrilateral:\n{true_quad}")

    # Compute final error
    final_distance = optimizer._points_to_mask_distance(final_points)
    initial_distance = optimizer._points_to_mask_distance(initial_points)

    print(f"\nInitial distance to mask: {initial_distance:.2f}")
    print(f"Final distance to mask: {final_distance:.2f}")
    print(f"Improvement: {((initial_distance - final_distance) / initial_distance * 100):.1f}%")

    # Test different strategies
    print("\n=== Test 2: Multiple Strategies ===")
    best_points, best_strategy = optimizer.optimize_with_different_strategies()

    # Visualize results
    visualize_optimization_process(optimizer, best_points, mask)

    return optimizer, best_points


if __name__ == "__main__":
    optimizer, final_points = test_affine_mask_optimizer()