import time

import cv2
import matplotlib.pyplot as plt
import numpy as np
import torch
from line_profiler import LineProfiler
from torch.nn import functional as F


class DifferentiableIoULoss:
    def __init__(self, mask_height, mask_width, sigma=1.0, sample_density=1.0, device=None):
        """
        Differentiable IoU loss between mask and quadrilateral

        Args:
            mask_height, mask_width: Dimensions of the segmentation mask
            sigma: Gaussian smoothing parameter for differentiability
            sample_density: Sampling density for quadrilateral rasterization (1.0 = pixel-level)
            device: PyTorch device (cuda or cpu)
        """
        self.H, self.W = mask_height, mask_width
        self.sigma = sigma
        self.sample_density = sample_density
        
        self.sigmoid = torch.nn.Sigmoid()
        
        # Auto-select device if not specified
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Pre-compute sampling grid for efficiency
        self._setup_sampling_grid()

    def _setup_sampling_grid(self):
        """Create dense sampling grid for smooth quadrilateral representation"""
        # Create sampling points with sub-pixel precision
        step = 1.0 / self.sample_density
        x_coords = torch.arange(0, self.W, step, device=self.device)
        y_coords = torch.arange(0, self.H, step, device=self.device)

        # Create grid using torch meshgrid
        self.grid_y, self.grid_x = torch.meshgrid(y_coords, x_coords, indexing='ij')
        self.grid_points = torch.stack([self.grid_x.flatten(), self.grid_y.flatten()], dim=1)
        self.grid_shape = self.grid_x.shape

        print(f"Sampling grid: {self.grid_shape[0]}x{self.grid_shape[1]} points on {self.device}")

    def ensure_counter_clockwise(self, vertices):
        """Ensure vertices are ordered counter-clockwise"""
        # Convert to tensor if needed
        if not isinstance(vertices, torch.Tensor):
            vertices = torch.tensor(vertices, dtype=torch.float32, device=self.device)
            
        # Compute signed area using shoelace formula (vectorized)
        x, y = vertices[:, 0], vertices[:, 1]
        # Shift x and y for vectorized computation
        x_next = torch.roll(x, -1)
        y_next = torch.roll(y, -1)
        
        # Compute signed area
        signed_area = 0.5 * torch.sum(x * y_next - x_next * y)
        
        # Reverse if clockwise
        if signed_area < 0:
            return vertices.flip(0)  # Reverse order
        return vertices

    def soft_point_in_quadrilateral(self, points, quad_vertices):
        """
        Vectorized soft (differentiable) version of point-in-quadrilateral test
        Uses distance-based soft boundaries instead of hard binary decisions
        """
        # Convert inputs to tensors if needed
        if not isinstance(points, torch.Tensor):
            points = torch.tensor(points, dtype=torch.float32, device=self.device)
        if not isinstance(quad_vertices, torch.Tensor):
            quad_vertices = torch.tensor(quad_vertices, dtype=torch.float32, device=self.device)

        # quad = self.ensure_counter_clockwise(quad_vertices)

        quad = quad_vertices

        # Initialize soft membership (starts at 1.0)
        soft_inside = torch.ones(len(points), device=self.device)

        # Vectorize edge processing by creating edge vectors and normals for all edges at once
        v1 = quad
        v2 = torch.roll(quad, -1, dims=0)  # Shift to get next vertices
        
        # Edge vectors
        edges = v2 - v1  # Shape: [4, 2]
        
        # Edge normals (perpendicular to edges)
        edge_normals = torch.stack([-edges[:, 1], edges[:, 0]], dim=1)  # Shape: [4, 2]
        
        # Normalize edge normals
        edge_norms = torch.norm(edge_normals, dim=1, keepdim=True) + 1e-8
        edge_normals = edge_normals / edge_norms  # Shape: [4, 2]

        # For each edge, compute distances from all points
        for i in range(4):
            # Compute vectors from v1 to all points
            to_points = points - v1[i]  # Shape: [N, 2]
            
            # Compute signed distances to edge line
            distances = torch.matmul(to_points, edge_normals[i])  # Shape: [N]
            
            # Apply sigmoid for soft boundary
            soft_edge = self.sigmoid(distances / self.sigma)
            
            # Update soft membership
            soft_inside *= soft_edge

        return soft_inside

    def rasterize_quadrilateral_soft(self, quad_vertices):
        """
        Create soft (differentiable) rasterization of quadrilateral

        Returns:
            2D tensor representing soft quadrilateral mask
        """
        # Get soft membership for all grid points
        soft_membership = self.soft_point_in_quadrilateral(self.grid_points, quad_vertices)

        # Reshape back to grid
        soft_quad_mask = soft_membership.reshape(self.grid_shape)

        # Resize to match target mask dimensions if needed
        if self.sample_density != 1.0:
            # Use PyTorch's interpolate for resizing
            soft_quad_mask = F.interpolate(
                soft_quad_mask.unsqueeze(0).unsqueeze(0),  # Add batch and channel dims
                size=(self.H, self.W),
                mode='bilinear',
                align_corners=False
            ).squeeze(0).squeeze(0)  # Remove batch and channel dims

        return soft_quad_mask

    def compute_iou_loss(self, quad_vertices, target_mask):
        """
        Compute differentiable IoU loss between quadrilateral and mask with autograd

        Args:
            quad_vertices: 4x2 array/tensor of quadrilateral vertices
            target_mask: HxW binary/probability mask (numpy array or tensor)

        Returns:
            loss: Scalar IoU loss (1 - IoU)
            grad: 4x2 tensor of gradients w.r.t. quad vertices
        """
        with torch.autograd.set_detect_anomaly(True):
            # Convert inputs to tensors if needed
            quad_vertices = torch.tensor(quad_vertices, dtype=torch.float32, device=self.device) if not isinstance(
                quad_vertices, torch.Tensor) else quad_vertices.clone().detach().to(self.device)
            quad_vertices.requires_grad_(True)

            target_mask = torch.tensor(target_mask, dtype=torch.float32, device=self.device) if not isinstance(
                target_mask, torch.Tensor) else target_mask.to(self.device)
            # Ensure target mask has values in valid range [0,1]
            target_mask = torch.clamp(target_mask, 0.0, 1.0)

            # Generate soft quadrilateral mask
            pred_mask = self.rasterize_quadrilateral_soft(quad_vertices)

            # Add small epsilon to masks to avoid zero division
            epsilon = 1e-6
            pred_mask = pred_mask + epsilon
            target_mask = target_mask + epsilon

            # Compute soft IoU with improved numerical stability
            intersection = torch.sum(pred_mask * target_mask)
            pred_sum = torch.sum(pred_mask)
            target_sum = torch.sum(target_mask)

            # Ensure sums are positive to avoid numerical issues
            pred_sum = torch.clamp(pred_sum, min=epsilon)
            target_sum = torch.clamp(target_sum, min=epsilon)

            # Calculate union with better numerical stability
            union = pred_sum + target_sum - intersection
            union = torch.clamp(union, min=epsilon)  # Ensure union is not too small

            # Calculate IoU with stable division
            iou = intersection / union
            iou = torch.clamp(iou, 0.0, 1.0)  # Ensure IoU is in valid range
            loss = 1.0 - iou

            # Compute gradients using autograd
            loss.backward()

            # Handle NaN gradients
            gradients = quad_vertices.grad.clone()

            # Replace NaN gradients with zeros
            gradients = torch.nan_to_num(gradients, nan=0.0, posinf=1.0, neginf=-1.0)

            # Apply gradient clipping to prevent extreme updates
            max_grad_norm = 10.0
            grad_norm = torch.norm(gradients)
            if grad_norm > max_grad_norm:
                gradients = gradients * (max_grad_norm / grad_norm)

            return loss.item(), gradients

class QuadrilateralOptimizer:
    """
    Gradient descent optimizer for quadrilateral fitting using IoU loss
    """

    def __init__(self, target_mask, quad_vertices=None, learning_rate=0.01, sigma=1.0, device=None):
        # Auto-select device if not specified
        self.device = device if device is not None else torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Convert target mask to tensor
        if isinstance(target_mask, np.ndarray):
            self.target_mask = torch.tensor(target_mask, dtype=torch.float32, device=self.device)
        else:
            self.target_mask = target_mask.to(self.device)
            
        self.lr = learning_rate

        # Initialize IoU loss computer
        h, w = target_mask.shape
        self.iou_loss = DifferentiableIoULoss(h, w, sigma=sigma, device=self.device)

        # Initialize quadrilateral (centered square) if not provided
        if quad_vertices is None:
            center_x, center_y = w // 2, h // 2
            size = min(w, h) // 4
            self.quad_vertices = torch.tensor([
                [center_x - size, center_y - size],  # Bottom-left
                [center_x + size, center_y - size],  # Bottom-right
                [center_x + size, center_y + size],  # Top-right
                [center_x - size, center_y + size]   # Top-left
            ], dtype=torch.float32, device=self.device)
        else:
            # Convert to tensor if needed
            if isinstance(quad_vertices, np.ndarray):
                self.quad_vertices = torch.tensor(quad_vertices, dtype=torch.float32, device=self.device)
            else:
                self.quad_vertices = quad_vertices.to(self.device)

    def optimize(self, max_iterations=1000, tolerance=1e-6):
        """Run gradient descent optimization"""
        losses = []
        
        # Make a copy that requires gradients
        vertices = self.quad_vertices.clone().detach().requires_grad_(True)

        for iteration in range(max_iterations):
            # Compute loss and gradients
            loss, gradients = self.iou_loss.compute_iou_loss(vertices, self.target_mask)
            losses.append(loss)

            # Gradient descent update
            with torch.no_grad():
                vertices -= self.lr * gradients
            
            # Check convergence
            if len(losses) > 1 and abs(losses[-2] - losses[-1]) < tolerance:
                break
        
        # Update the optimizer's vertices
        self.quad_vertices = vertices.detach()
        
        return losses

    def get_vertices(self):
        """Get current quadrilateral vertices"""
        return self.quad_vertices.cpu().numpy()


# Example usage and testing
def iou_optimization():
    """Test the IoU optimization with a synthetic target"""
    mask = cv2.imread("/data/dwg/temps/vsc/issues/develop_v3/debugs/debug_01/mask_0.png", cv2.IMREAD_GRAYSCALE).astype(np.float32) / 255.0

    # Find bounding box of non-zero values
    rows = np.any(mask > 0, axis=1)
    cols = np.any(mask > 0, axis=0)
    ymin, ymax = np.where(rows)[0][[0, -1]]
    xmin, xmax = np.where(cols)[0][[0, -1]]

    # Initialize optimizer with bounding box vertices
    init_quad_vertices = np.array([
        [xmin, ymin],  # Top-left
        [xmax, ymin],  # Top-right 
        [xmax, ymax],  # Bottom-right
        [xmin, ymax]   # Bottom-left
    ], dtype=np.float64)

    # Create optimizer with GPU acceleration if available
    optimizer = QuadrilateralOptimizer(
        mask,
        quad_vertices=init_quad_vertices,
        learning_rate=2000,
        sigma=2.0,
        device=torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    )

    # Run optimization
    profiler = LineProfiler()
    profiler.add_function(optimizer.optimize)
    profiler.add_function(optimizer.iou_loss.ensure_counter_clockwise)
    profiler.add_function(optimizer.iou_loss.compute_iou_loss)
    profiler.add_function(optimizer.iou_loss.rasterize_quadrilateral_soft)
    profiler.add_function(optimizer.iou_loss.soft_point_in_quadrilateral)

    losses = profiler.runcall(optimizer.optimize, max_iterations=50)
    # losses = optimizer.optimize(max_iterations=50)

    print(f"Final quadrilateral vertices:: {optimizer.get_vertices()}")
    print(f"Final IoU: {1.0 - losses[-1]:.4f}")

    profiler.print_stats()

    return optimizer, losses


def main():
    optimizer, losses = iou_optimization()

    # Plot target mask and optimized quadrilateral
    vertices = optimizer.get_vertices()

    plt.imshow(optimizer.target_mask.cpu().numpy())
    plt.plot(vertices[[0,1,2,3,0], 0], vertices[[0,1,2,3,0], 1], 'r-')
    plt.title('Optimized Mask & Quadrilateral')

    plt.tight_layout()
    plt.show()


if __name__ == "__main__":
    main()
