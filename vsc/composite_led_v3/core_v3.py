import numpy as np
from tqdm import tqdm

from vsc.composite_led_v2.models.blender.led_blender import LedBlender
from vsc.composite_led_v2.models.post_processor.post_processor import PostProcessor
from vsc.composite_led_v2.session import Session
from vsc.composite_led_v2.utils.corner_utils import extract_bbox_from_contour, is_dummy_corners
from vsc.composite_led_v3.backbones.optimizers.optimizer import Optimizer
from vsc.utils import cfg
from vsc.utils.logger import logger


class CompositeLEDInferenceV3:
    """
    Main class for Composite LED Inference pipeline version 3.

    This class handles the entire LED compositing process, including
    - Screen segmentation and detection
    - Corners detection and validation
    - Matting for screen isolation
    - Corners stabilization and smoothing
    - LED blending with the original video
    - Audio merging
    """

    def __init__(self, device: str = "cuda:0", weights_folder: str = "checkpoints"):
        """
        Initialize the Composite LED Inference pipeline.

        Args:
            device (str): Computing device to use (for example, 'cuda:0', 'cpu'), default is 'cuda:0'.
            weights_folder (str): Path to the folder containing model weights, the default is 'checkpoints'.
        """
        self.device = device
        self.weights_folder = weights_folder

        self.post_processor = PostProcessor(
            pyramid_iters=cfg.led.mask.pyramid_iters,
            use_edge_preserving=cfg.led.mask.use_edge_preserving,
            sigma_s=cfg.led.mask.sigma_s,
            sigma_r=cfg.led.mask.sigma_r,
            bbox_stretch=cfg.led.mask.bbox_stretch,
            erode_kernel=cfg.led.mask.erode,
            timi_matte_removing_threshold=cfg.led.mask.timi_matte_threshold_for_removing_noise,
            color_range=cfg.led.mask.color_range,
        )

        self.blender = LedBlender(device=self.device)

    def extract_source_corners(self, session: Session):

        width = session.source.get_source_properties().frame_width
        height = session.source.get_source_properties().frame_height

        for key in tqdm(session.source.keys, desc="Extract source corners: "):
            # contour detection
            contours = session.source.get_contour_data(key)

            # exist check
            is_exist = session.source.get_screen_exist_data(key)

            if is_exist:
                x0, y0, x1, y1 = extract_bbox_from_contour(contours, width, height, 0)
                corners = np.array([[x0, y0], [x0, y1], [x1, y1], [x1, y0]], dtype=np.int32)
                session.source.set_corners_data(key, corners)

    def run_post_processing_and_blending(self, session: Session):
        session.source.init_source_reader()
        session.source.init_screen_mask_reader()
        session.led.init_reader()
        session.init_intermediate_writer()

        width = session.source.get_source_properties().frame_width
        height = session.source.get_source_properties().frame_height

        timi_matte = np.zeros((height, width), dtype=np.float32)

        list_previous_corners = []

        optimizer = Optimizer()

        for key in tqdm(session.source.keys, desc="Post-processing screen mask: "):

            frame = session.source.get_frame_data()
            led = session.led.get_frame_data()
            screen_mask = session.source.get_screen_mask_data()
            corners = session.source.get_corners_data(key)
            screen_box = session.source.get_bounding_box_data(key)

            if session.source.get_screen_exist_data(key) and not is_dummy_corners(corners):
                # refine mask
                psm = self.post_processor.post_process_mask(
                    screen_mask=screen_mask,
                    frame=frame,
                    fittest_corners=corners,
                    timi_matte=timi_matte,
                    screen_box=screen_box[0],
                )

                refined_mask = np.zeros_like(psm)
                refined_mask[screen_box[0][1]:screen_box[0][3], screen_box[0][0]:screen_box[0][2]] = psm[screen_box[0][1]:screen_box[0][3], screen_box[0][0]:screen_box[0][2]]

                refined_corners = optimizer.run(
                    mask=refined_mask,
                    list_previous_points=list_previous_corners,
                    initial_points=None,
                )

                output = self.blender.run(
                    frame=frame,
                    fittest_corners=corners,
                    corners=corners,
                    led=led,
                    screen_mask=refined_mask,
                )

                list_previous_corners.append(refined_corners)
                list_previous_corners = list_previous_corners[-10:]

            else:
                list_previous_corners = []
                output = frame
            session.set_intermediate_frame_data(output)

        session.source.release_source_reader()
        session.source.release_screen_mask_reader()
        session.led.release_reader()
        session.release_intermediate_writer()
        logger.info("Finished mask post processing and blending")

    def release(self):
        self.blender.release()

    def run(self, session: Session):
        if session.get_exist_screen():
            logger.info("Start pipeline VSC")

            self.extract_source_corners(session)

            self.run_post_processing_and_blending(session)

            logger.info("End pipeline")

        else:
            logger.info("No screen detected. Skip pipeline")

            # run without processing
            session.run_without_processing()

        self.release()



def main():
    pass


if __name__ == "__main__":
    main()
