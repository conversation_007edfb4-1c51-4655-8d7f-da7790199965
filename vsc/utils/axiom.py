import argparse
import os

from axiom_client.client import Axiom


AXIOM_ARTIFACTS_DB = {"db": {"name": "vsc", "rtype": "model", "id": "720"}}


def get_axiom_client(username: str, password: str) -> Axiom:
    if not all([username, password]):
        raise ValueError("Username or Password is invalid")
    axiom_instance = Axiom()
    return_token = axiom_instance.login(username, password)
    print(f"Login with token [...]{return_token[:5]}")  # For security
    return axiom_instance


def upload_file_to_axiom(axiom_client=None, axiom_db=None, env=None, version="", file_path=""):
    resource_name = axiom_db[env.lower()]["name"]

    rtype = axiom_db[env.lower()]["rtype"]

    print(f"\n Destination: {resource_name}")
    print(f"\n version: {version}")
    print(f"\n upload_file: {file_path}")

    size = os.path.getsize(file_path)

    if size == 0:
        print("{} is an empty file. Not upload.".format(file_path))
        return False

    # Upload file to axiom
    item = axiom_client.resource_item_create(resource_name, rtype, version, file_path.split("/")[-1], size=size)

    axiom_client.resource_item_upload(item["id"], file_path)
    return True


class AxiomUploader:
    def __init__(self, username: str, password: str):

        self.__client = get_axiom_client(username, password)

    def upload_folder(self, folder_path: str, version: str):
        for r, d, f in os.walk(folder_path):
            for file in f:
                upload_file_to_axiom(
                    axiom_client=self.__client,
                    axiom_db=AXIOM_ARTIFACTS_DB,
                    env="db",
                    version=version,
                    file_path=os.path.join(r, file),
                )


if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("-u", "--username", type=str, help="Axiom username", default="<EMAIL>")

    parser.add_argument("-p", "--password", type=str, help="Axiom password", default="M9pa6F0a@Z92")

    parser.add_argument("-f", "--folder", type=str, help="Path to folder", default="dist")

    parser.add_argument("-v", "--version", type=str, help="Library version", default="0.0.1")
    args = parser.parse_args()
    uploader = AxiomUploader(args.username, args.password)
    uploader.upload_folder(args.folder, version=args.version)
