import csv
import os
import platform

import GPUtil
import matplotlib.pyplot as plt
import pandas as pd
import psutil
import seaborn as sns
from codetiming import Timer

from vsc.utils.logger import logger_perf


def save_to_csv(data, save_path):
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    header = ["frame_id", "cpu_core", "cpu_usaged", "execution_time"]
    data_dict = {}

    if data[0] == 0 and os.path.isfile(save_path):
        os.remove(save_path)

    for h, item in zip(header, data):
        data_dict.update({h: item})

    with open(save_path, "a", newline="") as csvfile:
        if os.stat(save_path).st_size == 0:
            writer = csv.DictWriter(csvfile, fieldnames=header)
            writer.writeheader()

        writer = csv.DictWriter(csvfile, fieldnames=header)
        # Write the data rows
        writer.writerow(data_dict)


def cuda_is_available() -> bool:
    cuda_available = False
    if platform.system().lower() == "linux":
        try:
            with open("/proc/driver/nvidia/version", "r") as f:
                cuda_version = f.read().strip()
        except FileNotFoundError:
            cuda_version = None

        if cuda_version:
            # print(f"CUDA version: {cuda_version}")
            cuda_available = True
        else:
            # print("CUDA not found")
            cuda_available = False
    else:
        print("This script only works on Linux")
    return cuda_available


@Timer("system_utils.get_system_info", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
def get_system_info():
    # CPU
    num_cpus = psutil.cpu_count(logical=True)

    process = psutil.Process()
    num_cores_used = process.cpu_num()
    mem_used = process.memory_info().rss / (1024**3)
    cpu_used = psutil.cpu_percent(interval=1)

    # GPU
    try:
        gpus = GPUtil.getGPUs()
        num_gpus = len(gpus)
    except Exception:
        num_gpus = 0

    # Memory
    mem = psutil.virtual_memory()
    total_mem = mem.total / (1024**3)  # Convert byte to GB
    logger_perf.debug(f"Number of CPUs: {num_cores_used} / {num_cpus}")
    logger_perf.debug(f"CPU usage: {cpu_used}%")
    logger_perf.debug(f"Number of GPUs: {num_gpus}")
    logger_perf.debug(f"Total memory (GB): {mem_used} / {total_mem}")
    return num_cores_used, cpu_used


def visualize_data(csv_file: str, save_path: str = None, show_chart: bool = False):
    if not os.path.isfile(csv_file):
        logger_perf.warning(f"csv_file '{csv_file}' not found")
        return
    df = pd.read_csv(csv_file)

    # Normalize data
    # df['cpu_core_normalized'] = (df['cpu_core'] / df['cpu_core'].max()) * 100
    # df['cpu_usaged_normalized'] = (df['cpu_usaged'] / df['cpu_usaged'].max()) * 100
    df['execution_time_normalized'] = df['execution_time'] * 10

    plt.rcParams['font.family'] = 'Arial'  # Change 'Arial' to the desired font family

    sns.set_style("whitegrid")

    sns.lineplot(data=df, x='frame_id', y='cpu_core', color='red', label='CPU Core')

    sns.lineplot(data=df, x='frame_id', y='cpu_usaged', color='blue', label='CPU Usage')

    # sns.lineplot(data=df, x='frame_id', y='execution_time', color='green', label='Execution Time')
    sns.lineplot(data=df, x='frame_id', y='execution_time_normalized', color='green', label='Execution Time')

    # Set labels and title
    plt.xlabel('Frame ID')
    plt.ylabel('Normalized Values')
    plt.title('Normalized Data Trends over Frame ID')

    plt.legend()

    if save_path is None:
        save_path = csv_file[:-4] + "_plot.png"
    plt.savefig(save_path)
    plt.close()

    if show_chart:
        plt.show()


def find_idle_cores(cpu_usage_thresh: int = 10) -> list:
    """
    Find and return the currently idle CPU cores.

    Parameters:
        cpu_usage_thresh (int): The minimum CPU usage threshold to consider a core as idle. Default is 10.

    Returns:
        List[int]: A list of indexes of the idle cores.

    """
    idle_cores = []
    cpu_percentages = psutil.cpu_percent(interval=1, percpu=True)
    logger_perf.debug(f"cpu_percentages: {cpu_percentages}")
    for core, usage in enumerate(cpu_percentages):
        if usage < cpu_usage_thresh:
            idle_cores.append(core)
    return idle_cores


def get_current_pid() -> int:
    """
    Return the Process ID (PID) of the current process.

    Returns:
        int: The Process ID (PID) of the current process.

    """
    return os.getpid()


def set_process_affinity(pid, cores, num_cores):
    """
    Set CPU core affinity for a specific process.

    Parameters:
        pid (int): The Process ID (PID) of the process to set core affinity.
        cores (List[int]): A list of indexes of CPU cores to be assigned to the process.
        num_cores (int): The maximum number of CPU cores to be assigned to the process.

    """
    logger_perf.debug(f"Cores available: {cores}")
    cores = cores[0:num_cores] if len(cores) > num_cores else cores
    logger_perf.debug(f"Cores indicated: {cores}, PID: {pid}")
    cores_str = ",".join(map(str, cores))
    command = f"taskset -p -c {cores_str} {pid}"
    os.system(command)


def set_cpu_cores(num_cores: int = 8):
    """
    Set CPU core affinity for the current process based on the specified number of CPU cores.

    Parameters:
        num_cores (int): The maximum number of CPU cores to be assigned to the process. Default is 8.

    """
    idle_cores = find_idle_cores()
    current_pid = get_current_pid()
    set_process_affinity(current_pid, idle_cores, num_cores)
