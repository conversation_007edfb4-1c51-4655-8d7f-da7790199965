import os

import hydra
from ruamel.yaml import YAM<PERSON>

from vsc.conf import root_config


class ConfigurationManager:
    _instance = None

    @staticmethod
    def merge_yaml_files(input_folder, output_file):
        yaml = YAML()
        merged_data = {}

        for filename in sorted(os.listdir(input_folder)):
            if filename.endswith(".yaml"):
                file_path = os.path.join(input_folder, filename)

                with open(file_path, "r") as file:
                    data = yaml.load(file)
                    merged_data[filename[:-5]] = data

        with open(output_file, "w") as outfile:
            yaml.dump(merged_data, outfile)

    def __new__(cls, root_config="conf"):
        if cls._instance is None:
            sub_configs = os.path.join(root_config, "sub_configs")
            output_file = os.path.join(root_config, "default.yaml")
            config_name = os.path.basename(output_file)[:-5]
            config_dir = os.path.abspath(os.path.dirname(output_file))
            ConfigurationManager.merge_yaml_files(sub_configs, output_file)

            cls._instance = super(ConfigurationManager, cls).__new__(cls)
            with hydra.initialize_config_dir(version_base=None, config_dir=config_dir):
                cfg = hydra.compose(config_name=config_name)
                cls._instance.config = cfg

        return cls._instance.config


cfg = ConfigurationManager(root_config)
