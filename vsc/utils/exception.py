import functools


class NoSpaceLeftOnDeviceError(OSError):
    """Raised when there is no space left on the device."""


class CudaOutOfMemoryError(RuntimeError):
    """Raised when CUDA is out of memory"""


class ONNXRuntimeError(Exception):
    """Raised when an ONNX Runtime error occurs, such as GPU out of memory."""

    def __init__(self, message: str):
        ONNXRuntimeError.__module__ = "builtins"
        super().__init__(f"ONNX Runtime Error: {message}")


def handle_runtime_error(func):
    @functools.wraps(func)
    def handle_error_task(*args, **kwargs):
        return func(*args, **kwargs)

    return handle_error_task


class NumFrameMismatchError(Exception):
    """Raised when the number of frames in two videos do not match."""

    def __init__(self, num_frames1: int, num_frames2: int, video1_name="Video 1", video2_name="Video 2"):
        NumFrameMismatchError.__module__ = "builtins"
        super().__init__(
            f"Frame count mismatch: {video1_name} has {num_frames1} frames, "
            f"but {video2_name} has {num_frames2} frames. Please re-check!"
        )


class ModelInitialisationError(Exception):
    """Raised when the model initialisation fails."""

    def __init__(self, message: str):
        ModelInitialisationError.__module__ = "builtins"
        super().__init__(message)


class VideoError(Exception):
    def __init__(self, message: str):
        ModelInitialisationError.__module__ = "builtins"
        super().__init__(message)


class EmptySessionsMergeError(Exception):
    def __init__(self, message: str):
        EmptySessionsMergeError.__module__ = "builtins"
        super().__init__(message)


class DownloadError(Exception):
    def __init__(self, message: str):
        DownloadError.__module__ = "builtins"
        super().__init__(message)


# import pretty_errors
#
#
# pretty_errors.configure(
#     separator_character='*',
#     filename_display=pretty_errors.FILENAME_EXTENDED,
#     line_number_first=True,
#     display_link=True,
#     lines_before=5,
#     lines_after=2,
#     line_color=pretty_errors.RED + '> ' + pretty_errors.default_config.line_color,
#     code_color='  ' + pretty_errors.default_config.line_color,
#     truncate_code=True,
#     display_locals=True,
# )
