import os
from typing import Optional

import requests
from minio import Minio
from minio.error import InvalidResponseError, S3Error

from vsc.utils.config import cfg
from vsc.utils.logger import logger_system


client = Minio(
    endpoint=cfg.minio.minio_endpoint,
    access_key=cfg.minio.access_key,
    secret_key=cfg.minio.secret_key,
    secure=cfg.minio.secure,
)  # Set to False if using MinIO with HTTP instead of HTTPS


def upload(
    object_name: str,
    file_path: str,
    bucket_name: str = cfg.minio.bucket_name,
    client=client,
) -> Optional[str]:
    """
    Function upload file to MinIO server.
    Args:
        bucket_name: Name of bucket in MinIO
        object_name: Path to the uploaded file in the MinIO
        file_path: Path to the file in the local (file upload)

    Returns:
        public_url: URL to download file uploaded

    """
    if not client.bucket_exists(bucket_name):
        client.make_bucket(bucket_name)
    try:
        client.fput_object(bucket_name, object_name, file_path, metadata={"x-amz-acl": "public-read"})
        public_url = client.presigned_get_object(bucket_name, object_name)
        logger_system.info(f"✅ File '{object_name}' uploaded to bucket '{bucket_name}' with public access.")
        public_url = public_url.split("?X-Amz-Algorithm=")[0]
        return public_url
    except InvalidResponseError as err:
        logger_system.error(f"❌ Error uploading the file: {err}")
        return None


def is_downloadable(url: str) -> bool:
    """
    Checks whether a URL is downloadable or not.

    Args:
        url (str): The URL address to check.

    Returns:
        bool: True if the resource at the URL is available for download, False otherwise.

    """
    try:
        response = requests.head(url)
        return response.status_code == 200
    except requests.RequestException:
        return False


def upload_directory(local_path: str, minio_path: str) -> list:
    """
    Upload all files in the local directory to the specified MinIO path.

    Args:
        local_path (str): The local directory path containing files to upload.
        minio_path (str): The MinIO path where files will be uploaded.

    Returns:
        list: A list of URLs corresponding to the uploaded files.

    Note:
        This function recursively traverses the local directory and uploads each file to the MinIO server
        at the specified path. It returns a list of URLs corresponding to the uploaded files.
    """
    urls_list = []
    for root, _, files in os.walk(local_path):
        for file in files:
            local_file_path = os.path.join(root, file)
            minio_file_path = os.path.join(minio_path, os.path.relpath(local_file_path, local_path))
            url = upload(minio_file_path, local_file_path)
            urls_list.append(url)
    return urls_list


def delete_object_in_minio(
    bucket_name: str = cfg.minio.bucket_name, folder_prefix: str = "timi/generatevideoapi/checkpoints/leds/"
):
    """
    Deletes all objects within a specified folder in a MinIO bucket and optionally deletes the bucket if it becomes empty.

    This function first checks if there is an object with the same name as the folder prefix and deletes it if it exists.
    Then, it lists and deletes all objects within the specified folder. If the bucket becomes empty after deleting the objects,
    the bucket itself is deleted.

    Args:
        bucket_name (str): The name of the MinIO bucket. Defaults to the bucket name specified in the configuration.
        folder_prefix (str): The prefix of the folder to delete objects from. Defaults to "timi/generatevideoapi/checkpoints/leds/".

    Raises:
        S3Error: If an error occurs during the MinIO operations.

    Returns:
        None
    """
    try:
        # Check if there is an object with the same name as the folder
        try:
            client.stat_object(bucket_name, folder_prefix.rstrip('/'))
            # If stat_object doesn't raise an exception, delete the object
            client.remove_object(bucket_name, folder_prefix.rstrip('/'))
            print(f"Deleted object '{folder_prefix.rstrip('/')}' from bucket '{bucket_name}'.")
        except S3Error as stat_err:
            if stat_err.code != 'NoSuchKey':
                raise

        # List all objects within the folder
        objects = client.list_objects(bucket_name, prefix=folder_prefix, recursive=True)

        # Delete each object in the folder
        for obj in objects:
            client.remove_object(bucket_name, obj.object_name)
            print(f"Deleted object '{obj.object_name}' from bucket '{bucket_name}'.")

        # Optionally, check if the bucket is now empty and delete the bucket
        remaining_objects = list(client.list_objects(bucket_name, recursive=True))
        if not remaining_objects:
            client.remove_bucket(bucket_name)
            print(f"Bucket '{bucket_name}' has been deleted.")
        else:
            print(f"Bucket '{bucket_name}' is not empty and contains other objects.")

    except S3Error as e:
        print(f"Error occurred: {e}")
