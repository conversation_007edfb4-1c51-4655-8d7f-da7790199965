# Reference: https://gitlab.ftech.ai/-/ide/project/computer-vision/projects/timi/generatevideotool-api/edit/master/-/utils/config.py
import logging
import os
import warnings
from typing import Optional

from vsc.utils import cfg
from vsc.utils.color import COLOR
from vsc.utils.folder_utils import create_date_path
from vsc.utils.package_helpers import create_temp_package_path


class CustomWarning(UserWarning):
    def __init__(self, message, level):
        super().__init__(message)
        self.level = level


class WarningHandler(logging.Handler):
    def emit(self, record):
        warnings.warn(CustomWarning(record.getMessage(), record.levelname))


class CustomFormatter(logging.Formatter):
    def __init__(self, format_log, format_log_timer):
        super().__init__()
        self.format_log = format_log
        self.format_log_timer = format_log_timer

    def select_format(self, format_type):
        return {
            logging.DEBUG: COLOR.LIGHTBLUE + format_type + COLOR.NOCOLOR,
            logging.INFO: COLOR.LIGHTCYAN + format_type + COLOR.NOCOLOR,
            logging.WARNING: COLOR.YELLOW + format_type + COLOR.NOCOLOR,
            logging.ERROR: COLOR.RED + format_type + COLOR.NOCOLOR,
            logging.CRITICAL: COLOR.CYAN + format_type + COLOR.NOCOLOR,
        }

    def format(self, record):
        format_type = self.format_log_timer if "_timer.py" in record.pathname else self.format_log
        FORMATS = self.select_format(format_type)
        log_fmt = FORMATS.get(record.levelno)
        formatter = logging.Formatter(log_fmt)
        return formatter.format(record)


class Logger:
    def __init__(
        self,
        name: str = "generatevideotool-api",
        level: int = logging.DEBUG,
        path_file: Optional[str] = None,
        root_log: str = "logs",
        format_log: str = "%(asctime)s - %(levelname)s - [in %(pathname)s:%(lineno)d] - %(message)s",
        format_log_timer: str = "%(asctime)s - %(levelname)s - [Timer] - %(message)s",
    ):
        """Logger in json format that writes to a file and console.

        Args:
            name (str): Name of the logger.
            level (str): Level of the logger.
            path_file (str): Path to the log file.
            format_log (str): Format of the log record.

        Attributes:
            logger (logging.Logger): Logger object.

        """
        self.name = name
        self.level = level
        self.path_file = path_file
        self.root_log = create_temp_package_path(root_log)

        self.format_log = format_log
        self.format_log_timer = format_log_timer
        self.format = CustomFormatter(self.format_log, self.format_log_timer)

        self.logger = logging.getLogger(self.name)
        self.configure()

    def create_log_file(self):
        self.root_log = os.path.join(self.root_log, create_date_path())
        self.root_log = create_temp_package_path(self.root_log)
        os.makedirs(self.root_log, exist_ok=True)
        return os.path.join(self.root_log, self.path_file)

    def configure(self):
        """Configures the logger."""
        # Remove Handlers if it exist to handle duplicate log
        if self.logger.hasHandlers():
            self.logger.handlers.clear()

        if self.logger.level == 0 or self.level < self.logger.level:
            self.logger.setLevel(self.level)

        if len(self.logger.handlers) == 0:
            handler = logging.StreamHandler()
            handler.setLevel(self.level)
            handler.setFormatter(self.format)
            self.logger.addHandler(handler)

            # Add WarningHandler
            warning_handler = WarningHandler()
            warning_handler.setLevel(logging.WARNING)
            self.logger.addHandler(warning_handler)

        if self.path_file is not None:
            self.path_file = self.create_log_file()
            path_file_handler = logging.FileHandler(self.path_file)
            path_file_handler.setLevel(self.level)
            path_file_handler.setFormatter(logging.Formatter(self.format_log))
            self.logger.addHandler(path_file_handler)

        self.logger.propagate = False


# Init logger
API_NAME = cfg.led.api_name
logger = Logger(name=API_NAME, path_file=f"{API_NAME}.log").logger
logger_debug = Logger(name=API_NAME + "debug", path_file=f"{API_NAME}_debug.log").logger
logger_system = Logger(name="system", path_file="system.log").logger
logger_perf = Logger(name="performance", path_file=f"{API_NAME}_perf.log", level=logging.INFO).logger
