api_name: led_composition

# Model detection for corner, screen
detector:
  checkpoint_id: https://minio.dev.ftech.ai/cvdata/timi/VSC/checkpoints/led/v2.3.1.onnx
  checkpoint_path: v2.3.1.onnx
  size: 640
  iou_threshold: 0.5
  conf: 0.1
  classes: [corners]

# Model detection for face
face_detector:
  checkpoint_id: https://minio.dev.ftech.ai/cvdata/timi/VSC/checkpoints/led/scrfd_500.onnx
  checkpoint_path: led/scrfd_500.onnx
  input_size: [320, 320]
  nms_thresh: 0.4
  expand: 2.5

corner_classification:
  checkpoint_id: https://minio.dev.ftech.ai/cvdata/timi/VSC/checkpoints/led/CornerClassification_v1.12.0.onnx
  checkpoint_path: CornerClassification.onnx
  hash_code: "c77c5a0b8fdb16e73a30bf8155e561b6"
  coordinate_stretch: 10
  patch_ratio: 0.3

# Model RVM
rvm:
  backbone: mobilenetv3 # [mobilenetv3, resnet50]
  checkpoint_path: ${.storage.${.backbone}.checkpoint_path}
  checkpoint_id: ${.storage.${.backbone}.checkpoint_id}
  storage:
    mobilenetv3:
      checkpoint_path: rvm_mobilenetv3_fp32.onnx
      checkpoint_id: https://minio.dev.ftech.ai/cvdata/timi/VSC/checkpoints/led/rvm_mobilenetv3_fp32.onnx

    resnet50:
      checkpoint_path: rvm_resnet50_fp32.onnx
      checkpoint_id: https://minio.dev.ftech.ai/cvdata/timi/VSC/checkpoints/led/rvm_resnet50_fp32.onnx

vc2:
  checkpoints:
    timi_detection:
      checkpoint_id: http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/last_v5.pt
      hash_code: "af5b9c6a030373f6d77215d56750fdb4"
    matte_detection:
      checkpoint_id: http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/BiRefNet-matting-epoch_100_full_fp16.trt
      hash_code: "d93869d9d5b0581cc5bbf0dbda6d4537"

# Model Screen detector
screen_detector:
  checkpoint_id: http://minio.dev.ftech.ai/vsc-corner-detection-0.0.0-6ad8bd7a/screen_detection_v3.pt
  threshold: 0.8

status_filename: .status
download_success_msg: download complete
download_in_progress_msg: download in progress

model_matting: vc2 # [vc2, rvm]

# Composite
composite:
  mode: cupy # [cupy, none]

# Smoothing coordinates
smoothing:
  savgol_window_size_handle_outliers: 5
  savgol_window_size: 75
  savgol_polyorder: 1
  savgol_padding_mode: "interp"
  butterworth_order: 2
  butterworth_cutoff_frequency: 0.14
  gaussian_sigma: 0.15
  gaussian_padding_mode: "nearest"

# Stabilize coordinates
stabilize:
  window_size: 30
  std_thresh: 5
  distance_threshold: 10  # This value should be equal to smoothing.distance_threshold value for consistency
  screen_moving_threshold: 4e-2

# Post processing for mask screen
mask:
  color_range: [[43, 50, 57], [80, 255, 255]]
  smoothing: 31
  dilate: 1
  blur: 3
  erode: 2
  low_epsilon: 0.003
  sigma_s: 5
  sigma_r: 0.1
  pyramid_iters: 1
  use_edge_preserving: False
  bbox_stretch: 10
  timi_matte_threshold_for_removing_noise: 0.1   # Threshold for removing noise if Timi touches the mask (Not very sensitive) [0 -> 1]

matte:
  expand: 11
  matte_appearance_threshold: 0.0011 # The relative threshold to determine if Timi shows up in the frame or not to run corner validation function

post_processing:
  expansion_factor: 0.03  # [0., 1.]
  stable_threshold: 20
  validation_threshold: 27
  distance_noise: 17
  green_screen_dimension_threshold: 50

dummy_value: 0
angle_distortion_threshold: 90

# Conserve foreground
conserve_fg:
  alpha_thresh_refine: 0.25
  alpha_factor_refine: 0.5

double_check:
  rule_1:
    threshold: 15
  rule_2:
    threshold: 20
  rule_3:
    threshold: -2.828 # How much noise allowed, smaller -> more noise allowed

handle_shaking:
  threshold: 0.04

corner_validation:
  verification_model: "classification"    # ["detection", "classification"]
