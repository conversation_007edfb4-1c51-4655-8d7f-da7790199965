import gc
import os
from typing import List, Optional

import numpy as np
import torch
from fvutils.minio import download
from ultralytics import YOLO

from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.yolo_utils import convert_xyxy_to_xywh


class YOLOScreenDetectorInference(BaseModel):
    """
    A class for performing screen detection using YOLO model.

    Handles model initialization, inference and post-processing of YOLO detections.
    Inherits from BaseModel.
    """

    def __init__(self, checkpoint_id: str, weights_folder: str, threshold: float, device: str):
        """
        Initialize the YOLO screen detector.

        Args:
            checkpoint_id (str): ID of the model checkpoint to load
            weights_folder (str): Path to folder containing model weights
            threshold (float): Confidence threshold for detections
            device (str): Device to run inference on (e.g. 'cuda', 'cpu')
        """
        super(YOLOScreenDetectorInference, self).__init__()

        self.__checkpoint_id = checkpoint_id
        self.__weights_folder = weights_folder
        self.__threshold = threshold
        self.__device = device

        self.__checkpoint_path: str = self._download()

        self.__model: YOLO = self._init_model()
        self.__model.to(self.__device)
        self.n_frame = 0

    def _init_model(self) -> YOLO:
        """
        Initialize the YOLO model.

        Returns:
            YOLO: Initialized YOLO model instance
        """
        if not os.path.exists(self.__checkpoint_path):
            self.__checkpoint_path = self._download()
        return YOLO(self.__checkpoint_path)

    def _download(self) -> str:
        """
        Download model checkpoint if not present locally.

        Returns:
            str: Path to downloaded checkpoint file
        """
        os.makedirs(self.__weights_folder, exist_ok=True)
        checkpoint_path = download(self.__checkpoint_id, self.__weights_folder)
        return checkpoint_path

    def _preprocess(self, inputs: List[np.ndarray]) -> List[np.ndarray]:
        """
        Preprocess input images before inference.

        Args:
            inputs (List[np.ndarray]): List of input images as numpy arrays

        Returns:
            List[np.ndarray]: Preprocessed images
        """
        return inputs

    def _inference(self, processed_inputs: List[np.ndarray]) -> List:
        """
        Run YOLO inference on preprocessed images.

        Args:
            processed_inputs (List[np.ndarray]): List of preprocessed images

        Returns:
            List: YOLO model outputs
        """
        with torch.no_grad():
            outputs = self.__model(processed_inputs, verbose=False)

        return outputs

    def _postprocess(self, outputs: List) -> List[List[Optional[np.ndarray]]]:
        """
        Postprocess YOLO outputs to extract screen bounding boxes.

        Args:
            outputs (List): Raw outputs from YOLO model

        Returns:
            List[List[Optional[np.ndarray]]]: Processed bounding boxes for each image
        """
        results = []
        print(self.n_frame)
        self.n_frame += 1
        for output in outputs:
            result = []
            conf_list = output.boxes.conf.detach().cpu().numpy()
            xyxy_list = output.boxes.xyxy.detach().cpu().numpy()

            if len(conf_list) > 0:
                xywh_list = convert_xyxy_to_xywh(xyxy_list)

                # just get the largest bounding box
                largest_index = np.argmax([xywh[2] * xywh[3] for xywh in xywh_list])
                conf_list = [conf_list[largest_index]]
                xywh_list = [xywh_list[largest_index]]
                print(f"{conf_list}")
                for conf, xywh in zip(conf_list, xywh_list):
                    if conf > self.__threshold:
                        result.append(xywh)
            results.append(result)
        return results

    def run(self, inputs: List[np.ndarray]) -> List[List[np.ndarray]]:
        """
        Run complete screen detection pipeline on input images.

        Args:
            inputs (List[np.ndarray]): List of BGR images with shape (H, W, C)

        Returns:
            List[List[np.ndarray]]: Detected screen bounding boxes for each image
        """
        processed_inputs = self._preprocess(inputs)
        outputs = self._inference(processed_inputs)
        results = self._postprocess(outputs)
        return results

    def release(self, *args, **kwargs):
        """Release model resources and clean up memory."""
        del self.__model
        gc.collect()
