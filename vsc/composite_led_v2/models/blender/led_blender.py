import gc
from concurrent.futures import Thr<PERSON><PERSON>oolExecutor

import numpy as np
import torch

from vsc.composite_led_v2.backbones.gauss_filter.GaussFilter import (
    GaussFilter,
    guided_filter2d_color,
)
from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.blend_utils import fill_led_1_pytorch
from vsc.composite_led_v2.utils.corner_utils import extract_bbox_from_contour
from vsc.utils.logger import logger


class LedBlender(BaseModel):
    """
    A class that handles LED blending operations using GPU acceleration.

    This class implements methods to blend LED content with video frames using
    guided filtering and CUDA-based processing for optimal performance.

    Args:
        device (str): The device to run computations on ('cuda' or 'cpu')
    """

    def __init__(self, device: str):
        super().__init__()
        self.__device = device
        self.gauss_filter = GaussFilter((3, 3), 0, device=self.__device)

    def _init_model(self, *args, **kwargs):
        """Initialize the model components."""
        pass

    def _download(self, *args, **kwargs):
        """Download required model files."""
        pass

    def _preprocess(self, *args, **kwargs):
        """Preprocess input data."""
        pass

    def _inference(self, *args, **kwargs):
        """Run model inference."""
        pass

    def _postprocess(self, *args, **kwargs):
        """Post-process model outputs."""
        pass

    def run(
        self,
        frame: np.ndarray,
        fittest_corners: np.ndarray,  # for bounding box
        corners: np.ndarray,
        led: np.ndarray,
        screen_mask: np.ndarray,
    ) -> np.ndarray:
        """
        Run the LED blending operation on a single frame.

        Args:
            frame (np.ndarray): Input video frame
            fittest_corners (np.ndarray): Corner points for bounding box
            corners (np.ndarray): Corner points for LED placement
            led (np.ndarray): LED content to blend
            screen_mask (np.ndarray): Mask for the screen area

        Returns:
            np.ndarray: Blended output frame
        """
        try:  # TODO: try-except for always return output
            x1, y1, x2, y2 = extract_bbox_from_contour(
                fittest_corners.astype(np.int32), frame.shape[1], frame.shape[0], 0, True
            )
            x2 = x2 + 1  # TODO: maybe for fix bug when no screen
            y2 = y2 + 1  # TODO: maybe for fix bug when no screen
            frame_patch = frame[y1:y2, x1:x2, :]
            mask_patch = screen_mask[y1:y2, x1:x2]
            patch_corners = corners - np.array([x1, y1])
            with torch.no_grad():
                output = self.blending_led_gpu(
                    mask_patch=mask_patch, frame_patch=frame_patch, led=led, composition_coordinates=patch_corners
                )

            result = frame.copy()
            result[y1:y2, x1:x2, :] = output
            return result
        except Exception as e:
            logger.warn(f"Error occurred during LED blending, skip and use the original frame: {e}")
            return frame.copy()

    def blending_led_gpu(
        self, mask_patch: np.ndarray, frame_patch: np.ndarray, led: np.ndarray, composition_coordinates: np.ndarray
    ) -> np.ndarray:
        """
        Perform GPU-accelerated LED blending with guided filtering.

        Args:
            mask_patch (np.ndarray): The mask patch for matting
            frame_patch (np.ndarray): The frame patch to process
            led (np.ndarray): The LED image to blend
            composition_coordinates (np.ndarray): The coordinates for LED placement in the frame_patch

        Returns:
            np.ndarray: The final blended output image as a NumPy array
        """
        # Create CUDA streams for asynchronous operations
        stream1 = torch.cuda.Stream()
        stream2 = torch.cuda.Stream()
        stream3 = torch.cuda.Stream()
        stream4 = torch.cuda.Stream()

        def async_transfer(data, stream, dtype=torch.float32):
            with torch.cuda.stream(stream):
                return torch.from_numpy(data).to(device=self.__device, dtype=dtype, non_blocking=True)

        # Multi-threaded transfer using ThreadPoolExecutor
        with ThreadPoolExecutor() as executor:
            future_mask = executor.submit(async_transfer, mask_patch, stream1, torch.float32)
            future_frame = executor.submit(async_transfer, frame_patch, stream2, torch.float32)
            future_led = executor.submit(async_transfer, led, stream3, torch.float32)
            future_coords = executor.submit(async_transfer, composition_coordinates, stream4, torch.float32)

            # Wait for all transfers to complete
            mask_patch = future_mask.result().unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
            frame_patch = future_frame.result().permute(2, 0, 1).unsqueeze(0)  # Channel-first format
            led = future_led.result().permute(2, 0, 1).unsqueeze(0)  # Channel-first format
            coordinates = future_coords.result()

        # Synchronize all CUDA streams
        torch.cuda.synchronize()

        # Start GPU processing
        patch_smoothed = guided_filter2d_color(self.gauss_filter(frame_patch), mask_patch, 5, 8).clamp_(0.0, 255.0).div_(255.0)
        bg_patch = frame_patch * (1 - patch_smoothed)
        led_patch = fill_led_1_pytorch(led, coordinates, patch_smoothed)

        # Combine results and transfer back to CPU
        output = (bg_patch + led_patch)[0].to(torch.uint8).permute(1, 2, 0).cpu().numpy()

        # release specific cuda tensors
        del mask_patch, frame_patch, led, coordinates, patch_smoothed, bg_patch, led_patch

        return output

    def release(self, *args, **kwargs):
        """Release GPU memory and collect garbage."""

        if hasattr(self, 'gauss_filter'):
            del self.gauss_filter

        torch.cuda.current_stream().synchronize()

        torch.cuda.empty_cache()
        gc.collect()
