from typing import List, Optional, <PERSON><PERSON>

import numpy as np

from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.screen_utils import (
    find_contour,
    find_low_epsilon_contour,
)


class ContourDetectorInference(BaseModel):
    """Handles contour detection for image masks using OpenCV.

    This class detects both regular contours and low epsilon contours from input masks.

    Args:
        low_epsilon (float): Epsilon value for approximating contours with fewer vertices.
    """

    def __init__(self, low_epsilon: float):
        """Initializes ContourDetectorInference with the given epsilon value.

        Args:
            low_epsilon (float): Epsilon value for approximating contours.
        """
        super(ContourDetectorInference, self).__init__()
        self.low_epsilon = low_epsilon

    def _init_model(self, *args, **kwargs):
        """Initializes the model (not implemented).

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        pass

    def _download(self, *args, **kwargs):
        """Downloads required model files (not implemented).

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        pass

    def _preprocess(self, *args, **kwargs):
        """Preprocesses input data (not implemented).

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        pass

    def _inference(self, *args, **kwargs):
        """Performs model inference (not implemented).

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        pass

    def _postprocess(self, *args, **kwargs):
        """Post-processes model output (not implemented).

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        pass

    def __find_contours(self, mask: np.ndarray) -> Tuple[Optional[np.ndarray], Optional[np.ndarray]]:
        """Finds both regular and low epsilon contours for a single mask.

        Args:
            mask (np.ndarray): Binary mask image.

        Returns:
            Tuple containing regular contour and low epsilon contour arrays.
        """
        contour = find_contour(mask)
        low_epsilon_contour = find_low_epsilon_contour(mask, epsilon=self.low_epsilon)
        return contour, low_epsilon_contour

    def run(self, inputs: List[np.ndarray]) -> Tuple[List[Optional[np.ndarray]], List[Optional[np.ndarray]]]:
        """Processes a list of masks to find their contours.

        Args:
            inputs (List[np.ndarray]): List of binary mask images with shape (H, W).

        Returns:
            Tuple containing two lists:
                - List of regular contours
                - List of low epsilon contours
        """
        contours_list, low_epsilon_contours_list = [], []
        for mask in inputs:
            contours, low_epsilon_contours = self.__find_contours(mask)
            contours_list.append(contours)
            low_epsilon_contours_list.append(low_epsilon_contours)

        return contours_list, low_epsilon_contours_list

    def release(self, *args, **kwargs):
        """Releases any resources held by the model (not implemented).

        Args:
            *args: Variable length argument list.
            **kwargs: Arbitrary keyword arguments.
        """
        pass
