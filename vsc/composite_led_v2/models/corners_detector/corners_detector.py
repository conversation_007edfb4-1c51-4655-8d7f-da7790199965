import numpy as np

from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.corner_utils import extract_bbox_from_contour


class CornersDetector(BaseModel):
    """
    A model class for detecting corners in a contour using bounding box and anchor points.

    This class extends BaseModel to detect corners by finding points in a contour that are
    closest to anchor points derived from a bounding box.

    Args:
        bbox_stretch (int): Amount to stretch the bounding box
        anchor_offset (int): Offset distance for anchor points from bbox corners (default: 10000)
    """

    def __init__(self, bbox_stretch: int, anchor_offset: int = 10000):
        """
        Initialize the CornersDetector with bbox stretch and anchor offset parameters.

        Args:
            bbox_stretch (int): Amount to stretch the bounding box
            anchor_offset (int): Offset distance for anchor points from bbox corners (default: 10000)
        """
        super(CornersDetector, self).__init__()
        self.__bbox_stretch = bbox_stretch
        self.__anchor_offset = anchor_offset

    def _init_model(self, *args, **kwargs):
        """Initialize the model (empty implementation)."""
        pass

    def _download(self, *args, **kwargs):
        """Download model weights/resources (empty implementation)."""
        pass

    def _preprocess(self, *args, **kwargs):
        """Preprocess input data (empty implementation)."""
        pass

    def _inference(self, *args, **kwargs):
        """Run model inference (empty implementation)."""
        pass

    def _postprocess(self, *args, **kwargs):
        """Post-process model output (empty implementation)."""
        pass

    def run(self, contours: np.ndarray, width: int, height: int) -> np.ndarray:
        """
        Detect corners from the input contours.

        Finds corner points by computing a bounding box from the contours and creating anchor points,
        then matching contour points closest to these anchors.

        Args:
            contours (np.ndarray): Input contour points
            width (int): Image width
            height (int): Image height

        Returns:
            np.ndarray: Detected corner points
        """
        x0, y0, x1, y1 = extract_bbox_from_contour(contours, width, height, self.__bbox_stretch)
        anchors = np.array(
            [
                [x0 - self.__anchor_offset, y0 - self.__anchor_offset],
                [x0 - self.__anchor_offset, y1 + self.__anchor_offset],
                [x1 + self.__anchor_offset, y1 + self.__anchor_offset],
                [x1 + self.__anchor_offset, y0 - self.__anchor_offset],
            ]
        )
        corners = contours[np.linalg.norm(contours[:, np.newaxis, :] - anchors, axis=2).argmin(0)]
        return corners

    def release(self, *args, **kwargs):
        """Release any resources used by the model (empty implementation)."""
        pass
