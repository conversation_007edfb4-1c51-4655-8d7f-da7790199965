from typing import List, Optional, <PERSON><PERSON>

import numpy as np

from vsc.composite_led_v2.datastruct import HiddenE<PERSON>, OccludedCorner
from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.corner_utils import (
    extract_bbox_from_contour,
    find_expansion_ranges,
    is_dummy_corners,
)
from vsc.composite_led_v2.utils.matting_utils import denoise, refine_mask, smooth_edge


class PostProcessor(BaseModel):
    """
    Post-processes masks and corners for screen detection.

    This class handles mask smoothing, denoising, and refinement operations on detected screens.

    Args:
        pyramid_iters (int): Number of iterations for pyramid smoothing
        use_edge_preserving (bool): Whether to use edge-preserving smoothing
        sigma_s (float): Spatial sigma parameter for smoothing
        sigma_r (float): Range sigma parameter for smoothing
        bbox_stretch (int): Amount to stretch bounding box by
        erode_kernel (int): Size of erosion kernel
        timi_matte_removing_threshold (float): Threshold for removing timi matte
        color_range (List): Color range values for processing
    """

    def __init__(
        self,
        pyramid_iters: int,
        use_edge_preserving: bool,
        sigma_s: float,
        sigma_r: float,
        bbox_stretch: int,
        erode_kernel: int,
        timi_matte_removing_threshold: float,
        color_range: List,
    ):
        super(BaseModel, self).__init__()
        self.__pyramid_iters = pyramid_iters
        self.__use_edge_preserving = use_edge_preserving
        self.__sigma_s = sigma_s
        self.__sigma_r = sigma_r
        self.__bbox_stretch = bbox_stretch
        self.__erode_kernel = erode_kernel
        self.__timi_matte_removing_threshold = timi_matte_removing_threshold
        self.__color_range = color_range

    def _init_model(self, *args, **kwargs):
        """Initializes the post-processing model."""
        pass

    def _download(self, *args, **kwargs):
        """Downloads required model files."""
        pass

    def _preprocess(self, *args, **kwargs):
        """Preprocesses input data."""
        pass

    def _inference(self, *args, **kwargs):
        """Performs model inference."""
        pass

    def _postprocess(self, *args, **kwargs):
        """Post-processes model outputs."""
        pass

    def run(self, *args, **kwargs):
        """Runs the complete post-processing pipeline."""
        pass

    def extract_expansion(
        self,
        contour: np.ndarray,
        corners: np.ndarray,
        hidden_edge: Optional[HiddenEdge],
        occluded_corner: OccludedCorner,
        width: int,
        height: int,
    ) -> Tuple[np.ndarray, float, float]:
        """
        Extracts expansion mask and ranges from corners and contour.

        Args:
            contour: Array of contour points
            corners: Array of corner points
            hidden_edge: Hidden edge enumeration value
            occluded_corner: Occluded corner enumeration value
            width: Image width
            height: Image height

        Returns:
            Tuple containing:
                - Mask array
                - Expansion width
                - Expansion height
        """
        if not is_dummy_corners(corners):
            fittest_corners, expansion_width, expansion_height = find_expansion_ranges(corners, contour)
            if occluded_corner is not None:
                x1, y1, x2, y2 = extract_bbox_from_contour(contour, width, height, stretch=0, clamp_option=False)
                mask = np.array([[x1, y1], [x1, y2], [x2, y2], [x2, y1]], dtype=np.int32)
            else:
                mask = fittest_corners.astype(np.int32)
        else:
            mask = corners
            expansion_width = expansion_height = 1.0  # TODO: the dummy value for expansion is 1?
        return mask, expansion_width, expansion_height

    def expand_corners(self, corners: np.ndarray, expansion_range: Tuple[float, float]) -> np.ndarray:
        """
        Expands corner points by given ranges.

        Args:
            corners: Array of corner points
            expansion_range: Tuple of width and height expansion factors

        Returns:
            Expanded corner points array
        """
        factor_matrix = np.array([[-1, -1], [-1, 1], [1, 1], [1, -1]]) * np.array(expansion_range)
        return factor_matrix + corners

    def post_process_mask(
        self,
        screen_mask: np.ndarray,
        frame: np.ndarray,
        fittest_corners: np.ndarray,
        timi_matte: np.ndarray,
        screen_box: np.ndarray,
    ) -> np.ndarray:
        """
        Post-processes the screen mask through smoothing, denoising, and refinement.

        Args:
            screen_mask: Binary mask of detected screen
            frame: Input image frame
            fittest_corners: Array of fitted corner points
            timi_matte: Timi matting mask
            screen_box: Screen bounding box coordinates

        Returns:
            Post-processed screen mask
        """
        x0, y0, x1, y1 = screen_box
        cropped_mask = screen_mask[y0:y1, x0:x1]
        cropped_frame = frame[y0:y1, x0:x1]
        cropped_matte = timi_matte[y0:y1, x0:x1]
        cropped_fittest_corners = fittest_corners - np.array([x0, y0])

        cropped_mask = smooth_edge(
            mask=cropped_mask,
            frame=cropped_frame,
            iters=self.__pyramid_iters,
            use_edge_preserving=self.__use_edge_preserving,
            sigma_s=self.__sigma_s,
            sigma_r=self.__sigma_r,
            bbox_stretch=self.__bbox_stretch,
            color_range=self.__color_range,
        )

        cropped_denoised_mask = denoise(
            mask=cropped_mask,
            corners=cropped_fittest_corners,
            timi_matte=cropped_matte,
            kernel_erode=self.__erode_kernel,
            timi_matte_removing_threshold=self.__timi_matte_removing_threshold,
        )

        cropped_refined_mask = refine_mask(
            mask_smoothed=cropped_mask,
            mask_origin=cropped_denoised_mask,
            frame=cropped_frame,
            corners=cropped_fittest_corners,
            timi_matte=cropped_matte,
            timi_matte_removing_threshold=self.__timi_matte_removing_threshold,
            color_range=self.__color_range,
        )

        output_mask = screen_mask.copy()
        output_mask[y0:y1, x0:x1] = cropped_refined_mask

        return output_mask

    def release(self, *args, **kwargs):
        """Releases any allocated resources."""
        pass
