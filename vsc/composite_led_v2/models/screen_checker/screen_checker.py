from typing import List

import numpy as np

from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.screen_utils import contour_static_check


class ScreenChecker(BaseModel):
    """
    A class to check screen detection results based on masks and contours.

    Inherits from BaseModel and provides methods for validating screen existence
    and checking if detected screens are static.
    """

    def __init__(self, mask_area_threshold: float):
        """
        Initialize the ScreenChecker.

        Args:
            mask_area_threshold (float): Threshold ratio for minimum mask area
        """
        super(<PERSON><PERSON><PERSON><PERSON>, self).__init__()

        self.__dummy_bbox = [0, 0, 0, 0]
        self.__mask_area_threshold = mask_area_threshold

    def _init_model(self):
        """Initialize model parameters (not used in this implementation)."""
        pass

    def _download(self):
        """Download model files (not used in this implementation)."""
        pass

    def _preprocess(self):
        """Preprocess input data (not used in this implementation)."""
        pass

    def _inference(self):
        """Run model inference (not used in this implementation)."""
        pass

    def _postprocess(self):
        """Post-process model outputs (not used in this implementation)."""
        pass

    def run(self):
        """Execute the full model pipeline (not used in this implementation)."""
        pass

    def release(self):
        """Release model resources (not used in this implementation)."""
        pass

    def static_check(self, contour_list: List[np.ndarray]) -> bool:
        """
        Check if detected screens are static based on contours.

        Args:
            contour_list (List[np.ndarray]): List of screen contours to check

        Returns:
            bool: True if screens are static, False otherwise
        """
        return contour_static_check(contour_list, self.__dummy_bbox)

    def exist_screen(self, mask: np.ndarray, contour: np.ndarray) -> bool:
        """
        Check if a valid screen exists based on mask area and contour.

        Args:
            mask (np.ndarray): Binary mask of detected screen
            contour (np.ndarray): Contour points of detected screen

        Returns:
            bool: True if valid screen exists, False otherwise
        """
        area_condition = (mask.sum() / (mask.shape[0] * mask.shape[1])) > self.__mask_area_threshold
        contour_condition = contour is not None and len(contour) > 3
        return area_condition and contour_condition
