from typing import List, Optional

import numpy as np

from vsc.composite_led_v2.datastruct import HiddenEdge
from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.smooth_utils import (
    find_hidden_edge_corners,
    replace_dummy_corners,
    run_cma_smoothing,
    run_filters,
    stabilize_corners,
)


class CornersSmoother(BaseModel):
    """
    Smooths corner coordinates in video frames to reduce jitter and stabilize corners.

    This class applies multiple filtering techniques including Savitzky-<PERSON><PERSON>, Butterworth,
    and Gaussian filters to smooth detected corner positions across video frames.

    Args:
        handle_shaking_threshold (float): Threshold for handling camera shake
        screen_moving_threshold (float): Threshold for detecting screen movement
        angle_distortion_threshold (float): Threshold for angle distortion
        window_size (int): Size of sliding window for corner stabilization
        std_threshold (int): Standard deviation threshold for corner stability
        distance_threshold (int): Maximum allowed distance between consecutive corner positions
        savgol_window_size_handle_outliers (int): Window size for Savitzky-<PERSON>lay filter outlier handling
        savgol_window_size (int): Window size for Sa<PERSON>tzky-<PERSON>lay smoothing
        savgol_poly_order (int): Polynomial order for Savitzky-Golay filter
        savgol_padding_mode (str): Padding mode for Savitzky-Golay filter
        butterworth_order (int): Order of Butterworth filter
        butterworth_cutoff_frequency (float): Cutoff frequency for Butterworth filter
        gaussian_sigma (float): Standard deviation for Gaussian filter
        gaussian_padding_mode (str): Padding mode for Gaussian filter
    """

    def __init__(
        self,
        handle_shaking_threshold: float,
        screen_moving_threshold: float,
        angle_distortion_threshold: float,
        window_size: int,
        std_threshold: int,
        distance_threshold: int,
        savgol_window_size_handle_outliers: int,
        savgol_window_size: int,
        savgol_poly_order: int,
        savgol_padding_mode: str,
        butterworth_order: int,
        butterworth_cutoff_frequency: float,
        gaussian_sigma: float,
        gaussian_padding_mode: str,
    ):
        super(CornersSmoother, self).__init__()
        self.__handle_shaking_threshold = handle_shaking_threshold
        self.__screen_moving_threshold = screen_moving_threshold
        self.__angle_distortion_threshold = angle_distortion_threshold

        # for stabilizing corners
        self.__window_size = window_size
        self.__std_threshold = std_threshold
        self.__distance_threshold = distance_threshold

        # for removing outliers & CMA smoothing
        self.__savgol_window_size_handle_outliers = savgol_window_size_handle_outliers
        self.__savgol_window_size = savgol_window_size
        self.__savgol_poly_order = savgol_poly_order
        self.__savgol_padding_mode = savgol_padding_mode
        self.__butterworth_order = butterworth_order
        self.__butterworth_cutoff_frequency = butterworth_cutoff_frequency
        self.__gaussian_sigma = gaussian_sigma
        self.__gaussian_padding_mode = gaussian_padding_mode

    def _init_model(self, *args, **kwargs):
        """
        Initializes the corner smoothing model.

        This method is required by BaseModel but not used in this implementation.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        pass

    def _download(self, *args, **kwargs):
        """
        Downloads required model files.

        This method is required by BaseModel but not used in this implementation.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        pass

    def _preprocess(self, *args, **kwargs):
        """
        Preprocesses input data before smoothing.

        This method is required by BaseModel but not used in this implementation.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        pass

    def _inference(self, *args, **kwargs):
        """
        Performs model inference.

        This method is required by BaseModel but not used in this implementation.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        pass

    def _postprocess(self, *args, **kwargs):
        """
        Postprocesses model output.

        This method is required by BaseModel but not used in this implementation.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        pass

    def run(self, list_corners: np.ndarray, list_hidden_edge: List[Optional[HiddenEdge]], width: int) -> np.ndarray:
        """
        Runs the corner smoothing pipeline on input corner coordinates.

        This method applies multiple filtering and smoothing techniques to stabilize corner
        positions across video frames.

        Args:
            list_corners (np.ndarray): Array of corner coordinates
            list_hidden_edge (List[Optional[HiddenEdge]]): List of hidden edge indicators
            width (int): Width of the video frame

        Returns:
            np.ndarray: Smoothed corner coordinates
        """
        list_corners = list_corners.astype(np.float32)

        # replace dummy value using mean
        list_corners = replace_dummy_corners(list_corners)

        # TODO: if missing ONLY one point and ZOOMED orientation, recalculate missing corner

        # process hidden edge
        list_corners = find_hidden_edge_corners(list_corners, list_hidden_edge)

        # stabilize corners using std and distance
        list_corners = stabilize_corners(
            list_corners,
            window_size=self.__window_size,
            std_threshold=self.__std_threshold,
            distance_threshold=self.__distance_threshold,
        )

        # remove outlier using savgol, butter and gauss
        list_corners = run_filters(
            list_corners,
            savgol_window_length=self.__savgol_window_size_handle_outliers,
            savgol_poly_order=self.__savgol_poly_order,
            savgol_padding_mode=self.__savgol_padding_mode,
            butter_order=self.__butterworth_order,
            butter_cutoff_freq=self.__butterworth_cutoff_frequency,
            gauss_sigma=self.__gaussian_sigma,
            gauss_padding_mode=self.__gaussian_padding_mode,
        )

        # if ZOOMED orientation, smooth corners using savgol, butter and gauss
        list_corners = run_cma_smoothing(
            list_corners,
            savgol_window_length=self.__savgol_window_size,
            savgol_poly_order=self.__savgol_poly_order,
            savgol_padding_mode=self.__savgol_padding_mode,
            butter_order=self.__butterworth_order,
            butter_cutoff_freq=self.__butterworth_cutoff_frequency,
            gauss_sigma=self.__gaussian_sigma,
            gauss_padding_mode=self.__gaussian_padding_mode,
            moving_screen_threshold=self.__screen_moving_threshold,
            width=width,
        )

        return list_corners

    def release(self, *args, **kwargs):
        """
        Releases any resources used by the model.

        This method is required by BaseModel but not used in this implementation.

        Args:
            *args: Variable length argument list
            **kwargs: Arbitrary keyword arguments
        """
        pass
