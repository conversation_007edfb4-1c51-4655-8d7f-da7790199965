from abc import abstractmethod

from vsc.utils.logger import logger


class BaseModel:
    """Abstract base class for machine learning model implementations.

    Defines standard methods required for model initialization, inference,
    and resource management. All model classes should inherit from this base.
    """

    def __init__(self):
        """Initializes model with logger instance."""
        self._logger = logger

    @abstractmethod
    def _init_model(self, *args, **kwargs):
        """Initializes model architecture and parameters."""
        pass

    @abstractmethod
    def _download(self, *args, **kwargs):
        """Downloads model weights and assets."""
        pass

    @abstractmethod
    def _preprocess(self, *args, **kwargs):
        """Preprocesses input data."""
        pass

    @abstractmethod
    def _inference(self, *args, **kwargs):
        """Runs model inference."""
        pass

    @abstractmethod
    def _postprocess(self, *args, **kwargs):
        """Processes model outputs."""
        pass

    @abstractmethod
    def run(self, *args, **kwargs):
        """Executes complete model pipeline."""
        pass

    @abstractmethod
    def release(self, *args, **kwargs):
        """Releases model resources."""
        pass
