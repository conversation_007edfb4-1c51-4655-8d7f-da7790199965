import gc
from typing import Dict, List, Optional, Tuple

import numpy as np
import torch

from vsc.composite_led_v2.backbones.vc2.vc2_matting import VC2
from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.yolo_utils import expand_bbox


class VC2Inference(BaseModel):
    """
    A class for performing matting inference using the VC2 model.

    Handles preprocessing of input images, model inference, and postprocessing of outputs.
    Inherits from BaseModel.
    """

    def __init__(self, checkpoints: Dict, weights_folder, device):
        """
        Initialize the VC2Inference model.

        Args:
            checkpoints (Dict): Model checkpoint configurations
            weights_folder: Path to model weights folder
            device: Device to run inference on (e.g. 'cuda', 'cpu')
        """
        super(VC2Inference, self).__init__()
        self.__checkpoints = checkpoints
        self.__weights_folder = weights_folder
        self.__device = device
        self.__bounding_box_scale = 0.2

        self.__model: VC2 = self._init_model()

        self.__previous_bbox: Optional[Tuple] = None

    def _init_model(self):
        """
        Initialize the VC2 model with the specified configurations.

        Returns:
            VC2: Initialized VC2 model instance
        """
        model = VC2(checkpoints=self.__checkpoints, weights_folder=self.__weights_folder, device=self.__device)
        return model

    def _download(self):
        """Download required model files if needed."""
        pass

    def _preprocess(
        self, inputs: List[np.ndarray], bboxes: List[List[np.ndarray]]
    ) -> Tuple[List[List[np.ndarray]], List[List[np.ndarray]], List[Tuple[int, int]]]:
        """
        Preprocess input images by cropping based on bounding boxes.

        Args:
            inputs: List of BGR images as numpy arrays with shape (H, W, C)
            bboxes: List of bounding box coordinates for each image

        Returns:
            Tuple containing:
                - List of cropped image patches
                - List of processed bounding boxes
                - List of original image sizes
        """
        cropped_images, processed_bboxes, sizes = [], [], []
        for image, bbox in zip(inputs, bboxes):
            h, w, c = image.shape
            sub_cropped_images, sub_processed_bboxes = [], []
            for sub_bbox in bbox:
                # expand bounding box
                x0, y0, w_box, h_box = expand_bbox(
                    bbox=sub_bbox,
                    expand_ratio=self.__bounding_box_scale,
                    img_width=w,
                    img_height=h,
                )
                x0 = max(0, int(x0))
                y0 = max(0, int(y0))
                w_box = int(w_box)
                h_box = int(h_box)
                # crop
                sub_cropped_images.append(image[y0 : y0 + h_box, x0 : x0 + w_box])
                sub_processed_bboxes.append(np.array([x0, y0, w_box, h_box]))
            cropped_images.append(sub_cropped_images)
            processed_bboxes.append(sub_processed_bboxes)
            sizes.append((h, w))

        return cropped_images, processed_bboxes, sizes

    def _inference(self, processed_inputs: List[List[np.ndarray]]) -> List[List[np.ndarray]]:
        """
        Run inference on preprocessed image patches.

        Args:
            processed_inputs: List of preprocessed image patches

        Returns:
            List of model output predictions for each image patch
        """
        outputs = []
        with torch.no_grad():
            for image in processed_inputs:
                output = []
                for sub_image in image:
                    sub_output = self.__model.process_image(sub_image)
                    output.append(sub_output)
                outputs.append(output)

        return outputs

    def _postprocess(
        self, outputs: List[List[np.ndarray]], bboxes: List[List[np.ndarray]], sizes: List[Tuple[int, int]]
    ) -> List[np.ndarray]:
        """
        Postprocess model outputs by mapping predictions back to original image dimensions.

        Args:
            outputs: List of model predictions
            bboxes: List of bounding boxes used for cropping
            sizes: List of original image sizes

        Returns:
            List of final processed outputs as numpy arrays
        """
        results = []
        for output, bbox, size in zip(outputs, bboxes, sizes):
            result = np.zeros(size, dtype=np.uint8)
            for sub_output, sub_bbox in zip(output, bbox):
                x0, y0, w_box, h_box = sub_bbox.astype(np.int32)
                result[y0 : y0 + h_box, x0 : x0 + w_box] = (sub_output * 255).astype(np.uint8)
            results.append(result)

        return results

    def run(self, inputs: List[np.ndarray], bboxes: Optional[List[List[np.ndarray]]]) -> List[np.ndarray]:
        """
        Run the complete inference pipeline on input images.

        Args:
            inputs: List of BGR images as numpy arrays with shape (H, W, C)
            bboxes: Optional list of bounding boxes for each image. If None, uses full image

        Returns:
            List of processed outputs as numpy arrays
        """
        if bboxes is None:
            bboxes = [[np.array([0, 0, inputs[0].shape[1], inputs[0].shape[0]], dtype=np.int32)]]
        processed_images, processed_bboxes, sizes = self._preprocess(inputs, bboxes)
        outputs = self._inference(processed_images)
        results = self._postprocess(outputs, processed_bboxes, sizes)

        return results

    def release(self):
        """Release model resources and clean up memory."""
        self.__model.release()
        del self.__model
        gc.collect()
        torch.cuda.empty_cache()
