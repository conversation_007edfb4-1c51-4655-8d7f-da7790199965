import gc
import os
from typing import List, Optional

import cv2
import numpy as np
import onnxruntime as ort
import torch

from vsc.composite_led_v2.constants import DUMMY_CORNER, DUMMY_CORNERS
from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.corner_utils import (
    calculate_angle_difference_quadrilateral,
    extract_bbox_from_contour,
    extract_roi_image,
    get_nb_visible_corners,
    get_visible_corners,
)
from vsc.composite_led_v2.utils.download_utils import download_with_hash_check
from vsc.utils.exception import ONNXRuntimeError


class CornersValidator(BaseModel):
    """
    Validates corner points of detected LED screen using visual and geometric checks.

    The validator performs three types of validation:
    1. Shape validation - checks if corner angles form valid quadrilateral
    2. Visual validation - uses ML model to verify corner appearance
    3. Distance validation - verifies minimum distance between corners

    Args:
        checkpoint_id (str): ID of the ML model checkpoint
        weights_folder (str): Folder path to store model weights
        hash_code (str): Hash code to verify downloaded weights
        angle_validation_threshold (float): Minimum angle difference for validation
        larger_angle_validation_threshold (float): Threshold for cases with hidden edges
        distance_validation_threshold (float): Minimum distance between corners
        device (str): Device to run inference on ('cpu' or 'cuda')
    """

    def __init__(
        self,
        checkpoint_id: str,
        weights_folder: str,
        hash_code: str,
        angle_validation_threshold: float,
        larger_angle_validation_threshold: float,
        distance_validation_threshold: float,
        device: str,
    ):
        super(CornersValidator, self).__init__()
        self.__checkpoint_id = checkpoint_id
        self.__weights_folder = weights_folder
        self.__hash_code = hash_code

        self.__angle_validation_threshold = angle_validation_threshold
        self.__larger_angle_validation_threshold = larger_angle_validation_threshold
        self.__distance_validation_threshold = distance_validation_threshold

        self.__device = device

        self.__checkpoint_path: str = self._download()

        self.__model = self._init_model()

        self.__input_name = self.__model.get_inputs()[0].name
        self.__output_name = self.__model.get_outputs()[0].name
        self.__input_size = tuple(self.__model.get_inputs()[0].shape[-2:])

    def _init_model(self):
        """
        Initializes the ONNX model for corner classification.

        Returns:
            onnxruntime.InferenceSession: Initialized ONNX model session

        Raises:
            ONNXRuntimeError: If model initialization fails
        """
        device_type = 'cuda' if 'cuda' in self.__device else 'cpu'
        device_id = self.__device.split(':')[-1] if device_type == 'cuda' else -1

        provider = [("CUDAExecutionProvider", {"device_id": device_id})] if device_type == "cuda" else ["CPUExecutionProvider"]

        # Init the corner classification model
        try:
            model = ort.InferenceSession(self.__checkpoint_path, providers=provider)
        except RuntimeError as e:
            raise ONNXRuntimeError(f"Failed to load ONNX model on {self.__device} \nError: {str(e)}") from e

        return model

    def _download(self) -> str:
        """
        Downloads and verifies the model weights.

        Returns:
            str: Path to downloaded checkpoint file
        """
        os.makedirs(self.__weights_folder, exist_ok=True)

        checkpoint_path = download_with_hash_check(
            weight_url=self.__checkpoint_id,
            weight_path=os.path.join(self.__weights_folder, os.path.basename(self.__checkpoint_id)),
            hash_code=self.__hash_code,
        )
        return checkpoint_path

    def _preprocess(
        self,
        corners: np.ndarray,
        contour: np.ndarray,
        frame: np.ndarray,
    ) -> List[Optional[np.ndarray]]:
        """
        Preprocesses input frame and extracts ROIs around corners.

        Args:
            corners (np.ndarray): Corner coordinates
            contour (np.ndarray): LED screen contour points
            frame (np.ndarray): Input image frame

        Returns:
            List[Optional[np.ndarray]]: Preprocessed ROI images for each corner
        """
        height, width = frame.shape[:2]

        bbox = extract_bbox_from_contour(contour, width, height)

        roi_images = extract_roi_image(frame, corners, bbox)

        processed_roi_images = [
            (
                cv2.resize(roi_image, self.__input_size).transpose(2, 0, 1)[None, ...].astype(np.float32) / 255.0
                if roi_image is not None
                else None
            )
            for roi_image in roi_images
        ]
        return processed_roi_images

    def _inference(self, roi_images: List[np.ndarray]) -> List[Optional[np.ndarray]]:
        """
        Runs model inference on preprocessed ROI images.

        Args:
            roi_images (List[np.ndarray]): List of preprocessed ROI images

        Returns:
            List[Optional[np.ndarray]]: Model predictions for each ROI
        """
        predictions = []
        with torch.no_grad():
            for roi_image in roi_images:
                if roi_image is not None:
                    prediction = self.__model.run([], {self.__input_name: roi_image})[0]
                else:
                    prediction = None
                predictions.append(prediction)

        return predictions

    def _postprocess(self, corners: np.ndarray, predictions: List[Optional[np.ndarray]]) -> np.ndarray:
        """
        Postprocesses model predictions and updates corner coordinates.

        Args:
            corners (np.ndarray): Original corner coordinates
            predictions (List[Optional[np.ndarray]]): Model predictions

        Returns:
            np.ndarray: Updated corner coordinates
        """
        for i, prediction in enumerate(predictions):
            if prediction is None or not np.bitwise_not(prediction.argmax(-1).astype("bool")):
                corners[i] = DUMMY_CORNER

        return corners

    def run_shape_validation(self, corners: np.ndarray, width: int, height: int) -> bool:
        """
        Validates corner positions based on geometric constraints.

        Args:
            corners (np.ndarray): Corner coordinates
            width (int): Frame width
            height (int): Frame height

        Returns:
            bool: True if corners pass shape validation
        """
        hidden_edges = [
            corners[0][0] == corners[1][0] == 0,  # hidden left
            corners[1][1] == corners[2][1] == height - 1,  # hidden down
            corners[2][0] == corners[3][0] == width - 1,  # hidden right
            corners[3][1] == corners[0][1] == 0,  # hidden up
        ]
        allow_larger_difference = any(hidden_edges)
        angle_diff = calculate_angle_difference_quadrilateral(corners)
        if allow_larger_difference:
            return angle_diff > self.__larger_angle_validation_threshold

        return angle_diff > self.__angle_validation_threshold

    def run_visual_validation(
        self,
        corners: np.ndarray,
        contour: np.ndarray,
        frame: np.ndarray,
    ) -> np.ndarray:
        """
        Validates corners using visual appearance model.

        Args:
            corners (np.ndarray): Corner coordinates
            contour (np.ndarray): LED screen contour points
            frame (np.ndarray): Input image frame

        Returns:
            np.ndarray: Validated corner coordinates
        """
        processed_roi_images = self._preprocess(corners, contour, frame)
        predictions = self._inference(processed_roi_images)
        corners = self._postprocess(corners, predictions)
        return corners

    def run_distance_validation(self, corners: np.ndarray) -> np.ndarray:
        """
        Validates minimum distance between corners.

        Args:
            corners (np.ndarray): Corner coordinates

        Returns:
            np.ndarray: Validated corner coordinates
        """
        if get_nb_visible_corners(corners) < 3:
            return DUMMY_CORNERS
        visible_corners = get_visible_corners(corners)

        p1 = visible_corners[0]
        for p2 in visible_corners[1:]:
            distance = ((p1[0] - p2[0]) ** 2 + (p1[1] - p2[1]) ** 2) ** 0.5
            if distance > self.__distance_validation_threshold:
                return corners

        return DUMMY_CORNERS

    def run(
        self,
        corners: np.ndarray,
        contour: np.ndarray,
        frame: np.ndarray,
        matte_flag: bool,
        nb_visible_corners: int,
    ) -> np.ndarray:
        """
        Runs complete corner validation pipeline.

        Args:
            corners (np.ndarray): Corner coordinates
            contour (np.ndarray): LED screen contour points
            frame (np.ndarray): Input image frame
            matte_flag (bool): Whether input is matte LED screen
            nb_visible_corners (int): Number of visible corners

        Returns:
            np.ndarray: Final validated corner coordinates
        """
        if matte_flag:
            if nb_visible_corners == 4:
                if self.run_shape_validation(corners=corners, width=frame.shape[1], height=frame.shape[0]):
                    corners = self.run_visual_validation(corners, contour, frame)
                corners = self.run_distance_validation(corners)
            elif nb_visible_corners == 3:
                corners = self.run_distance_validation(corners)
            else:
                return DUMMY_CORNERS

            return corners
        else:
            corners = self.run_visual_validation(corners, contour, frame)
            if get_nb_visible_corners(corners) < 3:
                return DUMMY_CORNERS

            corners = self.run_distance_validation(corners)
            return corners

    def release(self, *args, **kwargs):
        """Releases allocated resources and clears GPU memory."""
        if hasattr(self, '__model'):
            del self.__model

        torch.cuda.empty_cache()
        gc.collect()
