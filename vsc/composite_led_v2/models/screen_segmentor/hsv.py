from typing import List, Optional, <PERSON><PERSON>

import cv2
import numpy as np

from vsc.composite_led_v2.models.base_model import BaseModel
from vsc.composite_led_v2.utils.screen_utils import get_mask_based_on_hsv
from vsc.composite_led_v2.utils.yolo_utils import expand_bbox


class HSVScreenSegmentorInference(BaseModel):
    """HSV-based screen segmentation inference model.

    Performs screen segmentation using HSV color space thresholding.
    Inherits from BaseModel class.
    """

    def __init__(self, color_range: List):
        """Initialize the HSV screen segmentor.

        Args:
            color_range: List of HSV color ranges for segmentation
        """
        super(HSVScreenSegmentorInference, self).__init__()
        self.__bounding_box_scale = 0.2
        self.__color_range = color_range

    def _init_model(self, *args, **kwargs):
        """Initialize model parameters (not used in this implementation)."""
        pass

    def _download(self, *args, **kwargs):
        """Download model files (not used in this implementation)."""
        pass

    def _preprocess(
        self, inputs: List[np.ndarray], bboxes: List[List[np.ndarray]]
    ) -> Tuple[List[List[np.ndarray]], List[List[np.ndarray]], List[Tuple[int, int]]]:
        """Preprocess input images by cropping regions of interest.

        Args:
            inputs: List of input images as numpy arrays
            bboxes: List of bounding box coordinates for each image

        Returns:
            Tuple containing:
                - Cropped image regions
                - Processed bounding boxes
                - Original image sizes
        """
        cropped_images, processed_bboxes, sizes = [], [], []
        for image, bbox in zip(inputs, bboxes):
            h, w, c = image.shape
            sub_cropped_images, sub_processed_bboxes = [], []
            for sub_bbox in bbox:
                # expand bounding box
                x0, y0, w_box, h_box = expand_bbox(
                    bbox=sub_bbox,
                    expand_ratio=self.__bounding_box_scale,
                    img_width=w,
                    img_height=h,
                )
                x0 = max(0, int(x0))
                y0 = max(0, int(y0))
                w_box = int(w_box)
                h_box = int(h_box)
                # crop
                sub_cropped_images.append(image[y0 : y0 + h_box, x0 : x0 + w_box])
                sub_processed_bboxes.append(np.array([x0, y0, w_box, h_box]))
            cropped_images.append(sub_cropped_images)
            processed_bboxes.append(sub_processed_bboxes)
            sizes.append((h, w))

        return cropped_images, processed_bboxes, sizes

    def _inference(self, processed_inputs: List[List[np.ndarray]]) -> List[List[np.ndarray]]:
        """Perform HSV-based segmentation on preprocessed images.

        Args:
            processed_inputs: List of preprocessed image regions

        Returns:
            List of segmentation masks for each image region
        """
        outputs = []
        for image in processed_inputs:
            output = []
            for sub_image in image:
                sub_output = get_mask_based_on_hsv(image=sub_image, color_range=self.__color_range)
                output.append(sub_output)
            outputs.append(output)

        return outputs

    def _postprocess(
        self, outputs: List[List[np.ndarray]], bboxes: List[List[np.ndarray]], sizes: List[Tuple[int, int]]
    ) -> List[np.ndarray]:
        """Postprocess segmentation masks by mapping them back to original image sizes.

        Args:
            outputs: Segmentation masks for image regions
            bboxes: Processed bounding boxes
            sizes: Original image sizes

        Returns:
            List of final segmentation masks at original image sizes
        """
        results = []
        for output, bbox, size in zip(outputs, bboxes, sizes):
            result = np.zeros(size, dtype=np.uint8)
            for sub_output, sub_bbox in zip(output, bbox):
                x0, y0, w_box, h_box = sub_bbox.astype(np.int32)
                result[y0 : y0 + h_box, x0 : x0 + w_box] = sub_output
            results.append(result)

        return results

    def run(self, inputs: List[np.ndarray], bboxes: Optional[List[List[np.ndarray]]]) -> List[np.ndarray]:
        """Run complete screen segmentation pipeline.

        Args:
            inputs: BGR images as numpy arrays with shape List[(H, W, C)]
            bboxes: Bounding boxes for regions of interest, optional

        Returns:
            List of segmentation masks for each input image
        """
        inputs = [cv2.cvtColor(image, cv2.COLOR_BGR2RGB) for image in inputs]
        if bboxes is not None:
            processed_images, processed_bboxes, sizes = self._preprocess(inputs, bboxes)
            outputs = self._inference(processed_images)
            results = self._postprocess(outputs, processed_bboxes, sizes)
        else:
            outputs = self._inference([inputs])
            results = outputs[0]
        return results

    def release(self, *args, **kwargs):
        """Release model resources (not used in this implementation)."""
        pass
