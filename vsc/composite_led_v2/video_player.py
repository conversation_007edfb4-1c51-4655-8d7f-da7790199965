import queue
import threading
import time
from abc import abstractmethod
from typing import Itera<PERSON>, Optional, Tuple

import cv2
import numpy as np
from fvutils.videoio import FVideoCapture, FVideoWriter, PixelFormat

from vsc.composite_led_v2.constants import DEFAULT_READER_QUEUE, DEFAULT_WRITER_QUEUE
from vsc.utils.exception import VideoError
from vsc.utils.logger import logger


class AsyncReader:
    """
    Asynchronous video reader class.

    This class provides a base implementation for an asynchronous video
    reader that uses a queue to store video frames and a separate thread
    to continuously add frames to the queue. Subclasses must implement
    abstract methods to define specific behavior for initializing and
    releasing the video reader, as well as retrieving frames.

    Args:
        video_path (str): Path to the video file to read
        queue_size (int, optional): Maximum size of the frame queue. Defaults to DEFAULT_READER_QUEUE.
    """

    def __init__(self, video_path: str, queue_size: int = DEFAULT_READER_QUEUE):
        self._video_path = video_path

        self.__queue_size = queue_size
        self.__reader_queue: Optional[queue.Queue[Optional[np.ndarray]]] = None
        self.__read_thread: Optional[threading.Thread] = None
        self.__stop: Optional[bool] = None

    @abstractmethod
    def init_reader(self):
        """
        Initializes the video reader.

        This abstract method must be implemented by subclasses to handle
        video reader initialization.
        """
        pass

    @abstractmethod
    def release_reader(self):
        """
        Releases the video reader resources.

        This abstract method must be implemented by subclasses to properly
        clean up video reader resources.
        """
        pass

    @abstractmethod
    def _next(self) -> Tuple[bool, Optional[np.ndarray]]:
        """
        Gets the next frame from the video.

        This abstract method must be implemented by subclasses to handle
        retrieving the next frame.

        Returns:
            Tuple[bool, Optional[np.ndarray]]: Tuple containing success flag and frame data
        """
        pass

    def read(self) -> Tuple[bool, Optional[np.ndarray]]:
        """
        Reads a frame from the queue.

        Returns:
            Tuple[bool, Optional[np.ndarray]]: Tuple containing:
                - bool: True if frame exists, False otherwise
                - Optional[np.ndarray]: Frame data if available, None otherwise
        """
        frame = self.__pop_from_queue()
        return frame is not None, frame

    def _init_queue(self):
        """
        Initializes the frame queue and reading thread.

        Creates a new queue and starts a daemon thread for reading frames
        if they don't already exist.
        """
        if self.__reader_queue is None:
            self.__reader_queue = queue.Queue(maxsize=self.__queue_size)
        if self.__read_thread is None:
            self.__read_thread = threading.Thread(target=self.__put_to_queue, daemon=True)
            self.__read_thread.start()
        if self.__stop is None:
            self.__stop = False

    def _release_queue(self):
        """
        Releases queue resources.

        Stops the reading thread and empties/removes the frame queue.
        """
        if self.__read_thread is not None:
            self.__read_thread.join()
            self.__read_thread = None
        if self.__reader_queue is not None:
            self.__reader_queue.empty()
            self.__reader_queue = None

    def __put_to_queue(self):
        """
        Continuously adds frames to the queue.

        Internal method that runs in a separate thread to read frames
        and add them to the queue while checking queue capacity.
        """
        while True:
            if self.__reader_queue.qsize() < self.__queue_size:
                ret, frame = self._next()
                if ret:
                    self.__reader_queue.put(frame)
                else:
                    self.__reader_queue.put(None)
                    break
            else:
                time.sleep(0.06)
                continue

    def __pop_from_queue(self) -> np.ndarray:
        """
        Retrieves a frame from the queue.

        Returns:
            np.ndarray: Frame data from the queue
        """
        frame = self.__reader_queue.get()
        return frame


class AsyncWriter:
    """
    Represents an abstract base class for asynchronous video writing.

    This class provides a framework for managing asynchronous writing of video frames
    to a specific file or stream location. It uses a queue to store frames and a
    separate thread to process the writing operation. Subclasses must implement
    specific writer initialization, releasing, and frame writing logic via abstract
    methods.

    Args:
        video_path (str): Path to the output video file
        queue_size (int, optional): Maximum size of the frame queue. Defaults to DEFAULT_WRITER_QUEUE.
    """

    def __init__(self, video_path: str, queue_size: int = DEFAULT_WRITER_QUEUE):
        self._video_path = video_path

        self.__queue_size = queue_size
        self.__writer_queue: Optional[queue.Queue[np.ndarray]] = None
        self.__write_thread: Optional[threading.Thread] = None
        self.__stop_event: threading.Event = threading.Event()

    @abstractmethod
    def init_writer(self):
        """
        Initializes the video writer.

        This abstract method must be implemented by subclasses to handle video
        writer initialization.
        """
        pass

    @abstractmethod
    def release_writer(self):
        """
        Releases the video writer resources.

        This abstract method must be implemented by subclasses to properly clean up
        video writer resources.
        """
        pass

    @abstractmethod
    def _next(self, frame: np.ndarray):
        """
        Processes the next frame for writing.

        This abstract method must be implemented by subclasses to handle writing
        the provided frame.

        Args:
            frame (np.ndarray): Video frame to be written
        """
        pass

    def write(self, frame: np.ndarray):
        """
        Writes a frame to the queue for asynchronous processing.

        Args:
            frame (np.ndarray): Video frame to be queued for writing
        """
        self.__put_to_queue(frame)

    def _init_queue(self):
        """
        Initializes the frame queue and writing thread.

        Creates a new queue and starts a daemon thread for writing frames if they
        don't already exist. Also resets the stop event.
        """
        self.__stop_event.clear()

        if self.__writer_queue is None:
            self.__writer_queue = queue.Queue(maxsize=self.__queue_size)

        if self.__write_thread is None:
            self.__write_thread = threading.Thread(target=self.__pop_from_queue, daemon=True)
            self.__write_thread.start()

    def _release_queue(self):
        """
        Releases queue resources.

        Waits for the queue to empty, stops the writing thread, and cleans up
        associated resources.
        """
        if self.__writer_queue is not None:
            while not self.__writer_queue.empty():
                time.sleep(0.1)
                continue

            self.__writer_queue = None

        self.__stop_event.set()

        if self.__write_thread is not None:
            self.__write_thread.join()
            self.__write_thread = None

    def __put_to_queue(self, frame: np.ndarray):
        """
        Adds a frame to the writing queue.

        Args:
            frame (np.ndarray): Video frame to be added to the queue
        """
        self.__writer_queue.put(frame)

    def __pop_from_queue(self):
        """
        Continuously processes frames from the queue.

        Internal method that runs in a separate thread to retrieve frames from
        the queue and process them for writing. Continues until the stop event
        is set.
        """
        while not self.__stop_event.is_set():
            try:
                frame = self.__writer_queue.get(timeout=1)
            except queue.Empty:
                continue
            self._next(frame)


class AsyncPlayer:
    """
    Provides asynchronous video processing capabilities.

    This class handles both reading video frames through an asynchronous reader and
    writing frames through an asynchronous writer. It manages smooth operation for
    video processing tasks and handles edge cases like end-of-video scenarios.

    Args:
        video_path (str): Path to the video file
        video_reader (AsyncReader): Instance of AsyncReader for reading video frames
        video_writer (AsyncWriter): Instance of AsyncWriter for writing video frames
    """

    def __init__(self, video_path: str, video_reader: AsyncReader, video_writer: AsyncWriter):
        self._video_path = video_path

        # reader
        self.__reader: AsyncReader = video_reader
        self.__end_of_reader: bool = False
        self.__last_frame: Optional[np.ndarray] = None

        # writer
        self.__writer: AsyncWriter = video_writer

    def init_reader(self) -> None:
        """
        Initializes the video reader.

        Resets the end-of-reader flag and initializes the underlying AsyncReader instance.
        """
        self.__reader.init_reader()
        self.__end_of_reader = False

    def read(self) -> np.ndarray:
        """
        Reads the next frame from the video.

        Returns:
            np.ndarray: Video frame data. Returns last available frame if end of video is reached.

        Raises:
            VideoError: If no valid frames exist in the video.
        """
        if not self.__end_of_reader:
            ret, frame = self.__reader.read()
            if not ret:  # encounter False --> end of video
                logger.warning("Video has ended, now we're using the last frame")
                self.__end_of_reader = True
            else:
                self.__last_frame = frame

            return self.__last_frame
        else:
            if self.__last_frame is not None:  # replicate the last frame to avoid crash program
                return self.__last_frame
            else:  # raise an error because the video isn't valid
                raise VideoError(f"The video {self._video_path} doesn't have any frame!")

    def release_reader(self):
        """
        Releases video reader resources.

        Resets internal state and releases the underlying AsyncReader instance.
        """
        self.__reader.release_reader()
        self.__end_of_reader = False
        self.__last_frame = None

    def init_writer(self):
        """
        Initializes the video writer.

        Initializes the underlying AsyncWriter instance for writing frames.
        """
        self.__writer.init_writer()

    def write(self, frame: np.ndarray):
        """
        Writes a frame to the video.

        Args:
            frame (np.ndarray): Video frame data to write
        """
        self.__writer.write(frame)

    def release_writer(self):
        """
        Releases video writer resources.

        Releases the underlying AsyncWriter instance.
        """
        self.__writer.release_writer()


class VideoReader(AsyncReader):
    """
    Handles video reading operations asynchronously, inheriting behavior from the AsyncReader class.

    This class allows reading video frames asynchronously from a given video path. It encapsulates
    initialization, frame retrieval, and reader release capability. The internal reader leverages
    the FVideoCapture library for efficient video processing.

    Args:
        video_path (str): Path to the video file to read
        queue_size (int, optional): Size of the internal frame queue. Defaults to DEFAULT_READER_QUEUE.
    """

    def __init__(self, video_path: str, queue_size: int = DEFAULT_READER_QUEUE):
        """
        Initializes the VideoReader instance.

        Args:
            video_path (str): Path to the video file to read
            queue_size (int, optional): Size of the internal frame queue. Defaults to DEFAULT_READER_QUEUE.
        """
        super(VideoReader, self).__init__(video_path, queue_size)

        self.__reader: Optional[FVideoCapture] = None
        self.__iter: Optional[Iterator] = None

    def init_reader(self):
        """
        Initializes the video reader and frame queue.

        Creates an FVideoCapture instance if one does not exist and initializes
        the frame iterator and queue for asynchronous reading.
        """
        if self.__reader is None:
            self.__reader = FVideoCapture(self._video_path, output_pixel_format=PixelFormat.BGR24)
            self.__iter = self.__reader.read()

        self._init_queue()  # always init queue at the end of init_reader for asynchronous reading

    def release_reader(self):
        """
        Releases all resources used by the video reader.

        Closes the FVideoCapture instance if it exists and releases the frame queue.
        Sets internal reader and iterator references to None.
        """
        if self.__reader is not None:
            self.__reader.release()
            self.__reader = None
            self.__iter = None

        self._release_queue()  # always release queue at the end of release_reader for asynchronous reading

    def _next(self) -> Tuple[bool, Optional[np.ndarray]]:
        """
        Retrieves the next frame from the video iterator.

        Returns:
            Tuple[bool, Optional[np.ndarray]]: A tuple containing:
                - bool: Success flag indicating if frame was retrieved
                - Optional[np.ndarray]: The frame data if successful, None otherwise

        Raises:
            Exception: If error occurs while reading frame (caught and logged)
        """
        try:
            ret, frame = next(self.__iter)
            if not ret:
                return False, None
            return True, frame
        except Exception as e:
            logger.debug(f"Error reading frame from video {self._video_path}: {str(e)}")
            return False, None


class VideoWriter(AsyncWriter):
    """
    Provides capability for asynchronously writing video frames to a file.

    This class extends AsyncWriter to support background writing operations using a queue.
    It handles initialization of a video writer object, frame writing, and cleanup.

    Args:
        video_path: Path where the video file will be written
        fps: Target frames per second for the output video
        size: Tuple of (width, height) specifying frame dimensions
        bitrate: Target bitrate for video compression (e.g. "500k")
        queue_size: Maximum number of frames to buffer in the writing queue

    Attributes:
        __writer: FVideoWriter instance for actual video writing
        __fps: Frame rate for the output video
        __size: Frame dimensions as (width, height) tuple
        __bitrate: Video bitrate setting
    """

    def __init__(
        self, video_path: str, fps: float, size: Tuple[int, int], bitrate: str, queue_size: int = DEFAULT_WRITER_QUEUE
    ):
        """
        Initializes the VideoWriter with specified settings.

        Args:
            video_path: Path where the video file will be written
            fps: Target frames per second for the output video
            size: Tuple of (width, height) specifying frame dimensions
            bitrate: Target bitrate for video compression
            queue_size: Maximum number of frames to buffer in the writing queue
        """
        super(VideoWriter, self).__init__(video_path, queue_size)
        self.__writer: Optional[FVideoWriter] = None

        self.__fps: float = fps
        self.__size: Tuple[int, int] = size
        self.__bitrate = bitrate

    def init_writer(self):
        """
        Initializes the video writer and frame queue.

        Creates an FVideoWriter instance with the specified video settings and
        initializes the asynchronous writing queue.
        """
        self.__writer = FVideoWriter(
            self._video_path,
            frame_size=self.__size,
            frame_pixel_format=PixelFormat.BGR24,
            output_pixel_format=PixelFormat.YUV420P,
            fps=self.__fps,
            bitrate=self.__bitrate,
        )

        self._init_queue()  # always init queue at the end of init_writer for asynchronous writing

    def _next(self, frame: np.ndarray):
        """
        Writes the next frame to the video file.

        Args:
            frame: Video frame as a numpy array in BGR24 format
        """
        self.__writer.write(frame)  # here's always np.uint8

    def release_writer(self):
        """
        Releases resources used by the video writer.

        Ensures the writing queue is emptied and properly released before
        closing the FVideoWriter instance.
        """
        self._release_queue()  # always init queue at the beginning of release_writer for asynchronous writing

        if self.__writer is not None:
            self.__writer.release()
            self.__writer = None


class VideoPlayer(AsyncPlayer):
    """
    Handles video playback and processing asynchronously.

    VideoPlayer is a specialized asynchronous player designed to handle video
    playback. It integrates with `VideoReader` for reading video frames and
    `VideoWriter` for writing or processing the video data. This class leverages
    asynchronous operations for enhanced performance, especially with large
    videos. Users can customize various configurations such as the frame rate,
    video size, bitrate, and queue sizes for both the reader and writer components.
    """

    def __init__(
        self,
        video_path: str,
        fps: float,
        size: Tuple[int, int],
        bitrate: str,
        reader_queue_size: int = DEFAULT_READER_QUEUE,
        writer_queue_size: int = DEFAULT_WRITER_QUEUE,
    ):
        reader = VideoReader(video_path=video_path, queue_size=reader_queue_size)
        writer = VideoWriter(video_path=video_path, fps=fps, size=size, bitrate=bitrate, queue_size=writer_queue_size)
        super(VideoPlayer, self).__init__(video_path, reader, writer)


class MaskVideoReader(AsyncReader):
    """
    Asynchronous video frame reader with data type conversion capabilities.

    This class extends AsyncReader to handle initialization, reading, and release of
    a video reader (cv2.VideoCapture) asynchronously. It processes frames to match
    a specified data type format (uint8 or float32) and maintains an internal queue
    for efficient reading.

    Args:
        video_path (str): Path to the video file to read
        dtype (np.dtype): Target data type for output frames (np.uint8 or np.float32)
        queue_size (int, optional): Size of frame queue. Defaults to DEFAULT_READER_QUEUE.
    """

    def __init__(self, video_path: str, dtype: np.dtype, queue_size: int = DEFAULT_READER_QUEUE):
        """
        Initialize the MaskVideoReader with specified parameters.

        Args:
            video_path (str): Path to the input video file
            dtype (np.dtype): Target data type for output frames
            queue_size (int, optional): Size of internal frame queue
        """
        super(MaskVideoReader, self).__init__(video_path, queue_size)

        self.__reader: Optional[cv2.VideoCapture] = None

        assert dtype in [np.uint8, np.float32]
        self.__dtype = dtype

    def init_reader(self):
        """
        Initialize the video capture reader and frame queue.

        Creates a new cv2.VideoCapture instance if one does not exist and
        initializes the asynchronous frame reading queue.
        """
        if self.__reader is None:
            self.__reader = cv2.VideoCapture(self._video_path)

        self._init_queue()  # always init queue at the end of init_writer for asynchronous writing

    def release_reader(self):
        """
        Release all resources used by the video reader.

        Closes the cv2.VideoCapture instance if it exists and releases
        the frame queue. Sets reader reference to None.
        """
        if self.__reader is not None:
            self.__reader.release()
            self.__reader = None

        self._release_queue()  # always release queue at the end of release_reader for asynchronous reading

    def _next(self) -> Tuple[bool, Optional[np.ndarray]]:
        """
        Retrieve and process the next frame from the video.

        Reads a frame, converts it to grayscale if needed, and applies dtype conversion.
        Handles any exceptions during frame reading.

        Returns:
            Tuple[bool, Optional[np.ndarray]]: Tuple containing:
                - bool: Success flag indicating if frame was retrieved
                - Optional[np.ndarray]: The processed frame if successful, None otherwise

        Raises:
            Exception: If error occurs during frame reading (caught and logged)
        """
        try:
            ret, frame = self.__reader.read()  # here's always np.uint8
            if not ret:
                return False, None
            if len(frame.shape) == 3:
                frame = frame[..., 0]

            if self.__dtype == np.float32:
                frame = np.divide(frame.astype(np.float32), 255.0)
            return True, frame
        except Exception as e:
            logger.debug(f"Error reading frame from video {self._video_path}: {str(e)}")
            return False, None


class MaskVideoWriter(AsyncWriter):
    """
    Writes video frames with a mask asynchronously.

    This class extends AsyncWriter to handle writing single-channel (grayscale) mask frames
    to video files. It provides methods for initializing the writer, releasing resources,
    and processing frames asynchronously through a queue system.

    Args:
        video_path (str): Path where the output video file will be written
        fps (float): Frame rate for the output video in frames per second
        size (Tuple[int, int]): Frame dimensions as (width, height) tuple
        queue_size (int, optional): Maximum size of frame queue. Defaults to DEFAULT_WRITER_QUEUE.
    """

    def __init__(self, video_path: str, fps: float, size: Tuple[int, int], queue_size: int = DEFAULT_WRITER_QUEUE):
        """
        Initializes the MaskVideoWriter with specified parameters.

        Args:
            video_path (str): Path where the output video file will be written
            fps (float): Frame rate for the output video in frames per second
            size (Tuple[int, int]): Frame dimensions as (width, height) tuple
            queue_size (int, optional): Maximum size of frame queue. Defaults to DEFAULT_WRITER_QUEUE.
        """
        super(MaskVideoWriter, self).__init__(video_path, queue_size)
        self.__writer: Optional[cv2.VideoWriter] = None

        self.__fps: float = fps
        self.__size: Tuple[int, int] = size

    def init_writer(self):
        """
        Initializes the video writer and frame queue.

        Creates a new cv2.VideoWriter instance configured for grayscale video if one
        does not exist and initializes the asynchronous frame writing queue.
        """
        if self.__writer is None:
            self.__writer = cv2.VideoWriter(
                filename=self._video_path,
                fourcc=cv2.VideoWriter_fourcc(*'FFV1'),
                fps=self.__fps,
                frameSize=self.__size,
                isColor=False,
            )

        self._init_queue()  # always init queue at the end of init_writer for asynchronous writing

    def release_writer(self):
        """
        Releases video writer resources.

        Ensures the writing queue is emptied and properly released before closing
        the cv2.VideoWriter instance.
        """
        self._release_queue()  # always release queue at the beginning of release_writer for asynchronous writing

        if self.__writer is not None:
            self.__writer.release()
            self.__writer = None

    def _next(self, frame: np.ndarray):
        """
        Processes and writes the next video frame.

        Converts frame to 8-bit grayscale format if needed before writing to video file.
        Handles both float32 and multichannel inputs by converting to uint8 grayscale.

        Args:
            frame (np.ndarray): Input frame to process and write
        """
        if frame.dtype == np.float32:
            frame = (frame * 255).astype(np.uint8)

        if len(frame.shape) == 3:
            frame = frame[..., 0]

        self.__writer.write(frame)  # here's always np.uint8


class MaskVideoPlayer(AsyncPlayer):
    """
    Handles video playback with mask capability by inheriting AsyncPlayer.

    The MaskVideoPlayer integrates capability for reading and writing video
    frames specifically for masked video formats. It uses MaskVideoReader
    for efficient frame reading and MaskVideoWriter for writing video frames
    with specified attributes such as frame size and frame rate. The main
    purpose of this class is to allow seamless video processing with read and
    write capabilities while incorporating masks. This is particularly useful
    in scenarios where masked processing of video frames is required.
    """

    def __init__(
        self,
        video_path: str,
        fps: float,
        size: Tuple[int, int],
        dtype: np.dtype,
        reader_queue_size: int = DEFAULT_READER_QUEUE,
        writer_queue_size: int = DEFAULT_WRITER_QUEUE,
    ):
        reader: MaskVideoReader = MaskVideoReader(video_path=video_path, dtype=dtype, queue_size=reader_queue_size)
        writer: MaskVideoWriter = MaskVideoWriter(video_path=video_path, fps=fps, size=size, queue_size=writer_queue_size)
        super(MaskVideoPlayer, self).__init__(video_path, reader, writer)
