import os
import pickle
import shutil
import uuid
from typing import Optional

import numpy as np

from vsc.composite_led_v2.datastruct import (
    LedProperties,
    Metadata,
    ScreenOrientation,
    VideoProperties,
)
from vsc.composite_led_v2.led import Led
from vsc.composite_led_v2.source import Source
from vsc.composite_led_v2.video_player import VideoPlayer
from vsc.utils.logger import logger


class Session:
    """
    Manages video processing sessions for LED composition.

    This class handles video source and LED configurations, temporary file management,
    and intermediate processing for video composition tasks.

    Args:
        video_path (str): Path to input video file
        led_path (str): Path to LED configuration file
        output_path (str): Path for output video file
        source_properties (Optional[VideoProperties]): Properties for source video
        led_properties (Optional[LedProperties]): Properties for LED configuration
        metadata (Optional[Metadata]): Additional metadata for processing
        session_id (str): Unique identifier for the session
        temp_dir (str): Directory for temporary files
        debug (bool): Enable debug mode
    """

    def __init__(
        self,
        video_path: str,
        led_path: str,
        output_path: str,
        source_properties: Optional[VideoProperties] = None,
        led_properties: Optional[LedProperties] = None,
        metadata: Optional[Metadata] = None,
        session_id: str = None,
        temp_dir: str = "data/temp",
        debug: bool = False,
    ):
        self.__session_id = str(uuid.uuid4()) if session_id is None else session_id
        self.__temp_dir = temp_dir
        self.__data_dir = os.path.join(self.__temp_dir, self.__session_id)
        self.__source_dir = os.path.join(self.__data_dir, "source")
        self.__led_dir = os.path.join(self.__data_dir, "led")
        self.__video_path = video_path
        self.__led_path = led_path
        self.__output_path = output_path
        self.__intermediate_path = os.path.join(self.__data_dir, "intermediate.mp4")

        os.makedirs(self.__data_dir, exist_ok=True)
        os.makedirs(self.__source_dir, exist_ok=True)
        os.makedirs(self.__led_dir, exist_ok=True)
        os.makedirs(os.path.dirname(self.__video_path), exist_ok=True)
        os.makedirs(os.path.dirname(self.__led_path), exist_ok=True)
        os.makedirs(os.path.dirname(self.__output_path), exist_ok=True)
        os.makedirs(os.path.dirname(self.__intermediate_path), exist_ok=True)

        logger.info(f"Session id: {self.__session_id}")
        logger.info(f"Video path: {self.__video_path}")
        logger.info(f"LED path: {self.__led_path}")
        logger.info(f"Output path: {self.__output_path}")

        # init source
        self.source: Source = Source(
            self.__video_path, self.__source_dir, source_properties=source_properties, metadata=metadata
        )

        # init led
        self.led: Led = Led(self.__led_path, self.__led_dir, led_properties=led_properties)

        # screen orientation
        self.__screen_orientation: Optional[ScreenOrientation] = None

        # screen exist
        self.__exist: bool = True

        # video output writer
        self.__intermediate_video_player: VideoPlayer = VideoPlayer(
            video_path=self.__intermediate_path,
            fps=self.source.get_source_properties().fps,
            size=(self.source.get_source_properties().frame_width, self.source.get_source_properties().frame_height),
            bitrate=self.source.get_source_properties().bit_rate,
        )

        # debug
        self.__debug = debug
        self.__debug_corners_path = os.path.join(self.__source_dir, 'corners.pkl')
        self.__debug_occluded_corner_path = os.path.join(self.__source_dir, 'occluded_corner.pkl')
        self.__debug_hidden_edge_path = os.path.join(self.__source_dir, 'hidden_edge.pkl')

    def get_intermediate_path(self) -> str:
        """Get path to intermediate video file.

        Returns:
            str: Path to intermediate video file
        """
        return self.__intermediate_path

    def get_output_video_path(self) -> str:
        """Get path to output video file.

        Returns:
            str: Path to output video file
        """
        return self.__output_path

    # -------------- SCREEN ORIENTATION --------------
    def set_screen_orientation(self, orientation: ScreenOrientation) -> None:
        """Set screen orientation for video processing.

        Args:
            orientation (ScreenOrientation): Orientation to set
        """
        self.__screen_orientation = orientation

    def get_screen_orientation(self) -> ScreenOrientation:
        """Get current screen orientation.

        Returns:
            ScreenOrientation: Current screen orientation
        """
        return self.__screen_orientation

    def set_exist_screen(self, exist: bool) -> None:
        """Set whether the screen exists.

        Args:
            exist (bool): Whether the screen exists
        """
        self.__exist = exist

    def get_exist_screen(self) -> bool:
        """Get whether the screen exists.

        Returns:
            bool: Whether the screen exists
        """
        return self.__exist

    # -------------- INTERMEDIATE VIDEO --------------
    def init_intermediate_writer(self) -> None:
        """Initialize intermediate video writer."""
        self.__intermediate_video_player.init_writer()

    def set_intermediate_frame_data(self, frame_data: np.ndarray) -> None:
        """Write frame data to intermediate video.

        Args:
            frame_data (np.ndarray): Frame data to write
        """
        self.__intermediate_video_player.write(frame_data)

    def release_intermediate_writer(self) -> None:
        """Release intermediate video writer resources."""
        self.__intermediate_video_player.release_writer()

    def init_intermediate_reader(self) -> None:
        """Initialize intermediate video reader."""
        self.__intermediate_video_player.init_reader()

    def get_intermediate_frame_data(self) -> np.ndarray:
        """Get frame data from intermediate video.

        Returns:
            np.ndarray: Frame data from intermediate video
        """
        return self.__intermediate_video_player.read()

    def release_intermediate_reader(self) -> None:
        """Release intermediate video reader resources."""
        self.__intermediate_video_player.release_reader()

    # -------------- DEBUG --------------
    def run_debugging(self):
        """Save debugging data to files.

        Saves corner arrays, hidden edges, and occluded corner data for debugging.
        """
        # save all corners
        with open(self.__debug_corners_path, "wb") as f:
            pickle.dump(self.source.get_all_corners_array(), f)

        # save all hidden edge
        with open(self.__debug_hidden_edge_path, "wb") as f:
            pickle.dump(self.source.get_all_hidden_edges(), f)

        # save all hidden edge
        with open(self.__debug_occluded_corner_path, "wb") as f:
            pickle.dump(self.source.get_all_occluded_corner(), f)

    # -------------- RUN WITHOUT PROCESSING --------------
    def run_without_processing(self):
        shutil.copy(self.__video_path, self.__intermediate_path)

    # -------------- RELEASE --------------
    def release(self):
        """Release session resources.

        Runs debugging if enabled, otherwise cleans up temporary files.
        """
        if self.__debug:
            self.run_debugging()
        elif os.path.exists(self.__temp_dir):
            shutil.rmtree(self.__temp_dir)
