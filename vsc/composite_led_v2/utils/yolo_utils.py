import numpy as np


def convert_xyxy_to_xywh(boxes: np.ndarray) -> np.ndarray:
    """
    Convert bounding boxes from (x1, y1, x2, y2) format to (x, y, width, height) format.

    Args:
        boxes (np.ndarray): Array of shape (N, 4) containing bounding boxes in (x1, y1, x2, y2) format

    Returns:
        np.ndarray: Array of shape (N, 4) containing converted boxes in (x, y, width, height) format,
                   where (x,y) is the top-left corner
    """
    x0, y0, x1, y1 = boxes[:, 0], boxes[:, 1], boxes[:, 2], boxes[:, 3]
    width = x1 - x0
    height = y1 - y0
    return np.stack((x0, y0, width, height), axis=1)


def expand_bbox(bbox, expand_ratio=0.1, img_width=None, img_height=None):
    """
    Expand a bounding box by a given ratio while ensuring it stays within image boundaries.

    Args:
        bbox: List or tuple of [x, y, w, h] where (x, y) is the top-left corner
        expand_ratio: Float value to expand the box by (e.g., 0.1 = 10% expansion)
        img_width: Optional width of the image for boundary checking
        img_height: Optional height of the image for boundary checking

    Returns:
        List [x, y, w, h] of the expanded bounding box
    """
    x, y, w, h = bbox

    # Calculate expansion amounts
    w_half_expand = w * expand_ratio / 2
    h_half_expand = h * expand_ratio / 2

    # Expand the box
    new_x = x - w_half_expand
    new_y = y - h_half_expand
    new_w = w + w * expand_ratio
    new_h = h + h * expand_ratio

    # Boundary checks if image dimensions are provided
    if img_width is not None:
        # Ensure x doesn't go below 0
        if new_x < 0:
            new_x = 0
        # Ensure x + width doesn't exceed image width
        if new_x + new_w > img_width:
            new_w = img_width - new_x

    if img_height is not None:
        # Ensure y doesn't go below 0
        if new_y < 0:
            new_y = 0
        # Ensure y + height doesn't exceed image height
        if new_y + new_h > img_height:
            new_h = img_height - new_y

    return [new_x, new_y, new_w, new_h]
