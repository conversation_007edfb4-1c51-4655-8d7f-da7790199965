import torch
import torch.nn.functional as F
from torchvision.transforms.functional import perspective


def fill_led_1_pytorch(led: torch.Tensor, corners: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
    """
    Transform and blend an LED image onto a target frame using perspective transformation.

    This function resizes the LED image, applies perspective transformation based on corner points,
    and blends it with a mask.

    Args:
        led (torch.Tensor): LED image tensor in NCHW format
        corners (torch.Tensor): Corner points for perspective transformation
        mask (torch.Tensor): Binary mask tensor for blending

    Returns:
        torch.Tensor: Transformed and masked LED image tensor
    """
    h, w = mask.shape[-2:]
    led_resized = F.interpolate(led, size=(h, w), mode="bilinear", align_corners=False)
    led_h, led_w = led_resized.shape[-2:]
    source_corners = [[0, 0], [0, led_h], [led_w, led_h], [led_w, 0]]
    transformed = perspective(led_resized, startpoints=source_corners, endpoints=corners.tolist())
    return transformed * mask
