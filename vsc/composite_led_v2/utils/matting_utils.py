from typing import List, Tuple

import cv2
import numpy as np

from vsc.composite_led_v2.utils.screen_utils import get_mask_based_on_hsv


def calculate_matting_ratio(matting: np.ndarray, matting_threshold: float = 0.9) -> float:
    """
    Calculate the ratio of matting area above threshold to total image area.

    Args:
        matting: Grayscale image (float32) with shape [H, W]
        matting_threshold: Threshold value for matting calculation

    Returns:
        float: Ratio of matting area to total image area
    """
    threshold_value = matting_threshold * matting.max()
    matting_area = np.count_nonzero(matting > threshold_value)
    return matting_area / (matting.shape[0] * matting.shape[1])


def remove_sparse_noise_matte(alpha_matte: np.ndarray) -> np.ndarray:
    """
    Remove sparse noise from alpha matte by keeping only the largest contour.

    Args:
        alpha_matte: Input alpha matte image array

    Returns:
        np.ndarray: Alpha matte with sparse noise removed
    """
    if np.issubdtype(alpha_matte.dtype, np.floating) and alpha_matte.max() <= 1:
        alpha_matte = alpha_matte * 255

    alpha_matte = alpha_matte.astype(np.uint8)
    mask = np.zeros_like(alpha_matte)

    try:
        _, binary_mask = cv2.threshold(alpha_matte, 0, 255, cv2.THRESH_BINARY)

        contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
        area_list = [cv2.contourArea(contour) for contour in contours]

        if len(area_list) == 0:
            return mask

        largest_contour_index = np.argmax(area_list)

        for i, contour in enumerate(contours):
            if i != largest_contour_index:
                cv2.drawContours(mask, [contour], -1, (255, 255, 255), thickness=cv2.FILLED)

        inverted_mask = cv2.bitwise_not(mask)

        return cv2.bitwise_and(alpha_matte, alpha_matte, mask=inverted_mask)

    except Exception:
        return mask


def dilate(mask: np.ndarray, kernel_size: int = 5, crop_mode: bool = False) -> np.ndarray:
    """
    Dilate the given mask using a structuring element of the specified size.

    Args:
        mask: Input mask to be dilated
        kernel_size: Size of the kernel for dilation, defaults to 5
        crop_mode: Whether to dilate only the cropped region of the mask, defaults to False

    Returns:
        np.ndarray: The dilated mask
    """
    if mask.size == 0:
        return mask

    if mask.ndim == 3:
        mask = mask[:, :, 0]

    element = cv2.getStructuringElement(
        cv2.MORPH_ELLIPSE,
        (2 * kernel_size + 1, 2 * kernel_size + 1),
        (kernel_size, kernel_size),
    )

    if not crop_mode:
        return cv2.dilate(mask, element)

    x1, y1, x2, y2 = extract_region(mask)
    cropped_mask = mask[y1:y2, x1:x2]

    if cropped_mask.size == 0:
        return mask

    dilated_cropped_mask = cv2.dilate(cropped_mask, element)
    mask = np.zeros_like(mask)
    mask[y1:y2, x1:x2] = dilated_cropped_mask
    return mask


def extract_region(mask: np.ndarray, stretch: int = 10):
    """
    Extract the region of interest from a binary mask with optional stretching.

    Args:
        mask: Input binary mask with values [0, 255] or [0, 1], shape `hwc`
        stretch: Number of pixels to expand the region of interest, defaults to 10

    Returns:
        tuple: Coordinates of the extracted region (x1, y1, x2, y2). Returns (0, 0, 0, 0) if mask is empty

    Notes:
        The function converts 3D masks to 2D by selecting the first channel
    """
    if mask.ndim == 3:
        mask = mask[:, :, 0]

    ys, xs = np.nonzero(mask)

    if len(ys) == 0 or len(xs) == 0:  # Handle the case where the mask is empty
        return 0, 0, 0, 0

    y_min = ys.min()
    y_max = ys.max()
    x_min = xs.min()
    x_max = xs.max()

    x1 = max(0, x_min - stretch)
    y1 = max(0, y_min - stretch)
    x2 = min(mask.shape[1] - 1, x_max + stretch)
    y2 = min(mask.shape[0] - 1, y_max + stretch)
    return x1, y1, x2, y2


def smooth_edge(
    mask: np.ndarray,
    frame: np.ndarray,
    iters: int,
    use_edge_preserving: bool,
    sigma_s: float,
    sigma_r: float,
    bbox_stretch: int,
    color_range: List,
) -> np.ndarray:
    """
    Smooth edges of a mask using pyramid reduction and optional edge preservation.

    Args:
        mask: Input binary mask
        frame: Input image frame
        iters: Number of pyramid iterations
        use_edge_preserving: Whether to apply edge preservation
        sigma_s: Spatial standard deviation for edge preservation
        sigma_r: Range standard deviation for edge preservation
        bbox_stretch: Amount to stretch bounding box
        color_range: Color range for HSV masking

    Returns:
        np.ndarray: Smoothed mask
    """
    x1, y1, x2, y2 = extract_region(mask, bbox_stretch)
    calibrated_x1, calibrated_y1, calibrated_x2, calibrated_y2 = calibrate_coordinates_for_pyramid(frame, x1, y1, x2, y2)
    processing_patch = frame[calibrated_y1:calibrated_y2, calibrated_x1:calibrated_x2, :]
    if processing_patch.size == 0:
        return mask
    processing_patch = smooth_with_pyramid(processing_patch, iters=iters)
    if use_edge_preserving:
        processing_patch = cv2.edgePreservingFilter(processing_patch, flags=1, sigma_s=sigma_s, sigma_r=sigma_r)
    processing_mask = get_mask_based_on_hsv(processing_patch, color_range)
    mask[calibrated_y1:calibrated_y2, calibrated_x1:calibrated_x2] = processing_mask
    return mask


def calibrate_coordinates_for_pyramid(frame: np.ndarray, x1: int, y1: int, x2: int, y2: int) -> Tuple[int, int, int, int]:
    """
    Adjust coordinates to ensure bounding box dimensions are even for pyramid processing.

    Args:
        frame: Original image frame (not cropped)
        x1: Left coordinate
        y1: Top coordinate
        x2: Right coordinate
        y2: Bottom coordinate

    Returns:
        tuple: Adjusted coordinates (x1, y1, x2, y2) suitable for pyramid processing
    """
    if (x2 - x1) % 2 != 0:
        if x2 == frame.shape[1] - 1:
            x1 += 1  # To reduce workload
        else:
            x2 -= 1  # To reduce workload

    if (y2 - y1) % 2 != 0:
        if y2 == frame.shape[0] - 1:
            y1 += 1  # To reduce workload
        else:
            y2 -= 1  # To reduce workload
    return x1, y1, x2, y2


def smooth_with_pyramid(image: np.ndarray, iters: int = 1) -> np.ndarray:
    """
    Apply pyramid smoothing to an image through down-sampling and up-sampling.

    Args:
        image: Input image to smooth
        iters: Number of pyramid iterations, defaults to 1

    Returns:
        np.ndarray: Smoothed image
    """
    image_clone = image.copy()
    for _ in range(iters):
        image_clone = cv2.pyrUp(cv2.pyrDown(image_clone))
    return image_clone


def denoise(
    mask: np.ndarray, corners: np.ndarray, timi_matte: np.ndarray, kernel_erode: int, timi_matte_removing_threshold: float
) -> np.ndarray:
    """
    Apply dense and sparse noise removal to mask.

    Args:
        mask: Input mask to denoise
        corners: Corner coordinates for noise removal
        timi_matte: TIMI matte image
        kernel_erode: Kernel size for erosion
        timi_matte_removing_threshold: Threshold for TIMI matte processing

    Returns:
        np.ndarray: Denoised mask
    """
    mask = remove_dense_noise(mask, kernel_erode)
    mask = remove_sparse_noise(mask, corners, timi_matte, timi_matte_removing_threshold=timi_matte_removing_threshold)
    return mask


def remove_dense_noise(mask: np.ndarray, kernel_erosion: int) -> np.ndarray:
    """
    Remove dense noise using morphological operations.

    Args:
        mask: Input mask to denoise
        kernel_erosion: Kernel size for erosion operation

    Returns:
        np.ndarray: Mask with dense noise removed
    """
    element = cv2.getStructuringElement(
        cv2.MORPH_CROSS,
        (2 * kernel_erosion + 1, 2 * kernel_erosion + 1),
        (kernel_erosion, kernel_erosion),
    )
    mask_denoise = cv2.erode(mask, element)
    mask_conserve_area = cv2.dilate(mask_denoise, element)
    return mask_conserve_area


def remove_sparse_noise(
    mask: np.ndarray,
    corners: np.ndarray,
    timi_matte: np.ndarray,
    timi_matte_removing_threshold: float,
) -> np.ndarray:
    """
    Remove sparse noise outside specified corners and using TIMI matte.

    Args:
        mask: Input mask for noise removal
        corners: Corner coordinates defining valid region
        timi_matte: TIMI matte image
        timi_matte_removing_threshold: Threshold for TIMI matte processing

    Returns:
        np.ndarray: Mask with sparse noise removed
    """
    corners = corners.reshape((-1, 1, 2))
    contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        center_point = [x + w / 2, y + h / 2]
        result = cv2.pointPolygonTest(corners, center_point, False)

        """
            result = -1: Point is outside the polygon
            result = 0: Point is on the polygon
            result = 1: Point is inside the polygon
        """

        if result == -1:
            cv2.fillPoly(mask, pts=[contour], color=(0, 0, 0))

    return remove_difficult_noise_with_timi_matte(timi_matte, mask, timi_matte_removing_threshold)


def remove_difficult_noise_with_timi_matte(
    matte_timi: np.ndarray,
    mask: np.ndarray,
    threshold: float,
    dilation_kernel_size: int = 5,
) -> np.ndarray:
    """
    Remove challenging noise using TIMI matte and contour analysis.

    Args:
        matte_timi: TIMI matte input image
        mask: Binary mask input
        threshold: Threshold for binarizing TIMI matte
        dilation_kernel_size: Kernel size for dilation, defaults to 5

    Returns:
        np.ndarray: Cleaned mask with challenging noise removed
    """
    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    area_list = [cv2.contourArea(contour) for contour in contours]

    if len(area_list) == 0:
        return mask

    largest_area_index = area_list.index(max(area_list))

    # Iterate over contours and remove those that don't intersect with the dilated mask
    for i, contour in enumerate(contours):
        if i != largest_area_index:
            x, y, w, h = cv2.boundingRect(contour)

            # Apply padding to the bounding box
            x_pad = max(x - dilation_kernel_size, 0)
            y_pad = max(y - dilation_kernel_size, 0)
            w_pad = min(w + 2 * dilation_kernel_size, matte_timi.shape[1] - x_pad)
            h_pad = min(h + 2 * dilation_kernel_size, matte_timi.shape[0] - y_pad)

            # Extract region with padding
            mask_with_only_this_contour = np.zeros((h_pad, w_pad), dtype=np.uint8)
            cv2.fillPoly(
                mask_with_only_this_contour,
                pts=[contour - [x_pad, y_pad]],
                color=(1, 1, 1),
            )

            # Dilate within the padded region
            dilated_region = dilate(
                (matte_timi[y_pad : y_pad + h_pad, x_pad : x_pad + w_pad] > threshold).astype(np.uint8),
                kernel_size=dilation_kernel_size,
            )

            # Calculate intersection
            intersection = np.bitwise_and(mask_with_only_this_contour, dilated_region)

            # Remove contour if no intersection
            if not np.any(intersection):
                cv2.fillPoly(mask, pts=[contour], color=(0, 0, 0))

    return mask


def refine_mask(
    mask_smoothed: np.ndarray,
    mask_origin: np.ndarray,
    frame: np.ndarray,
    corners: np.ndarray,
    timi_matte: np.ndarray,
    timi_matte_removing_threshold: float,
    color_range: List,
) -> np.ndarray:
    """
    Refine mask by conserving hand regions and reducing green areas.

    Args:
        mask_smoothed: Smoothed input mask
        mask_origin: Original unprocessed mask
        frame: Input image frame
        corners: Corner coordinates
        timi_matte: TIMI matte image
        timi_matte_removing_threshold: Threshold for TIMI matte processing
        color_range: Color range for HSV masking

    Returns:
        np.ndarray: Refined mask
    """
    # Conserve hand
    mask_smoothed = conserve_hand(mask_origin, mask_smoothed)

    # Reduce green area
    bg_main = cv2.bitwise_or(frame, frame, mask=cv2.bitwise_not(mask_smoothed))
    missing_mask = get_mask_based_on_hsv(bg_main, color_range=color_range)  # TODO: missing exception
    missing_mask = remove_sparse_noise(
        mask=missing_mask, corners=corners, timi_matte=timi_matte, timi_matte_removing_threshold=timi_matte_removing_threshold
    )
    missing_mask = remove_sparse_noise_based_on_mask(missing_mask, mask_smoothed)
    return (mask_smoothed + missing_mask).astype(np.uint8)


def remove_sparse_noise_based_on_mask(noise_mask: np.ndarray, final_mask: np.ndarray, kernel: int = 1) -> np.ndarray:
    """
    Remove noise regions that do not overlap with the final mask.

    Args:
        noise_mask: Binary mask containing noise regions to filter
        final_mask: Reference binary mask for overlap checking
        kernel: Kernel size for dilation, defaults to 1

    Returns:
        np.ndarray: Noise mask with non-overlapping regions removed
    """
    contours, _ = cv2.findContours(noise_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        x_pad = max(x - kernel, 0)
        y_pad = max(y - kernel, 0)
        w_pad = min(w + 2 * kernel, noise_mask.shape[1] - x_pad)
        h_pad = min(h + 2 * kernel, noise_mask.shape[0] - y_pad)
        temp_mask = np.zeros((h_pad, w_pad), dtype=np.uint8)
        cv2.fillPoly(temp_mask, [contour - [x_pad, y_pad]], color=(255, 255, 255))
        intersection = dilate(temp_mask, kernel) & final_mask[y_pad : y_pad + h_pad, x_pad : x_pad + w_pad]
        if not intersection.any():
            noise_mask[y : y + h, x : x + w] = 0
    return noise_mask


def conserve_hand(
    mask_origin: np.ndarray,
    mask_smoothed: np.ndarray,
    kernel_size_denoise: int = 1,
    kernel_size_smooth_hand: int = 3,
) -> np.ndarray:
    """
    Preserve hand regions in mask by comparing original and smoothed masks.

    Args:
        mask_origin: Original unprocessed mask
        mask_smoothed: Smoothed mask
        kernel_size_denoise: Kernel size for denoising, defaults to 1
        kernel_size_smooth_hand: Kernel size for hand smoothing, defaults to 3

    Returns:
        np.ndarray: Mask with preserved hand regions
    """
    mask_missing = cv2.subtract(mask_smoothed, mask_origin)
    mask_missing_denoise = remove_dense_noise(mask_missing, kernel_size_denoise)
    mask_missing_smooth = morphologyEx(mask_missing_denoise, kernel_size_smooth_hand)
    return cv2.subtract(mask_smoothed, mask_missing_smooth)


def morphologyEx(mask: np.ndarray, kernel_size: int) -> np.ndarray:
    """
    Apply morphological closing operation to mask.

    Args:
        mask: Input binary mask
        kernel_size: Size of morphological kernel

    Returns:
        np.ndarray: Mask after morphological processing
    """
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
    mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
    return cv2.threshold(mask, 1, 255, cv2.THRESH_BINARY)[1]
