from typing import List, Optional

import cv2
import numpy as np

from vsc.composite_led_v2.datastruct import ScreenOrientation


def get_mask_based_on_hsv(image: np.ndarray, color_range: List) -> np.ndarray:
    """
    Create a binary mask from a BGR image using HSV color thresholding.

    Args:
        image: BGR image as uint8 numpy array
        color_range: List containing lower and upper HSV color bounds

    Returns:
        Binary mask as numpy array where pixels within color range are white (255)
    """
    image_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    lower_color = np.array(color_range[0])
    upper_color = np.array(color_range[1])
    return cv2.inRange(image_hsv, lower_color, upper_color)


def find_contour(mask: np.ndarray) -> Optional[np.ndarray]:
    """
    Find the largest contour in a binary mask.

    Args:
        mask: Binary image mask as numpy array

    Returns:
        Coordinates of largest contour or None if no contours found
    """
    raw_contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    if len(raw_contours) == 0:
        return None

    area_list = [cv2.contourArea(contour) for contour in raw_contours]
    largest_area_index = area_list.index(max(area_list))
    largest_contour = raw_contours[largest_area_index]

    contours = largest_contour.squeeze().squeeze()
    return contours


def find_low_epsilon_contour(mask: np.ndarray, epsilon: float = 0.005) -> Optional[np.ndarray]:
    """
    Find simplified contour of largest object in mask using Douglas-Peucker algorithm.

    Args:
        mask: Binary image mask as numpy array
        epsilon: Maximum distance between simplified and original contour points

    Returns:
        Simplified contour coordinates or None if no contours found
    """
    raw_contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
    if len(raw_contours) == 0:
        return None

    area_list = [cv2.contourArea(contour) for contour in raw_contours]
    largest_area_index = area_list.index(max(area_list))
    largest_contour = raw_contours[largest_area_index]

    low_epsilon_contours = cv2.approxPolyDP(
        curve=largest_contour,
        epsilon=epsilon * cv2.arcLength(largest_contour, True),
        closed=True,
    )  # counter-clockwise
    low_epsilon_contours = np.squeeze(low_epsilon_contours, axis=1)
    return low_epsilon_contours


def contour_static_check(contour_list: List[np.ndarray], dummy_bbox: List[int]) -> bool:
    """
    Check if a sequence of contours represents a static object.

    Args:
        contour_list: List of contour coordinates over time
        dummy_bbox: Default bounding box to use for invalid contours

    Returns:
        True if contours represent static object, False otherwise
    """
    bbox_list = []
    for i, contour in enumerate(contour_list):
        if contour is not None and len(contour.shape) == 2 and contour.shape[0] > 3:
            bbox = cv2.boundingRect(contour)
        else:
            bbox = dummy_bbox
        bbox_list.append(bbox)

    bbox_list = np.array(bbox_list)
    bbox_list_std = np.std(bbox_list, axis=0)
    return np.count_nonzero(bbox_list_std) < 4 and np.mean(bbox_list_std) < 1


def corners_screen_check(list_corners: np.ndarray, threshold: float, width: int) -> ScreenOrientation:
    """
    Determine screen orientation based on corner point movement.

    Args:
        list_corners: Array of corner coordinates over time
        threshold: Maximum distance ratio for zoomed classification
        width: Image width for distance normalization

    Returns:
        ScreenOrientation enum indicating static, zoomed or moving state
    """
    start_centroid = np.mean(list_corners[0], axis=0)
    end_centroid = np.mean(list_corners[-1], axis=0)
    distance = np.linalg.norm(start_centroid - end_centroid)
    if distance == 0:
        return ScreenOrientation.STATIC
    elif distance / width < threshold:
        return ScreenOrientation.ZOOMED
    else:
        return ScreenOrientation.MOVING
