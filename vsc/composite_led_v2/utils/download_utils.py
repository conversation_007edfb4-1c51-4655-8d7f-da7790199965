import fcntl
import hashlib
import os
import time
from pathlib import Path

import requests
from tqdm import tqdm

from vsc.utils.exception import DownloadError
from vsc.utils.logger import logger


class AutoDeleteFile:
    """
    A context manager that automatically deletes a file when exiting the context.

    The file is created on entering and deleted on exit, useful for temporary files
    that should be cleaned up after use.
    """

    def __init__(self, path):
        """
        Initialize with the path to the file.

        Args:
            path: Path-like object representing the file location
        """
        self.path = Path(path)

    def __enter__(self):
        """
        Create and open the file for writing when entering the context.

        Returns:
            File object opened in write mode
        """
        self.file = self.path.open('w+')
        return self.file

    def __exit__(self, exc_type, exc_value, traceback):
        """
        Close and delete the file when exiting the context.

        Args:
            exc_type: Exception type if an error occurred
            exc_value: Exception value if an error occurred
            traceback: Traceback if an error occurred
        """
        self.file.close()
        if self.path.exists():
            self.path.unlink()


def calculate_md5(file_path) -> str:
    """
    Calculate MD5 hash of a file.

    Args:
        file_path: Path to the file

    Returns:
        MD5 hexadecimal digest
    """
    hash_md5 = hashlib.md5()

    # Read the file in chunks to handle large files efficiently
    with open(file_path, "rb") as f:
        for chunk in iter(lambda: f.read(4096), b""):
            hash_md5.update(chunk)

    return hash_md5.hexdigest()


def verify_hash(weight_path: str, hash_code: str) -> bool:
    """
    Verify if a file matches an expected hash code.

    Args:
        weight_path (str): Path to the file to verify
        hash_code (str): Expected MD5 hash code to check against. If None, verification is skipped.

    Returns:
        bool: True if the file hash matches the expected hash_code or if hash_code is None,
              False otherwise
    """
    return calculate_md5(weight_path) == hash_code or hash_code is None


def download_large_file(url, destination):
    """
    Download a large file from a URL with progress bar.

    Args:
        url (str): URL to download the file from
        destination (str): Local path where the file should be saved

    Raises:
        requests.exceptions.RequestException: If the download fails due to network or server issues

    The function streams the download in chunks to efficiently handle large files and displays
    a progress bar using tqdm. The file is written directly to disk in binary mode.
    """
    block_size = 1024
    try:
        with requests.get(url, stream=True) as response:
            response.raise_for_status()
            total_size = int(response.headers.get("content-length", 0))

            with open(destination, 'wb') as f:
                with tqdm(total=total_size, unit="B", unit_scale=True) as progress_bar:
                    for chunk in response.iter_content(chunk_size=block_size):
                        f.write(chunk)
                        progress_bar.update(len(chunk))

        logger.info("File downloaded successfully!")
    except requests.exceptions.RequestException as e:
        logger.critical(
            f"Error downloading the file:\n" f"\t - URL: \t{url} \n" f"\t - Save path: \t{destination}" f"\t - ERROR: \t{e} \n"
        )
        raise e


def download_with_hash_check(weight_url: str, weight_path: str, hash_code: str = None):
    """
    Downloads a file and verifies its hash code, with file locking to prevent concurrent downloads.

    Args:
        weight_url (str): URL from which to download the file
        weight_path (str): Local path where the file should be saved
        hash_code (str, optional): Expected MD5 hash code to verify the downloaded file.
            If None, hash verification is skipped. Defaults to None.

    Returns:
        str: Path to the downloaded and verified file

    Raises:
        DownloadError: If the downloaded file's hash does not match the expected hash_code

    The function uses file locking to prevent multiple concurrent downloads of the same file.
    If the file already exists and has the correct hash, downloading is skipped.
    If verification fails, the downloaded file is deleted and an error is raised.
    """
    os.makedirs(os.path.dirname(weight_path), exist_ok=True)
    lock_path = os.path.splitext(weight_path)[0] + '.lock'

    while os.path.isfile(lock_path):
        logger.info("Waiting for lock file to be released...")
        time.sleep(3)

    if os.path.isfile(weight_path):
        if verify_hash(weight_path, hash_code):
            logger.info("Weight already exists and has correct hash code. Skipping download.")
            return weight_path
        logger.info("Weight already exists and but has wrong hash code. Deleting and redownloading...")
        os.remove(weight_path)

    with AutoDeleteFile(lock_path) as f:
        try:
            fcntl.flock(f, fcntl.LOCK_EX)
            download_large_file(weight_url, weight_path)
        finally:
            fcntl.flock(f, fcntl.LOCK_UN)

    if verify_hash(weight_path, hash_code):
        logger.info("Downloaded weight has correct hash code.")
        return weight_path

    os.remove(weight_path)
    raise DownloadError("Downloaded weight has wrong hash code. Please check the url and try again.")
