import os
import shutil
import uuid

import ffmpeg
from fvutils.media import MergeTrimMode, has_audio, merge_video_with_audio


def merge_audio(
    audio_path: str,
    video_path: str,
    output_path: str,
    trim_mode: MergeTrimMode = MergeTrimMode.VIDEO_LENGTH,
) -> None:
    """Merge audio from an audio file with a video file.

    Args:
        audio_path (str): Path to the audio file to merge
        video_path (str): Path to the video file to merge
        output_path (str): Path where the merged video will be saved
        trim_mode (MergeTrimMode, optional): Mode for trimming the audio.
            Defaults to MergeTrimMode.VIDEO_LENGTH.
    """
    if os.path.isfile(audio_path) and has_audio(audio_path):
        merge_video_with_audio(
            video_path=video_path,
            audio_path=audio_path,
            output_path=output_path,
            check_duration=False,
            keep_video_audio=True,
            trim_mode=trim_mode,
        )
    else:
        shutil.copy(video_path, output_path)


def merge_audio_pipeline(
    led_path: str,
    audio_path: str,
    video_path: str,
    output_path: str,
    trim_mode: MergeTrimMode = MergeTrimMode.VIDEO_LENGTH,
):
    """Pipeline for merging multiple audio sources with a video.

    Args:
        led_path (str): Path to LED audio file
        audio_path (str): Path to main audio file
        video_path (str): Path to video file
        output_path (str): Path for final output video
        trim_mode (MergeTrimMode, optional): Mode for trimming audio.
            Defaults to MergeTrimMode.VIDEO_LENGTH.
    """
    tmp_output_path = f"{(uuid.uuid4())}.mp4"
    merge_audio(audio_path=audio_path, video_path=video_path, output_path=tmp_output_path, trim_mode=trim_mode)
    merge_audio(audio_path=led_path, video_path=tmp_output_path, output_path=output_path, trim_mode=trim_mode)
    os.remove(tmp_output_path)


def merge_videos(video_paths: list[str], output_path: str, bitrate: str = "5000k") -> None:
    """Merge multiple video files into a single video.

    Args:
        video_paths (list[str]): List of paths to input video files
        output_path (str): Path where merged video will be saved
        bitrate (str, optional): Output video bitrate. Defaults to "5000k".

    Raises:
        FileNotFoundError: If any input video file is not found.
    """
    for file in video_paths:
        if not os.path.exists(file):
            raise FileNotFoundError(f"Input file not found: {file}")

    # Create a temporary file with the list of input files
    temp_file = f"{(uuid.uuid4())}.txt"
    with open(temp_file, "w") as f:
        for file in video_paths:
            f.write(f"file '{file}'\n")

    try:
        # Use ffmpeg-concat demuxer to concatenate the videos
        (ffmpeg.input(temp_file, format="concat", safe=0).output(output_path, c="copy", b=bitrate).run(overwrite_output=True))
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_file):
            os.remove(temp_file)
