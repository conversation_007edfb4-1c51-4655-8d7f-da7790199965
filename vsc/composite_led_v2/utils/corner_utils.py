import math
from typing import List, Op<PERSON>, Tuple, Union

import cv2
import numpy as np

from vsc.composite_led_v2.constants import DUMMY_CORNER, DUMMY_CORNERS
from vsc.composite_led_v2.datastruct import (
    HiddenEdge,
    OccludedCorner,
    occluded_corner_dict,
)
from vsc.composite_led_v2.utils.matting_utils import dilate, remove_sparse_noise_matte
from vsc.composite_led_v2.utils.screen_utils import find_low_epsilon_contour


def find_dummy_index(corners: np.ndarray) -> int:
    """
    Find the index of a dummy corner in an array of corners.

    Args:
        corners: Array of corner coordinates.

    Returns:
        Index of first dummy corner found, or -1 if none found.
    """
    for i, corner in enumerate(corners):
        corner = np.array(corner).reshape(1, -1)
        if is_dummy_corner(corner):
            return i
    return -1


def is_dummy_corner(corner: np.ndarray) -> bool:
    """
    Check if a corner point is a dummy corner.

    Args:
        corner: Corner point coordinates as numpy array of shape (1,2).

    Returns:
        True if corner is a dummy corner, False otherwise.
    """
    assert corner.shape[0] == 1 and corner.shape[1] == 2
    return bool(np.all(corner == DUMMY_CORNER))


def is_dummy_corners(corners: np.ndarray) -> bool:
    """
    Check if all corners in an array are dummy corners.

    Args:
        corners: Array of corner coordinates of shape (4,2).

    Returns:
        True if all corners are dummy corners, False otherwise.
    """
    assert corners.shape[0] == 4 and corners.shape[1] == 2
    return bool(np.all(corners == DUMMY_CORNERS))


def get_nb_visible_corners(corners: np.ndarray) -> int:
    """
    Count number of non-dummy corners in array.

    Args:
        corners: Array of corner coordinates.

    Returns:
        Number of non-dummy corners.
    """
    assert len(corners.shape) == 2
    nb = 0
    for corner in corners:
        if not is_dummy_corner(corner.reshape(1, -1)):
            nb += 1
    return nb


def get_visible_corners(corners: np.ndarray) -> np.ndarray:
    """
    Extract non-dummy corners from array.

    Args:
        corners: Array of corner coordinates.

    Returns:
        Array containing only non-dummy corners.
    """
    nb_visible_corners = get_nb_visible_corners(corners)
    if nb_visible_corners == 0:
        return np.array([])
    visible_corners = []
    for corner in corners:
        corner = corner.reshape(1, -1)
        if not is_dummy_corner(corner):
            visible_corners.append(corner)

    return np.concatenate(visible_corners, axis=0)


def extract_bbox_from_contour(
    contour: np.ndarray, width: int, height: int, stretch: int = 10, clamp_option: bool = True
) -> Tuple[int, int, int, int]:
    """
    Extract bounding box coordinates from contour points.

    Args:
        contour: Array of contour points.
        width: Image width.
        height: Image height.
        stretch: Padding around bounding box.
        clamp_option: Whether to clamp coordinates to image dimensions.

    Returns:
        Tuple of (x1, y1, x2, y2) coordinates defining bounding box.
    """
    if clamp_option:
        x1 = np.clip(min(contour[:, 0]) - stretch, 0, width - 1)
        y1 = np.clip(min(contour[:, 1]) - stretch, 0, height - 1)

        x2 = np.clip(max(contour[:, 0]) + stretch, 0, width - 1)
        y2 = np.clip(max(contour[:, 1]) + stretch, 0, height - 1)
    else:
        x1 = min(contour[:, 0]) - stretch
        y1 = min(contour[:, 1]) - stretch

        x2 = max(contour[:, 0]) + stretch
        y2 = max(contour[:, 1]) + stretch

    return x1, y1, x2, y2


def extract_occluded_corner(
    corners: np.ndarray, matting: np.ndarray, screen_box: np.ndarray
) -> Tuple[Optional[OccludedCorner], np.ndarray]:
    """
    Extract occluded corner based on matting mask.

    Args:
        corners: Array of 4 corner coordinates, shape [4,2].
        matting: Gray scale matting image as float32.
        screen_box: Screen bounding box coordinates [x1,y1,x2,y2].

    Returns:
        Tuple containing:
            - Occluded corner type if found, None otherwise
            - Updated corners array with occluded corner marked as dummy
    """
    # process matte using screen box (reduce 40% compare with not using screen box)
    roi_matting = matting[screen_box[1] : screen_box[3], screen_box[0] : screen_box[2]]
    roi_alpha = remove_sparse_noise_matte(roi_matting)
    roi_alpha = dilate(roi_alpha, crop_mode=True)
    alpha = np.zeros_like(matting, dtype=np.float32)
    alpha[screen_box[1] : screen_box[3], screen_box[0] : screen_box[2]] = roi_alpha

    new_corners = []

    occluded_corner = None

    for i, corner in enumerate(corners):
        corner = np.array(corner).reshape(1, -1)
        if not is_dummy_corner(corner) and alpha[corner[0, 1], corner[0, 0]] == 0:
            new_corners.append(corner)
        else:
            new_corners.append(DUMMY_CORNER)
            occluded_corner = occluded_corner_dict[i]

    return occluded_corner, np.concatenate(new_corners, axis=0)


def calculate_angle_difference_quadrilateral(points: np.ndarray) -> float:
    """
    Calculate difference between max and min angles in quadrilateral.

    Args:
        points: Array of 4 corner points defining quadrilateral.

    Returns:
        Difference between largest and smallest angles in degrees.
    """
    angles_degrees = calculate_angles(points)
    return np.max(angles_degrees) - np.min(angles_degrees)


def calculate_angles(points: np.ndarray) -> List[float]:
    """
    Calculate interior angles of polygon.

    Args:
        points: Array of 3 or 4 points defining polygon vertices.

    Returns:
        List of interior angles in degrees.
    """
    if points.shape[0] == 4:
        vectors = [
            points[3] - points[0],
            points[1] - points[0],
            points[0] - points[1],
            points[2] - points[1],
            points[3] - points[2],
            points[1] - points[2],
            points[0] - points[3],
            points[2] - points[3],
        ]
    elif points.shape[0] == 3:
        vectors = [
            points[2] - points[0],
            points[1] - points[0],
            points[0] - points[1],
            points[2] - points[1],
            points[0] - points[2],
            points[1] - points[2],
        ]
    else:
        raise ValueError('points must have length 4 or 3')

    angles = [
        np.arctan2(
            np.linalg.det([vectors[i], vectors[i + 1]]),
            np.dot(vectors[i], vectors[i + 1]),
        )
        for i in range(0, len(vectors), 2)
    ]

    angles_degrees = np.abs(np.degrees(angles))
    return angles_degrees


def extract_roi_image(
    image: np.ndarray, corners: np.array, bbox: Tuple[int, int, int, int], patch_ratio: float = 0.3
) -> List[Optional[np.ndarray]]:
    """
    Extract ROI patches around corners from image.

    Args:
        image: Input image array.
        corners: Array of corner coordinates.
        bbox: Bounding box coordinates (x0,y0,x1,y1).
        patch_ratio: Size of ROI patches relative to bounding box.

    Returns:
        List of extracted image patches for each corner, None for missing corners.
    """
    x0, y0, x1, y1 = bbox
    patch_width, patch_height = int((x1 - x0) * patch_ratio), int((y1 - y0) * patch_ratio)

    if corners[0] is not None:
        top_left_image = image[y0 : corners[0][1] + patch_height, x0 : corners[0][0] + patch_width][::-1, :, :]
    else:
        top_left_image = None

    if corners[1] is not None:
        bot_left_image = image[corners[1][1] - patch_height : y1, x0 : corners[1][0] + patch_width]
    else:
        bot_left_image = None

    if corners[2] is not None:
        bot_right_image = image[corners[2][1] - patch_height : y1, corners[2][0] - patch_width : x1][:, ::-1, :]
    else:
        bot_right_image = None

    if corners[3] is not None:
        top_right_image = image[y0 : corners[3][1] + patch_height, corners[3][0] - patch_width : x1][::-1, ::-1, :]
    else:
        top_right_image = None

    return [top_left_image, bot_left_image, bot_right_image, top_right_image]


def simple_stable_corner(previous_corners: np.ndarray, corners: np.ndarray, stable_distance_threshold: float) -> np.ndarray:
    """
    Check if corners are stable compared to previous frame.

    Args:
        previous_corners: Corner coordinates from previous frame.
        corners: Current corner coordinates.
        stable_distance_threshold: Maximum allowed movement of corners.

    Returns:
        Previous corners if movement is below threshold, otherwise current corners.
    """
    total_distance = 0
    for previous_corner, corner in zip(previous_corners, corners):
        previous_corner = previous_corner.reshape(1, -1)
        corner = corner.reshape(1, -1)
        if not is_dummy_corner(previous_corner) and not is_dummy_corner(corner):
            # total_distance += np.sqrt(np.sum(np.square(previous_corner - corner)))
            total_distance += math.sqrt(
                (previous_corner[0, 0] - corner[0, 0]) ** 2 + (previous_corner[0, 1] - corner[0, 1]) ** 2
            )

    if total_distance < stable_distance_threshold:
        corners = previous_corners
    return corners


def simple_detect_outlier(previous_corners: np.ndarray, corners: np.ndarray, outlier_threshold: float = 6) -> np.ndarray:
    """
    Detect and handle outlier corners by comparing with previous frame.

    Args:
        previous_corners: Corner coordinates from previous frame.
        corners: Current corner coordinates.
        outlier_threshold: Distance threshold for detecting outliers.

    Returns:
        Corners array with outliers corrected.
    """
    assert find_dummy_index(corners) == -1
    if find_dummy_index(previous_corners) != -1:
        return corners

    distance_list = []
    for previous_corner, corner in zip(previous_corners, corners):
        distance = math.sqrt((previous_corner[0] - corner[0]) ** 2 + (previous_corner[1] - corner[1]) ** 2)
        distance_list.append(distance)

    if distance_list.count(0) != 3:
        distance_list = [max(value - outlier_threshold, 0) for value in distance_list]

    if distance_list.count(0) == 3:
        return previous_corners
    else:
        return corners


def find_occluded_corner(
    corners: np.ndarray,
    contour: np.ndarray,
    screen_mask: np.ndarray,
    angle_distortion_threshold: float,
    distance_noise_threshold: float,
) -> np.ndarray:
    """
    Find occluded corner position using visible corners and screen contour.

    Args:
        corners: Array of corner coordinates.
        contour: Screen contour points.
        screen_mask: Binary screen mask.
        angle_distortion_threshold: Maximum allowed angle distortion.
        distance_noise_threshold: Distance threshold for noise filtering.

    Returns:
        Updated corners array with occluded corner position estimated.
    """
    dummy_index = find_dummy_index(corners)
    if dummy_index == -1:
        return corners

    occluded_corner = DUMMY_CORNER
    visible_corners = get_visible_corners(corners)
    angles = calculate_angles(visible_corners)
    noise_corners = np.stack(
        [point for point in contour if not any(np.array_equal(point, corner) for corner in visible_corners)], axis=0
    )

    if max(angles) <= angle_distortion_threshold and len(noise_corners) > 0:
        if len(noise_corners) == 1:
            noise_corners = find_noise_corners(contour, noise_corners[0], screen_mask)
        if len(noise_corners) > 1:
            nearest_corners = find_nearest_corners(
                corners=corners, noise_corners=noise_corners, distance_noise_threshold=distance_noise_threshold
            )
            occluded_corner = find_occluded_corner_intersection(*nearest_corners)

    if is_dummy_corner(occluded_corner):
        occluded_corner = find_occluded_corner_parallelogram(visible_corners)

    corners[dummy_index] = occluded_corner
    return corners


def find_noise_corners(
    contour: np.ndarray,
    noise_corner: np.ndarray,
    screen_mask: np.ndarray,
    edge_factor: float = 0.25,
) -> List[np.ndarray]:
    """
    Find additional noise corner points around an initial noise corner.

    Args:
        contour: Screen contour points.
        noise_corner: Initial noise corner point.
        screen_mask: Binary screen mask.
        edge_factor: Factor for determining search region size.

    Returns:
        List of detected noise corner points.
    """

    x_diff = contour[:, 0][:, np.newaxis] - contour[:, 0]
    y_diff = contour[:, 1][:, np.newaxis] - contour[:, 1]
    distances = np.sqrt(x_diff**2 + y_diff**2)
    np.fill_diagonal(distances, np.inf)
    height_screen = np.min(distances)
    edge = edge_factor * height_screen

    square_coordinates = np.array(
        [
            [noise_corner[0] - edge, noise_corner[1] - edge],
            [noise_corner[0] - edge, noise_corner[1] + edge],
            [noise_corner[0] + edge, noise_corner[1] + edge],
            [noise_corner[0] + edge, noise_corner[1] - edge],
        ],
        dtype=np.int32,
    ).reshape((-1, 1, 2))

    cropped_screen_mask = screen_mask.copy()
    cv2.fillPoly(cropped_screen_mask, [square_coordinates], (0, 0, 0))

    noise_corners = [corner for corner in find_low_epsilon_contour(cropped_screen_mask)]
    noise_corners.append(noise_corner)

    return noise_corners


def find_occluded_corner_parallelogram(corners: np.ndarray) -> np.ndarray:
    """
    Estimate occluded corner position assuming parallelogram shape.

    Args:
        corners: Array of 3 visible corner coordinates.

    Returns:
        Estimated position of occluded corner.
    """
    corner0, corner1, corner2 = corners
    corner3_1 = corner2[0] + corner1[0] - corner0[0], corner2[1] + corner1[1] - corner0[1]
    corner3_2 = corner0[0] + corner2[0] - corner1[0], corner0[1] + corner2[1] - corner1[1]
    corner3_3 = corner1[0] + corner0[0] - corner2[0], corner1[1] + corner0[1] - corner2[1]

    corner3_list = [corner3_1, corner3_2, corner3_3]

    angle_diff_list = [
        calculate_angle_difference_quadrilateral(np.stack([corner0, corner1, corner2, corner3], axis=0))
        for corner3 in corner3_list
    ]

    corner3 = corner3_list[np.argmin(angle_diff_list)]

    return np.array(corner3)


def find_occluded_corner_intersection(
    corner1_1: np.ndarray, corner1_2: np.ndarray, corner2_1: np.ndarray, corner2_2: np.ndarray
) -> Optional[np.ndarray]:
    """
    Find occluded corner as intersection of two lines.

    Args:
        corner1_1: First point on first line.
        corner1_2: Second point on first line.
        corner2_1: First point on second line.
        corner2_2: Second point on second line.

    Returns:
        Intersection point coordinates or dummy corner if lines are parallel.
    """
    x1, y1 = corner1_1
    x2, y2 = corner1_2
    x3, y3 = corner2_1
    x4, y4 = corner2_2

    det = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)

    if det == 0:
        return DUMMY_CORNER

    intersection_x = ((x1 * y2 - y1 * x2) * float(x3 - x4) - float(x1 - x2) * (x3 * y4 - y3 * x4)) / det
    intersection_y = ((x1 * y2 - y1 * x2) * float(y3 - y4) - float(y1 - y2) * (x3 * y4 - y3 * x4)) / det

    return np.int32(np.round(np.array([intersection_x, intersection_y]))).reshape(1, -1)


def find_nearest_corners(
    corners: np.ndarray, noise_corners: np.ndarray, distance_noise_threshold: float
) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
    """
    Find nearest noise corners to visible corners.

    Args:
        corners: Array of corner coordinates with one dummy corner.
        noise_corners: Array of noise corner coordinates.
        distance_noise_threshold: Threshold for filtering distant noise.

    Returns:
        Tuple of four corner coordinates used for intersection calculation.
    """
    adjacent_corner_dict = {
        0: [1, 3],
        1: [0, 2],
        2: [3, 1],
        3: [2, 0],
    }

    adjacent_corner_indices = adjacent_corner_dict[find_dummy_index(corners)]

    nearest_corners = []
    for i, corner_index in enumerate(adjacent_corner_indices):
        sub_corner = find_nearest_corner(
            noise_corners=noise_corners,
            corner=corners[corner_index],
            axis=i,
            distance_noise_threshold=distance_noise_threshold,
        )
        nearest_corners += [corners[corner_index], sub_corner]

    return nearest_corners


def find_nearest_corner(
    noise_corners: np.ndarray, corner: np.ndarray, axis: int, distance_noise_threshold: float
) -> np.ndarray:
    """
    Find nearest noise corner to visible corner along specified axis.

    Args:
        noise_corners: Array of noise corner coordinates.
        corner: Reference corner coordinates.
        axis: Axis index (0 for x, 1 for y).
        distance_noise_threshold: Threshold for filtering distant noise.

    Returns:
        Coordinates of nearest valid noise corner.
    """
    points = [p for p in noise_corners if cal_distance(p, corner) > distance_noise_threshold]
    points = np.array(points)

    distances = np.abs(points[:, axis] - corner[axis])
    nearest_point_index = np.argmin(distances)
    nearest_point = points[nearest_point_index]
    return nearest_point


def cal_distance(point1: Union[list, tuple, np.array], point2: Union[list, tuple, np.array]) -> float:
    """
    Calculate Euclidean distance between two points.

    Args:
        point1: First point coordinates.
        point2: Second point coordinates.

    Returns:
        Euclidean distance between points.
    """
    return ((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2) ** 0.5


def is_hidden(corners: np.ndarray, width: int) -> Optional[HiddenEdge]:
    """
    Check if screen edge is hidden at image boundary.

    Args:
        corners: Array of corner coordinates.
        width: Image width.

    Returns:
        HiddenEdge enum indicating which edge is hidden, or None.
    """
    left_hidden = any(np.where(np.array(corners)[:2, 0] == 0, True, False))
    right_hidden = any(np.where(np.array(corners)[2:, 0] >= width - 1, True, False))

    if left_hidden:
        return HiddenEdge.LEFT
    elif right_hidden:
        return HiddenEdge.RIGHT
    else:
        return None


def find_hidden_edge(previous_corners: np.ndarray, corners: np.ndarray, width: int) -> Tuple[np.ndarray, HiddenEdge]:
    """
    Find hidden screen edge at image boundary.

    Args:
        previous_corners: Corner coordinates from previous frame.
        corners: Current corner coordinates.
        width: Image width.

    Returns:
        Tuple of corrected corners and detected hidden edge type.
    """
    corners = simple_detect_outlier(previous_corners=previous_corners, corners=corners)  # TODO: just work with static screen
    hidden_edge = is_hidden(corners=corners, width=width)
    return corners, hidden_edge


def find_expansion_ranges(corners: np.ndarray, contour: np.ndarray) -> Tuple[np.ndarray, float, float]:
    """
    Calculate screen expansion ranges based on contour.

    Args:
        corners: Array of corner coordinates.
        contour: Screen contour points.

    Returns:
        Tuple containing:
            - Fitted corner coordinates
            - Maximum x expansion
            - Maximum y expansion
    """
    fitted_corners = fittest_expanding(corners, contour)
    diff = np.abs(np.array(corners) - np.array(fitted_corners))
    expansion_width, expansion_height = np.max(diff, axis=0)
    return fitted_corners, expansion_width, expansion_height


def fittest_expanding(coordinates: np.ndarray, contour: np.ndarray) -> np.ndarray:
    """
    Find best fitting expanded corners using contour points.

    Args:
        coordinates: Array of corner coordinates.
        contour: Screen contour points.

    Returns:
        Array of fitted corner coordinates.
    """
    w_ab, c_ab = line_from_points(coordinates[0], coordinates[1])
    w_bc, c_bc = line_from_points(coordinates[1], coordinates[2])
    w_cd, c_cd = line_from_points(coordinates[2], coordinates[3])
    w_da, c_da = line_from_points(coordinates[3], coordinates[0])

    c_vector = np.array([c_ab, c_bc, c_cd, c_da]).reshape(-1, 4)
    w_vector = np.stack([w_ab, w_bc, w_cd, w_da], -1)

    ab_closest_point, bc_closest_point, cd_closest_point, da_closest_point = contour[
        ((contour.dot(w_vector) + c_vector) / (np.linalg.norm(w_vector, axis=0).reshape(-1, 4) + 1e-6)).argmin(0)
    ]

    new_c_ab = -w_ab.dot(ab_closest_point) + 2.0 * np.linalg.norm(w_ab)
    new_c_bc = -w_bc.dot(bc_closest_point) + 2.0 * np.linalg.norm(w_bc)
    new_c_cd = -w_cd.dot(cd_closest_point) + 2.0 * np.linalg.norm(w_cd)
    new_c_da = -w_da.dot(da_closest_point) + 2.0 * np.linalg.norm(w_da)

    expanded_a_coord = find_intersection((w_da, new_c_da), (w_ab, new_c_ab))
    expanded_b_coord = find_intersection((w_ab, new_c_ab), (w_bc, new_c_bc))
    expanded_c_coord = find_intersection((w_bc, new_c_bc), (w_cd, new_c_cd))
    expanded_d_coord = find_intersection((w_cd, new_c_cd), (w_da, new_c_da))

    return np.stack([expanded_a_coord, expanded_b_coord, expanded_c_coord, expanded_d_coord], axis=0)


def line_from_points(p1: np.ndarray, p2: np.ndarray) -> Tuple[np.ndarray, float]:
    """
    Get line equation parameters from two points.

    Args:
        p1: First point coordinates.
        p2: Second point coordinates.

    Returns:
        Tuple containing:
            - Normal vector of line
            - Constant term of line equation
    """
    x1, y1 = p1
    x2, y2 = p2
    k1 = y2 - y1
    k2 = -(x2 - x1)

    c = -(k1 * x1 + k2 * y1)
    w = np.array([k1, k2])
    return w, c


def find_intersection(line1_coeffs: Tuple[np.ndarray, float], line2_coeffs: Tuple[np.ndarray, float]) -> np.ndarray:
    """
    Find intersection point of two lines from their equations.

    Args:
        line1_coeffs: First line coefficients (normal vector, constant).
        line2_coeffs: Second line coefficients (normal vector, constant).

    Returns:
        Coordinates of intersection point, or dummy point if lines are parallel.
    """
    w1, c1 = line1_coeffs
    w2, c2 = line2_coeffs

    # Create the coefficient matrix for the system of equations
    coef_a = np.array([w1, w2])

    # Create the constant matrix
    coef_b = np.array([-c1, -c2])

    try:
        intersection_point = np.linalg.solve(coef_a, coef_b)
        return intersection_point
    except np.linalg.LinAlgError:
        return DUMMY_CORNER.reshape(
            2,
        )
