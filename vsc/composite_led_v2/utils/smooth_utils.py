from typing import List, Optional, Tu<PERSON>, Union

import numpy as np
from scipy.ndimage import gaussian_filter
from scipy.signal import butter, savgol_filter, sosfiltfilt

from vsc.composite_led_v2.datastruct import HiddenEdge, ScreenOrientation
from vsc.composite_led_v2.utils.corner_utils import is_dummy_corners
from vsc.composite_led_v2.utils.screen_utils import corners_screen_check


def is_consecutive(frame_ids: List[int]) -> bool:
    """
    Check if a list of frame IDs is consecutive.

    Args:
        frame_ids: List of integer frame IDs to check

    Returns:
        bool: True if frame IDs are consecutive, False otherwise
    """
    return all(x == y - 1 for x, y in zip(frame_ids[:-1], frame_ids[1:]))


def get_dimension(corners: np.ndarray) -> Tuple[float, float]:
    """
    Calculate width and height from corner coordinates.

    Args:
        corners: Array of 4 corner coordinates with shape (4,2)

    Returns:
        tuple: (width, height) of the rectangle defined by corners
    """
    width = (corners[3][0] - corners[0][0] + corners[2][0] - corners[1][0]) / 2
    height = (corners[1][1] - corners[0][1] + corners[2][1] - corners[3][1]) / 2
    return width, height


def find_intervals(list_corners: np.ndarray) -> List[Tuple[int, int, Union[bool, np.bool_]]]:
    """
    Find intervals of consecutive dummy/non-dummy corners.

    Args:
        list_corners: Array of corner coordinates

    Returns:
        list: List of tuples containing (start_index, end_index, is_valid_corner)
    """
    intervals = []
    start = 0
    while start < list_corners.shape[0]:
        end = start
        while end + 1 < list_corners.shape[0] and is_dummy_corners(list_corners[end + 1]) == is_dummy_corners(
            list_corners[start]
        ):
            end += 1

        intervals.append((start, end + 1, not is_dummy_corners(list_corners[start])))
        start = end + 1
    return intervals


def get_ratio_for_appearing(list_corners: np.ndarray, hidden_indices: List[int]) -> Optional[float]:
    """
    Get aspect ratio for corners appearing after being hidden.

    Args:
        list_corners: Array of corner coordinates
        hidden_indices: List of indices where corners were hidden

    Returns:
        float: Width/height ratio, or None if cannot be calculated
    """
    assert is_consecutive(hidden_indices)
    end = hidden_indices[-1]

    if end == list_corners.shape[0] - 1 or is_dummy_corners(list_corners[end + 1]):
        return None

    width, height = get_dimension(list_corners[end + 1])

    return width / height


def get_ratio_for_hiding(list_corner: np.ndarray, hidden_indices: List[int]) -> Optional[float]:
    """
    Get aspect ratio for corners before being hidden.

    Args:
        list_corner: Array of corner coordinates
        hidden_indices: List of indices where corners were hidden

    Returns:
        float: Width/height ratio, or None if cannot be calculated
    """
    assert is_consecutive(hidden_indices)
    start = hidden_indices[0]

    if start == 0 or is_dummy_corners(list_corner[start - 1]):
        return None

    width, height = get_dimension(list_corner[start - 1])

    return width / height


def get_ratio(list_corners: np.ndarray, hidden_indices: List[int], default_ratio: float = 16 / 9) -> float:
    """
    Get aspect ratio for corners, with fallback to default.

    Args:
        list_corners: Array of corner coordinates
        hidden_indices: List of indices where corners were hidden
        default_ratio: Default ratio to use if cannot be calculated

    Returns:
        float: Width/height ratio
    """
    ratio = get_ratio_for_appearing(list_corners, hidden_indices)
    if ratio is None:
        ratio = get_ratio_for_hiding(list_corners, hidden_indices)
    if ratio is None:
        ratio = default_ratio
    return ratio


def based_on_length_and_direction(p1: np.ndarray, p2: np.ndarray, p3: np.ndarray, actual_length: float) -> np.ndarray:
    """
    Calculate target point based on vector direction and length.

    Args:
        p1: Starting point coordinates
        p2: Vector start point
        p3: Vector end point
        actual_length: Target vector length

    Returns:
        ndarray: Target point coordinates
    """
    if np.all(p2 == p3):
        return p2
    p2p3 = p3 - p2
    p2p3_length = np.linalg.norm(p2p3)
    p2p3_unit = p2p3 / p2p3_length
    p2p3_target = actual_length * p2p3_unit
    target = (p1 + p2p3_target).astype(np.int32)
    return target


def find_hidden_edge_corners(list_corners: np.ndarray, list_hidden_edge: List[Optional[HiddenEdge]]) -> np.ndarray:
    """
    Find and reconstruct hidden edge corners.

    Args:
        list_corners: Array of corner coordinates
        list_hidden_edge: List specifying which edges are hidden

    Returns:
        ndarray: Array of reconstructed corner coordinates
    """

    def __find_hidden_edge_corners(
        __list_corners: np.ndarray, __list_hidden_edge: List[Optional[HiddenEdge]], __hidden_edge: HiddenEdge
    ) -> np.ndarray:
        hidden_indices = sorted([i for i in range(len(__list_corners)) if list_hidden_edge[i] == __hidden_edge])
        if len(hidden_indices) > 0 and is_consecutive(hidden_indices):
            radio_hidden = get_ratio(__list_corners, hidden_indices)
            __list_corners[hidden_indices] = __detect_hidden_edge_corners(
                __list_corners[hidden_indices], __hidden_edge, radio_hidden
            )
        return __list_corners

    def __detect_hidden_edge_corners(__list_corners: np.ndarray, __hidden_edge: HiddenEdge, __ratio: float) -> np.ndarray:
        new_list_corners = []
        for corners in __list_corners:
            new_corners = corners.copy()

            _, height = get_dimension(corners)
            real_width = int(__ratio * height)

            if __hidden_edge == HiddenEdge.LEFT:
                new_corners[0] = based_on_length_and_direction(corners[3], corners[3], corners[0], real_width)
                new_corners[1] = based_on_length_and_direction(corners[2], corners[2], corners[1], real_width)
            elif __hidden_edge == HiddenEdge.RIGHT:
                new_corners[2] = based_on_length_and_direction(corners[1], corners[1], corners[2], real_width)
                new_corners[3] = based_on_length_and_direction(corners[0], corners[0], corners[3], real_width)

            new_list_corners.append(new_corners)

        return np.array(new_list_corners)

    list_corners = __find_hidden_edge_corners(list_corners, list_hidden_edge, HiddenEdge.LEFT)
    list_corners = __find_hidden_edge_corners(list_corners, list_hidden_edge, HiddenEdge.RIGHT)

    return list_corners


def stabilize_corners(
    list_corners: np.ndarray, window_size: int = 30, std_threshold: int = 5, distance_threshold: int = 10
) -> np.ndarray:
    """
    Stabilize corner coordinates using windowed statistics.

    Args:
        list_corners: Array of corner coordinates
        window_size: Size of sliding window for statistics
        std_threshold: Standard deviation threshold for stabilization
        distance_threshold: Maximum allowed distance for stabilization

    Returns:
        ndarray: Array of stabilized corner coordinates
    """
    n_samples, n_coords, coord_dim = list_corners.shape

    shifted_corners = np.stack(
        [np.concatenate((list_corners[:i], list_corners[:-i])) for i in range(1, window_size + 1)], axis=0
    )

    corners_expanded = list_corners[np.newaxis, :, :, :]
    squared_diffs = (corners_expanded - shifted_corners) ** 2
    coord_diffs = np.sqrt(np.sum(squared_diffs, axis=3))  # Sum across coord dimensions

    # Sum differences across coordinates
    total_diffs = np.sum(coord_diffs, axis=2)  # Shape: (window_size, n_samples)

    # Calculate standard deviation across window sizes
    diff_std = np.std(total_diffs, axis=0, ddof=1)  # Shape: (n_samples,)

    # Create stable corners using a rolling check for stability
    stable_corners = list_corners.copy()

    # Create mask where both current and previous std are below threshold
    std_mask = np.zeros(n_samples, dtype=bool)
    std_mask[1:] = (diff_std[1:] < std_threshold) & (diff_std[:-1] < std_threshold)

    # Apply stable corners based on cumulative stability
    # This is harder to fully vectorize due to the sequential nature
    # But we can optimize the inner loop
    for i in range(1, n_samples):
        if std_mask[i]:
            stable_corners[i] = stable_corners[i - 1]

    # Calculate distances between original and stabilized corners
    diff_squared = (list_corners - stable_corners) ** 2
    distances = np.sqrt(np.sum(diff_squared, axis=2))  # Shape: (n_samples, n_coords)

    # Create mask and apply threshold
    threshold_mask = (distances < distance_threshold)[:, :, np.newaxis]
    result = np.where(threshold_mask, stable_corners, list_corners)

    return result


def replace_dummy_corners(list_corners: np.ndarray) -> np.ndarray:
    """
    Replace dummy corners with interpolated values.

    Args:
        list_corners: Array of corner coordinates

    Returns:
        ndarray: Array with interpolated dummy corners
    """
    for i in range(1, list_corners.shape[0] - 1):
        if (
            is_dummy_corners(list_corners[i])
            and not is_dummy_corners(list_corners[i - 1])
            and not is_dummy_corners(list_corners[i + 1])
        ):
            list_corners[i] = (list_corners[i - 1] + list_corners[i + 1]) / 2

    return list_corners


def run_butterworth_filter(list_corners: np.ndarray, butter_order: int, butter_cutoff_freq: float) -> np.ndarray:
    """
    Apply Butterworth filter to corner coordinates.

    Args:
        list_corners: Array of corner coordinates
        butter_order: Order of Butterworth filter
        butter_cutoff_freq: Cutoff frequency for filter

    Returns:
        ndarray: Filtered corner coordinates
    """
    sos = butter(butter_order, butter_cutoff_freq, btype="low", analog=False, output="sos")
    default_pad_len = 3 * (2 * len(sos) + 1 - min(np.sum(sos[:, 2] == 0), np.sum(sos[:, 5] == 0)))
    pad_len = default_pad_len if default_pad_len < list_corners.shape[0] else list_corners.shape[0] // 2

    results = []
    for i in range(list_corners.shape[1]):
        x_filtered = sosfiltfilt(sos, list_corners[:, i, 0], padlen=pad_len)
        y_filtered = sosfiltfilt(sos, list_corners[:, i, 1], padlen=pad_len)
        result = np.stack([x_filtered, y_filtered], axis=-1)
        results.append(result)

    return np.stack(results, axis=1)


def run_savgol_filter(list_corners: np.ndarray, window_length: int, poly_order: int, mode: str) -> np.ndarray:
    """
    Apply Savitzky-Golay filter to corner coordinates.

    Args:
        list_corners: Array of corner coordinates
        window_length: Length of filter window
        poly_order: Order of polynomial
        mode: Border handling mode

    Returns:
        ndarray: Filtered corner coordinates
    """
    if list_corners.shape[0] < window_length and mode == 'interp':
        mode = 'nearest'

    results = []
    for i in range(list_corners.shape[1]):
        x_filtered = savgol_filter(list_corners[:, i, 0], window_length=window_length, polyorder=poly_order, mode=mode)
        y_filtered = savgol_filter(list_corners[:, i, 1], window_length=window_length, polyorder=poly_order, mode=mode)

        result = np.stack([x_filtered, y_filtered], axis=-1)
        results.append(result)

    return np.stack(results, axis=1)


def run_gaussian_filter(list_corners: np.ndarray, sigma: float, mode: str) -> np.ndarray:
    """
    Apply Gaussian filter to corner coordinates.

    Args:
        list_corners: Array of corner coordinates
        sigma: Standard deviation for Gaussian kernel
        mode: Border handling mode

    Returns:
        ndarray: Filtered corner coordinates
    """
    results = []
    for i in range(list_corners.shape[1]):
        x_filtered = gaussian_filter(list_corners[:, i, 0], sigma=sigma, mode=mode)
        y_filtered = gaussian_filter(list_corners[:, i, 1], sigma=sigma, mode=mode)

        result = np.stack([x_filtered, y_filtered], axis=-1)
        results.append(result)

    return np.stack(results, axis=1)


def run_filters(
    list_corners: np.ndarray,
    savgol_window_length: int,
    savgol_poly_order: int,
    savgol_padding_mode: str,
    butter_order: int,
    butter_cutoff_freq: float,
    gauss_sigma: float,
    gauss_padding_mode: str,
) -> np.ndarray:
    """
    Apply multiple filters sequentially to corner coordinates.

    Args:
        list_corners: Array of corner coordinates
        savgol_window_length: Window length for Savitzky-Golay filter
        savgol_poly_order: Polynomial order for Savitzky-Golay filter
        savgol_padding_mode: Border mode for Savitzky-Golay filter
        butter_order: Order for Butterworth filter
        butter_cutoff_freq: Cutoff frequency for Butterworth filter
        gauss_sigma: Standard deviation for Gaussian filter
        gauss_padding_mode: Border mode for Gaussian filter

    Returns:
        ndarray: Filtered corner coordinates
    """
    intervals = find_intervals(list_corners)

    for start, end, screen_visible_flag in intervals:
        if screen_visible_flag:
            list_corners[start:end] = run_butterworth_filter(
                list_corners[start:end], butter_order=butter_order, butter_cutoff_freq=butter_cutoff_freq
            )

            list_corners[start:end] = run_savgol_filter(
                list_corners[start:end],
                window_length=savgol_window_length,
                poly_order=savgol_poly_order,
                mode=savgol_padding_mode,
            )

            list_corners[start:end] = run_gaussian_filter(list_corners[start:end], sigma=gauss_sigma, mode=gauss_padding_mode)

    return list_corners


def run_cma_smoothing(
    list_corners: np.ndarray,
    savgol_window_length: int,
    savgol_poly_order: int,
    savgol_padding_mode: str,
    butter_order: int,
    butter_cutoff_freq: float,
    gauss_sigma: float,
    gauss_padding_mode: str,
    moving_screen_threshold: float,
    width: int,
):
    """
    Apply conditional multi-filter smoothing to corner coordinates.

    Args:
        list_corners: Array of corner coordinates
        savgol_window_length: Window length for Savitzky-Golay filter
        savgol_poly_order: Polynomial order for Savitzky-Golay filter
        savgol_padding_mode: Border mode for Savitzky-Golay filter
        butter_order: Order for Butterworth filter
        butter_cutoff_freq: Cutoff frequency for Butterworth filter
        gauss_sigma: Standard deviation for Gaussian filter
        gauss_padding_mode: Border mode for Gaussian filter
        moving_screen_threshold: Threshold for screen movement detection
        width: Image width for screen orientation check

    Returns:
        ndarray: Filtered corner coordinates
    """
    if corners_screen_check(list_corners, threshold=moving_screen_threshold, width=width) is ScreenOrientation.ZOOMED:
        return run_filters(
            list_corners,
            savgol_window_length=savgol_window_length,
            savgol_poly_order=savgol_poly_order,
            savgol_padding_mode=savgol_padding_mode,
            butter_order=butter_order,
            butter_cutoff_freq=butter_cutoff_freq,
            gauss_sigma=gauss_sigma,
            gauss_padding_mode=gauss_padding_mode,
        )
    else:
        return list_corners
