import os
from typing import Optional

import cv2
import numpy as np
from fvutils.media import (
    get_video_fps,
    get_video_properties,
    has_audio,
    split_audio_from_video,
)

from vsc.composite_led_v2.constants import DEFAULT_BITRATE, DEFAULT_FPS, VIDEO_EXT
from vsc.composite_led_v2.datastruct import (
    ImageProperties,
    LedProperties,
    LedType,
    VideoProperties,
)
from vsc.composite_led_v2.video_player import VideoPlayer
from vsc.utils.logger import logger


class Led:
    """
    Handles LED media content (image or video) operations.

    This class manages operations for LED content including reading, writing, and
    property extraction for both image and video formats.

    Args:
        led_path (str): Path to the LED media file
        data_dir (str): Directory for storing data files
        led_properties (Optional[LedProperties]): Pre-defined LED properties
    """

    def __init__(self, led_path: str, data_dir, led_properties: Optional[LedProperties]):
        self.__led_path: str = led_path
        self.__data_dir = data_dir

        self.__audio_path = os.path.join(self.__data_dir, "led_audio.wav")

        self.__led_properties: LedProperties = (
            led_properties if led_properties is not None else self.__extract_led_properties()
        )

        self.__led_video_player: VideoPlayer = VideoPlayer(
            self.__led_path,
            fps=self.__led_properties.properties.fps if self.__led_properties.type is LedType.VIDEO else DEFAULT_FPS,
            size=(self.__led_properties.properties.frame_width, self.__led_properties.properties.frame_height),
            bitrate=(
                self.__led_properties.properties.bit_rate if self.__led_properties.type is LedType.VIDEO else DEFAULT_BITRATE
            ),
        )

        self.__last_frame: Optional[np.ndarray] = None  # last frame for led image

    def __extract_led_type(self) -> LedType:
        """
        Determines the LED content type based on file extension.

        Returns:
            LedType: Type of LED content (VIDEO or IMAGE)
        """
        _, ext = os.path.splitext(self.__led_path)
        if ext[1:] in VIDEO_EXT:
            return LedType.VIDEO
        else:
            return LedType.IMAGE

    def __extract_led_properties(self) -> LedProperties:
        """
        Extracts properties from the LED media file.

        Returns:
            LedProperties: Properties of the LED content
        """
        led_type = self.__extract_led_type()
        if led_type == LedType.VIDEO:
            source_properties = get_video_properties(self.__led_path)
            fps = get_video_fps(self.__led_path)

            properties = VideoProperties(
                fps=fps,
                frame_width=source_properties['width'],
                frame_height=source_properties['height'],
                bit_rate=f"{int(int(source_properties['bit_rate']) / 1000)}k",
                total_frames=int(source_properties['nb_frames']),
            )

            led_properties = LedProperties(type=led_type, properties=properties)
            logger.info(f"Video LED has {str(properties)}")

        else:
            image = cv2.imread(self.__led_path)
            properties = ImageProperties(
                frame_width=image.shape[1],
                frame_height=image.shape[0],
            )

            led_properties = LedProperties(type=led_type, properties=properties)
            logger.info(f"Image LED has {str(properties)}")

        return led_properties

    def init_reader(self):
        """Initializes the reader for video content."""
        if self.__led_properties.type is LedType.VIDEO:
            self.__led_video_player.init_reader()

    def get_frame_data(self) -> np.ndarray:
        """
        Retrieves frame data from the LED content.

        Returns:
            np.ndarray: Frame data as a numpy array
        """
        if self.__led_properties.type is LedType.VIDEO:
            return self.__led_video_player.read()
        else:
            if self.__last_frame is None:
                self.__last_frame = cv2.imread(self.__led_path)
            return self.__last_frame

    def release_reader(self):
        """Releases the video reader resources."""
        if self.__led_properties.type is LedType.VIDEO:
            self.__led_video_player.release_reader()

    def init_writer(self):
        """Initializes the writer for video content."""
        if self.__led_properties.type is LedType.VIDEO:
            self.__led_video_player.init_writer()

    def set_frame_data(self, frame_data: np.ndarray) -> None:
        """
        Sets frame data for the LED content.

        Args:
            frame_data (np.ndarray): Frame data to be written
        """
        if self.__led_properties.type is LedType.VIDEO:
            self.__led_video_player.write(frame_data)
        else:
            if not os.path.isfile(self.__led_path):
                assert cv2.imwrite(self.__led_path, frame_data)

    def release_writer(self):
        """Releases the video writer resources."""
        if self.__led_properties.type is LedType.VIDEO:
            self.__led_video_player.release_writer()

    def get_led_path(self) -> str:
        """
        Gets the path to the LED media file.

        Returns:
            str: Path to the LED file
        """
        return self.__led_path

    def get_led_properties(self) -> LedProperties:
        """
        Gets the properties of the LED content.

        Returns:
            LedProperties: Properties of the LED content
        """
        return self.__led_properties

    def get_audio_path(self) -> str:
        """
        Gets the path to the extracted audio file.

        Returns:
            str: Path to the audio file
        """
        if has_audio(self.__led_path):
            split_audio_from_video(self.__led_path, self.__audio_path)
        return self.__audio_path
