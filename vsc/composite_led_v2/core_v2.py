import copy
from typing import Optional

import numpy as np
from tqdm import tqdm

from vsc.composite_led_v2.datastruct import ScreenOrientation
from vsc.composite_led_v2.models.blender.led_blender import LedBlender
from vsc.composite_led_v2.models.contour_detector.cv2_contour_detector import (
    ContourDetectorInference,
)
from vsc.composite_led_v2.models.corners_detector.corners_detector import (
    CornersDetector,
)
from vsc.composite_led_v2.models.corners_smoother.corners_smoother import (
    CornersSmoother,
)
from vsc.composite_led_v2.models.corners_validator.corners_validator import (
    CornersValidator,
)
from vsc.composite_led_v2.models.matting_detector.vc2 import VC2Inference
from vsc.composite_led_v2.models.occluded_detector.occluded_corners_detector import (
    OccludedCornersDetector,
)
from vsc.composite_led_v2.models.post_processor.post_processor import PostProcessor
from vsc.composite_led_v2.models.screen_checker.screen_checker import <PERSON><PERSON>hecker
from vsc.composite_led_v2.models.screen_segmentor.hsv import HSVScreenSegmentorInference
from vsc.composite_led_v2.session import Session
from vsc.composite_led_v2.utils.corner_utils import (
    extract_bbox_from_contour,
    find_hidden_edge,
    get_nb_visible_corners,
    is_dummy_corners,
)
from vsc.utils import cfg
from vsc.utils.logger import logger


class CompositeLEDInferenceV2:
    """
    Main class for Composite LED Inference pipeline version 2.

    This class handles the entire LED compositing process, including
    - Screen segmentation and detection
    - Corners detection and validation
    - Matting for screen isolation
    - Corners stabilization and smoothing
    - LED blending with the original video
    - Audio merging
    """

    def __init__(self, device: str = "cuda:0", weights_folder: str = "checkpoints"):
        """
        Initialize the Composite LED Inference pipeline.

        Args:
            device (str): Computing device to use (for example, 'cuda:0', 'cpu'), default is 'cuda:0'.
            weights_folder (str): Path to the folder containing model weights, the default is 'checkpoints'.
        """
        self.device = device

        self.matting_detector = VC2Inference(
            checkpoints=cfg.led.vc2.checkpoints,
            weights_folder=weights_folder,
            device=self.device,
        )

        self.screen_segmentor = HSVScreenSegmentorInference(
            color_range=cfg.led.mask.color_range,
        )

        self.contour_detector = ContourDetectorInference(low_epsilon=cfg.led.mask.low_epsilon)

        self.__mask_area_threshold = 0.001
        self.screen_checker = ScreenChecker(mask_area_threshold=self.__mask_area_threshold)

        self.__bbox_stretch = 20
        self.corners_detector = CornersDetector(bbox_stretch=self.__bbox_stretch)

        self.occluded_corners_detector = OccludedCornersDetector(
            matting_appearance_threshold=cfg.led.matte.matte_appearance_threshold,
            stable_distance_threshold=cfg.led.post_processing.stable_threshold,
            angle_distortion_threshold=cfg.led.angle_distortion_threshold,
            distance_noise_threshold=cfg.led.post_processing.distance_noise,
        )

        self.__larger_angle_validation_threshold = 65
        self.corners_validator = CornersValidator(
            checkpoint_id=cfg.led.corner_classification.checkpoint_id,
            weights_folder=weights_folder,
            hash_code=cfg.led.corner_classification.hash_code,
            angle_validation_threshold=cfg.led.post_processing.validation_threshold,
            larger_angle_validation_threshold=self.__larger_angle_validation_threshold,
            distance_validation_threshold=cfg.led.post_processing.green_screen_dimension_threshold,
            device=self.device,
        )

        self.corner_smoother = CornersSmoother(
            handle_shaking_threshold=cfg.led.handle_shaking.threshold,
            screen_moving_threshold=cfg.led.stabilize.screen_moving_threshold,
            angle_distortion_threshold=cfg.led.angle_distortion_threshold,
            window_size=cfg.led.stabilize.window_size,
            std_threshold=cfg.led.stabilize.std_thresh,
            distance_threshold=cfg.led.stabilize.distance_threshold,
            savgol_window_size_handle_outliers=cfg.led.smoothing.savgol_window_size_handle_outliers,
            savgol_window_size=cfg.led.smoothing.savgol_window_size,
            savgol_poly_order=cfg.led.smoothing.savgol_polyorder,
            savgol_padding_mode=cfg.led.smoothing.savgol_padding_mode,
            butterworth_order=cfg.led.smoothing.butterworth_order,
            butterworth_cutoff_frequency=cfg.led.smoothing.butterworth_cutoff_frequency,
            gaussian_sigma=cfg.led.smoothing.gaussian_sigma,
            gaussian_padding_mode=cfg.led.smoothing.gaussian_padding_mode,
        )

        self.post_processor = PostProcessor(
            pyramid_iters=cfg.led.mask.pyramid_iters,
            use_edge_preserving=cfg.led.mask.use_edge_preserving,
            sigma_s=cfg.led.mask.sigma_s,
            sigma_r=cfg.led.mask.sigma_r,
            bbox_stretch=cfg.led.mask.bbox_stretch,
            erode_kernel=cfg.led.mask.erode,
            timi_matte_removing_threshold=cfg.led.mask.timi_matte_threshold_for_removing_noise,
            color_range=cfg.led.mask.color_range,
        )

        self.blender = LedBlender(device=self.device)

    def extract_source_corners(self, session: Session):
        """
        Extract corners from source frames for LED composition.

        This method processes each frame to:
        1. Detect screen contours and masks
        2. Identify screen corners
        3. Handle occluded corners and validate corner positions
        4. Calculate expansion ratios for proper-LED fitting
        5. Store all detected data in the session

        Args:
            session (Session): Session object containing source video and processing state
        """
        width = session.source.get_source_properties().frame_width
        height = session.source.get_source_properties().frame_height

        first_occluded_corners, first_fully_corners = None, None

        # process
        session.source.init_source_reader()
        session.source.init_screen_mask_reader()
        session.source.init_timi_matte_writer()

        for key in tqdm(session.source.keys, desc="Extract source corners: "):

            frame = session.source.get_frame_data()
            screen_mask = session.source.get_screen_mask_data()

            # contour detection
            contours = session.source.get_contour_data(key)
            low_epsilon_contours = session.source.get_low_epsilon_contour_data(key)

            # screen detection
            screen_box = session.source.get_bounding_box_data(key)

            # exist check
            is_exist = session.source.get_screen_exist_data(key)

            if is_exist:
                # timi matting
                timi_matte = self.matting_detector.run([frame], None)[0]  # TODO: not use box here

                if session.get_screen_orientation() is not ScreenOrientation.STATIC or key == 0:
                    if (
                        session.get_screen_orientation() is not ScreenOrientation.STATIC
                        or session.source.get_metadata().corners is None
                    ):
                        corners = self.corners_detector.run(contours=contours, width=width, height=height)
                    else:
                        corners = session.source.get_metadata().corners  # use first frame corners from split

                    # occluded corner defining
                    occluded_corner, corners, matte_flag = self.occluded_corners_detector.run_occluded_corner_defining(
                        corners=corners, timi_matting=timi_matte, screen_box=screen_box[0]
                    )

                    # corners validation
                    nb_visible_corners = get_nb_visible_corners(corners)
                    corners = self.corners_validator.run(
                        corners=corners,
                        contour=contours,
                        frame=frame,
                        matte_flag=matte_flag,
                        nb_visible_corners=nb_visible_corners,
                    )

                    # occluded corner detection
                    if get_nb_visible_corners(corners) == 3:
                        first_occluded_corners = corners if first_occluded_corners is None else first_occluded_corners

                        corners = self.occluded_corners_detector.run_occluded_corner_detector(
                            screen_mask=screen_mask,
                            low_epsilon_contour=low_epsilon_contours,
                            previous_corners=first_occluded_corners,
                            corners=copy.deepcopy(corners),
                        )

                    # hidden edge defining
                    if get_nb_visible_corners(corners) == 4:
                        first_fully_corners = corners if first_fully_corners is None else first_fully_corners

                        corners, hidden_edge = find_hidden_edge(
                            previous_corners=first_fully_corners,
                            corners=copy.deepcopy(corners),
                            width=session.source.get_source_properties().frame_width,
                        )
                    else:
                        hidden_edge = None

                    # expansion calculate
                    fittest_corners, expansion_width, expansion_height = self.post_processor.extract_expansion(
                        contour=contours,
                        corners=corners.astype(np.float32),
                        hidden_edge=hidden_edge,
                        occluded_corner=occluded_corner,
                        width=session.source.get_source_properties().frame_width,
                        height=session.source.get_source_properties().frame_height,
                    )
                else:  # static screen
                    corners = session.source.get_corners_data(key - 1)
                    hidden_edge = session.source.get_hidden_edge_data(key - 1)
                    fittest_corners = session.source.get_fittest_corners_data(key - 1)
                    expansion_width = session.source.get_expansion_width_max()
                    expansion_height = session.source.get_expansion_height_max()

                session.source.set_corners_data(key, corners)
                session.source.set_hidden_edge_data(key, hidden_edge)
                session.source.set_fittest_corners_data(key, fittest_corners)
                session.source.set_expansion_width(key, expansion_width)
                session.source.set_expansion_height(key, expansion_height)

            else:
                timi_matte = np.zeros((height, width), dtype=np.uint8)
                session.source.set_expansion_width(key, 1.0)
                session.source.set_expansion_height(key, 1.0)

            session.source.set_timi_matte_data(timi_matte)
            session.source.set_contour_data(key, contours)
            session.source.set_low_epsilon_contour_data(key, low_epsilon_contours)

        session.source.release_source_reader()
        session.source.release_screen_mask_reader()
        session.source.release_timi_matte_writer()

    def run_corners_stabilizing(self, session: Session):
        """
        Stabilize and smooth corner positions across all frames.

        This method reduces jitter in corner positions throughout the video sequence
        to provide smooth screen tracking.
        It applies different smoothing algorithms like Savitzky-Golay filter and Butterworth filter
        to stabilize corner trajectories.
        Skip processing if the screen is detected as static.

        Args:
            session (Session): Session object containing corner data and processing state
        """
        if session.get_screen_orientation() == ScreenOrientation.STATIC:
            return

        list_corners = session.source.get_all_corners_array()
        list_hidden_edge = session.source.get_all_hidden_edges()
        new_list_corners = self.corner_smoother.run(
            list_corners=list_corners,
            list_hidden_edge=list_hidden_edge,
            width=session.source.get_source_properties().frame_width,
        )

        for key, corners in zip(session.source.keys, new_list_corners):
            session.source.set_corners_data(key, corners)

        logger.info("Finished stage 2: Stabilize corners")

    def run_corners_post_processing(self, session: Session):
        """
        Apply post-processing to detected corners.

        Expands corners based on maximum expansion width and height
        calculated during the corner detection phase.
        This ensures consistent corner positioning that properly accommodates the LED content for all frames.

        Args:
            session (Session): Session object containing corner data and expansion ratios
        """
        expansion_width_max = session.source.get_expansion_width_max()
        expansion_height_max = session.source.get_expansion_height_max()

        for key in tqdm(session.source.keys, desc="Post-processing corners: "):
            corners = session.source.get_corners_data(key)
            corners = self.post_processor.expand_corners(corners, (expansion_width_max, expansion_height_max))
            session.source.set_corners_data(key, corners)

        logger.info("Finished corners post-processing")

    def run_post_processing_and_blending(self, session: Session):
        """
        Post-process screen masks and blend LED content onto source frames.

        This method:
        1. Applies refinement to screen masks
        2. Uses corner positions to transform the LED content
        3. Blends the LED content with original frames using the refined masks
        4. Handles edge cases where screen detection may have failed

        The resulting composited frames are written to the intermediate output.

        Args:
            session (Session): Session object containing all necessary processing data
        """
        session.source.init_source_reader()
        session.source.init_timi_matte_reader()
        session.source.init_screen_mask_reader()
        session.led.init_reader()
        session.init_intermediate_writer()
        for key in tqdm(session.source.keys, desc="Post-processing screen mask: "):

            frame = session.source.get_frame_data()
            led = session.led.get_frame_data()
            screen_mask = session.source.get_screen_mask_data()
            timi_matte = session.source.get_timi_matte_data()

            fittest_corners = session.source.get_fittest_corners_data(key)
            corners = session.source.get_corners_data(key)
            screen_box = session.source.get_bounding_box_data(key)
            if (
                session.source.get_screen_exist_data(key)
                and not is_dummy_corners(fittest_corners)
                and not is_dummy_corners(corners)
            ):
                processed_screen_mask = self.post_processor.post_process_mask(
                    screen_mask=screen_mask,
                    frame=frame,
                    fittest_corners=fittest_corners,
                    timi_matte=timi_matte,
                    screen_box=screen_box[0],
                )
                output = self.blender.run(
                    frame=frame,
                    fittest_corners=fittest_corners,
                    corners=corners,
                    led=led,
                    screen_mask=processed_screen_mask,
                )
            else:
                output = frame
            session.set_intermediate_frame_data(output)

        session.source.release_source_reader()
        session.source.release_timi_matte_reader()
        session.source.release_screen_mask_reader()
        session.led.release_reader()
        session.release_intermediate_writer()
        logger.info("Finished mask post processing and blending")

    def run(self, session: Session):
        """
        Run the full Composite LED Inference pipeline.

        This is the main entry point for processing a video with LED compositing.
        The pipeline consists of:
        1. Checking if the screen is static
        2. Extracting source corners and screen masks
        3. Stabilizing and smoothing corners
        4. Post-processing corners with expansion
        5. Blending LED content with source frames
        6. Merging audio to produce the final output

        Args:
            session:
        """
        if session.get_exist_screen():
            logger.info("Start pipeline VSC")

            # Stage 1: extract source information (screen mask, timi matte, corners, occluded corner, hidden edge)
            self.extract_source_corners(session)

            # Release stage 1
            self.release_stage1()

            # Stage 2: smooth and stabilize corners
            self.run_corners_stabilizing(session)

            # Stage 3: post-process corners
            self.run_corners_post_processing(session)

            # Stage 3: post-processing masks and blending
            self.run_post_processing_and_blending(session)

            # release
            self.release(None)  # no release session because we need to use it in the merge step.

            logger.info("End pipeline")

        else:
            logger.info("No screen detected. Skip pipeline")

            # run without processing
            session.run_without_processing()

            # Release stage 1
            self.release_stage1()

            # release
            self.release(None)  # no release session because we need to use it in the merge step.

    def release_stage1(self):
        """
        Release resources used during stage 1.

        """

        self.matting_detector.release()
        self.corners_validator.release()

    def release(self, session: Optional[Session] = None):
        """
        Release resources used during processing.

        Properly releases memory and resources allocated by various components:
        - Matting detector
        - Corners validator
        - Blender
        - Session (including file handlers)

        This method should be called at the end of processing to avoid resource leaks.

        Args:
            session (Session): Session object to release
        """
        self.blender.release()
        if session is not None:
            session.release()
