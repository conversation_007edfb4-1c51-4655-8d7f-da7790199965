import numpy as np


OUTPUT_EXT = ".mp4"
TEXT_EXT = ".txt"
IMAGE_EXT = ["jpg", "png", "jpeg"]
VIDEO_EXT = ["mp4", "mov"]

DUMMY_CORNERS = np.ones((4, 2), dtype=np.int32) * -1  # using -1 for dummy corners

DUMMY_CORNER = np.ones((1, 2), dtype=np.int32) * -1  # using -1 for dummy corners

DISTANCE_BOUNDARY = 2

DEFAULT_READER_QUEUE = 10

DEFAULT_WRITER_QUEUE = 10

DEFAULT_BITRATE = "500k"

DEFAULT_FPS = 30
