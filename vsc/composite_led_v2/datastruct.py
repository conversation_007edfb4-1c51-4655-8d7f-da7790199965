from dataclasses import dataclass
from enum import Enum
from typing import Optional, Union

import numpy as np


class ScreenOrientation(Enum):
    STATIC = 0
    ZOOMED = 1
    MOVING = 2


class HiddenEdge(Enum):
    LEFT = 0
    RIGHT = 1
    TOP = 2
    BOTTOM = 3


class OccludedCorner(Enum):
    TL = 0
    BL = 1
    BR = 2
    TR = 3


occluded_corner_dict = {0: OccludedCorner.TL, 1: OccludedCorner.BL, 2: OccludedCorner.BR, 3: OccludedCorner.TR}


class LedType(Enum):
    IMAGE = 0
    VIDEO = 1


@dataclass
class ImageProperties:
    frame_width: int = None
    frame_height: int = None

    def __repr__(self):
        return f"Width: {self.frame_width}, Height: {self.frame_height}"


@dataclass
class VideoProperties:
    fps: float = 30  # Default video FPS
    frame_width: int = None
    frame_height: int = None
    bit_rate: str = "16000k"  # Default video bitrate
    total_frames: int = None

    def __repr__(self):
        return f"FPS = {self.fps}, Width: {self.frame_width}, Height: {self.frame_height}, Bitrate: {self.bit_rate}, Total frames: {self.total_frames}"


@dataclass
class LedProperties:
    type: LedType = None
    properties: Union[ImageProperties, VideoProperties] = None


class Metadata:
    def __init__(
        self,
        audio_path: str,
        led_audio_path: str,
        corners: Optional[np.ndarray] = None,
        fittest_corners: Optional[np.ndarray] = None,
        expansion_width_range: Optional[float] = None,
        expansion_height_range: Optional[float] = None,
    ):
        self.audio_path = audio_path
        self.led_audio_path = led_audio_path
        self.corners = corners
        self.fittest_corners = fittest_corners
        self.expansion_width_range = expansion_width_range
        self.expansion_height_range = expansion_height_range
