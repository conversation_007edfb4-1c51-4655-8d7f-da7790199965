import gc
import os
from typing import Dict, <PERSON>ple

import cuda
import cv2
import numpy as np
import tensorrt as trt
import torch
from tensorrt_bindings import Logger

from vsc.composite_led_v2.backbones.vc2 import common
from vsc.composite_led_v2.backbones.vc2.utils import inference
from vsc.composite_led_v2.backbones.vc2.utils_trt import load_engine
from vsc.composite_led_v2.utils.download_utils import download_with_hash_check


def preprocess_image(image, target_size=(512, 512)):
    img_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    img_rgb = cv2.resize(img_rgb, target_size, interpolation=cv2.INTER_LINEAR)
    img_rgb = np.transpose(img_rgb, (2, 0, 1))

    return img_rgb


class VC2:
    def __init__(self, checkpoints: Dict, weights_folder: str, device: str):
        self.__weights_folder = weights_folder
        self.__device = device
        self.__checkpoints = checkpoints

        # get path
        timi_detection_path, matte_detection_path = self._download()

        # init timi detection
        self.__modelx = torch.load(timi_detection_path, self.__device)["model"].float()
        self.__modelx.to(self.__device)
        self.__modelx.eval()

        # init matte detection
        try:
            self.gpu_id = int(self.__device.split(":")[-1])
        except ValueError:
            self.gpu_id = 0

        cuda.cudart.cudaSetDevice(self.gpu_id)
        self.logger = Logger(Logger.INFO)
        self.runtime = trt.Runtime(self.logger)
        self.engine = self.runtime.deserialize_cuda_engine(load_engine(matte_detection_path))
        self.context = self.engine.create_execution_context()
        self.inputs, self.outputs, self.bindings, self.stream = common.allocate_buffers(self.engine)

    def _download(self) -> Tuple[str, str]:
        os.makedirs(self.__weights_folder, exist_ok=True)

        timi_detection_path = download_with_hash_check(
            weight_url=self.__checkpoints["timi_detection"]["checkpoint_id"],
            weight_path=os.path.join(
                self.__weights_folder, os.path.basename(self.__checkpoints["timi_detection"]["checkpoint_id"])
            ),
            hash_code=self.__checkpoints["timi_detection"]["hash_code"],
        )

        matte_detection_path = download_with_hash_check(
            weight_url=self.__checkpoints["matte_detection"]["checkpoint_id"],
            weight_path=os.path.join(
                self.__weights_folder, os.path.basename(self.__checkpoints["matte_detection"]["checkpoint_id"])
            ),
            hash_code=self.__checkpoints["matte_detection"]["hash_code"],
        )

        return timi_detection_path, matte_detection_path

    def process_image(self, img):
        height, width, _ = img.shape

        x1, y1, x2, y2 = inference(self.__modelx, img.copy(), 640, self.__device)

        if x1 is None:
            zeros_mask = np.zeros((height, width), dtype=np.float32)
            mask_refine = zeros_mask

        else:
            x_exp = (x2 - x1) / 20
            y_exp = (y2 - y1) / 20
            x1_new = max(0, int(x1 - x_exp))
            y1_new = max(0, int(y1 - y_exp))
            x2_new = min(width, int(x2 + x_exp))
            y2_new = min(height, int(y2 + y_exp))

            cropped_image = img[y1_new:y2_new, x1_new:x2_new]
            input_tensor = preprocess_image(cropped_image)

            np.copyto(self.inputs[0].host, input_tensor.ravel())
            trt_outputs = common.do_inference(
                self.context, self.engine, self.bindings, self.inputs, self.outputs, self.stream, self.gpu_id
            )
            pred = trt_outputs[-1].reshape(1, 1, 512, 512)

            pred = cv2.resize(pred[0][0], (x2_new - x1_new, y2_new - y1_new), interpolation=cv2.INTER_LINEAR)
            mask = np.zeros((height, width), dtype=np.float32)
            mask[y1_new:y2_new, x1_new:x2_new] = pred
            mask_refine = mask

        return mask_refine

    def release(self):
        # Free CUDA resources
        if hasattr(self, 'stream'):
            del self.stream
        if hasattr(self, 'inputs'):
            del self.inputs
        if hasattr(self, 'outputs'):
            del self.outputs
        if hasattr(self, 'bindings'):
            del self.bindings

        # Release TensorRT resources
        if hasattr(self, 'context'):
            del self.context
        if hasattr(self, 'engine'):
            del self.engine
        if hasattr(self, 'runtime'):
            del self.runtime

        # Release PyTorch model
        if hasattr(self, '__modelx'):
            del self.__modelx

        torch.cuda.empty_cache()
        gc.collect()
