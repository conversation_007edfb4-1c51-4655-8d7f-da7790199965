import time

import cv2
import numpy as np
import torch


def wh2xy(x):
    assert x.shape[-1] == 4, f"expected 4 but input shape is {x.shape}"
    if isinstance(x, torch.Tensor):
        y = torch.empty_like(x, dtype=torch.float32)
    else:
        y = np.empty_like(x, dtype=np.float32)
    xy = x[..., :2]
    wh = x[..., 2:] / 2
    y[..., :2] = xy - wh
    y[..., 2:] = xy + wh
    return y


def non_max_suppression(pred, conf_th=0.001, iou_th=0.7):
    import torchvision

    max_det = 300
    max_wh = 7680
    max_nms = 30000

    pred = pred[0] if isinstance(pred, (list, tuple)) else pred

    bs = pred.shape[0]  # batch size
    nc = pred.shape[1] - 4  # number of classes
    xc = pred[:, 4 : (4 + nc)].amax(1) > conf_th

    start_time = time.time()
    time_limit = 2.0 + 0.05 * bs

    pred = pred.transpose(-1, -2)
    pred[..., :4] = wh2xy(pred[..., :4])

    output = [torch.zeros((0, 6), device=pred.device)] * bs
    for xi, x in enumerate(pred):
        x = x[xc[xi]]
        if not x.shape[0]:
            continue

        box, cls = x.split((4, nc), 1)

        if nc > 1:
            i, j = torch.where(cls > conf_th)
            x = torch.cat((box[i], x[i, 4 + j, None], j[:, None].float()), 1)
        else:  # best class only
            conf, j = cls.max(1, keepdim=True)
            x = torch.cat((box, conf, j.float()), 1)[conf.view(-1) > conf_th]

        n = x.shape[0]  # number of boxes
        if not n:
            continue
        if n > max_nms:
            x = x[x[:, 4].argsort(descending=True)[:max_nms]]

        c = x[:, 5:6] * max_wh  # classes
        boxes, scores = x[:, :4] + c, x[:, 4]

        idx = torchvision.ops.nms(boxes, scores, iou_th)  # NMS
        idx = idx[:max_det]

        output[xi] = x[idx]
        if (time.time() - start_time) > time_limit:
            print(f"WARNING ⚠️ NMS time limit {time_limit:.3f}s exceeded")
            break

    return output


def inference(model, frame, inp_size, device):
    image = frame.copy()
    shape = image.shape[:2]

    r = inp_size / max(shape[0], shape[1])
    if r != 1:
        resample = cv2.INTER_LINEAR if r > 1 else cv2.INTER_AREA
        image = cv2.resize(image, dsize=(int(shape[1] * r), int(shape[0] * r)), interpolation=resample)
    height, width = image.shape[:2]

    r = min(1.0, inp_size / height, inp_size / width)

    pad = int(round(width * r)), int(round(height * r))
    w = (inp_size - pad[0]) / 2
    h = (inp_size - pad[1]) / 2

    if (width, height) != pad:
        image = cv2.resize(image, pad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(h - 0.1)), int(round(h + 0.1))
    left, right = int(round(w - 0.1)), int(round(w + 0.1))
    image = cv2.copyMakeBorder(image, top, bottom, left, right, cv2.BORDER_CONSTANT)

    x = image.transpose((2, 0, 1))[::-1]
    x = np.ascontiguousarray(x)
    x = torch.from_numpy(x)
    x = x.unsqueeze(dim=0)
    x = x.to(device)
    x = x / 255

    outputs = model(x)
    outputs = non_max_suppression(outputs, 0.15, 0.2)[0]

    if len(outputs) > 0:
        outputs[:, [0, 2]] -= w
        outputs[:, [1, 3]] -= h
        outputs[:, :4] /= min(height / shape[0], width / shape[1])

        outputs[:, 0].clamp_(0, shape[1])
        outputs[:, 1].clamp_(0, shape[0])
        outputs[:, 2].clamp_(0, shape[1])
        outputs[:, 3].clamp_(0, shape[0])

        for box in outputs:
            box = box.cpu().numpy()
            x1, y1, x2, y2, score, index = box
    else:
        # x1, y1, x2, y2 = 0, 0, width, height
        x1, y1, x2, y2 = None, None, None, None

    return x1, y1, x2, y2
