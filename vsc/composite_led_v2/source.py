import os
from typing import Dict, <PERSON>, Optional, <PERSON><PERSON>

import numpy as np
from fvutils.media import (
    get_video_fps,
    get_video_properties,
    has_audio,
    split_audio_from_video,
)

from vsc.composite_led_v2.constants import DUMMY_CORNERS
from vsc.composite_led_v2.datastruct import (
    HiddenEdge,
    Metadata,
    OccludedCorner,
    VideoProperties,
)
from vsc.composite_led_v2.utils.corner_utils import get_nb_visible_corners
from vsc.composite_led_v2.video_player import MaskVideoPlayer, VideoPlayer
from vsc.utils.logger import logger


class Corners:
    """A class to manage four corner points that can be either visible or dummy.

    The Corners class stores corner data as a numpy array and tracks visibility status.
    """

    def __init__(self):
        """Initialize Corners with dummy corner values."""
        self.__corners: np.ndarray = DUMMY_CORNERS

    def set_corners_data(self, corners: np.ndarray) -> None:
        """Set the corner data if the input array has valid shape.

        Args:
            corners: A numpy array containing 4 corner points.
        """
        if corners.shape[0] != 4:
            logger.warning("Corners data has wrong number of corners")
            return

        self.__corners = corners

    def get_corners_data(self) -> np.ndarray:
        """Get the stored corner data.

        Returns:
            A numpy array containing the corner points.
        """
        return self.__corners

    def get_nb_visible_corners(self) -> int:
        """Get the number of visible (non-dummy) corners.

        Returns:
            The count of visible corners.
        """
        return get_nb_visible_corners(self.__corners)


class Source:
    """Manages video source data including metadata, properties, frames and other attributes.

    This class handles video source operations including reading/writing frames,
    managing video properties, corners, contours and other video-related data.
    """

    def __init__(
        self, video_path, data_dir, source_properties: Optional[VideoProperties], metadata: Optional[Metadata] = None
    ):
        """Initialize a Source instance.

        Args:
            video_path: Path to the source video file
            data_dir: Directory for storing generated data
            source_properties: Video properties if available, extracted from video otherwise
            metadata: Optional metadata for the video
        """
        self.__video_path = video_path
        self.__data_dir = data_dir

        self.__audio_path = os.path.join(self.__data_dir, "audio.wav")
        self.__timi_matte_path = os.path.join(self.__data_dir, "timi_matte.avi")
        self.__screen_mask_path = os.path.join(self.__data_dir, "screen_mask.avi")
        self.__processed_screen_mask_path = os.path.join(self.__data_dir, "processed_screen_mask.avi")

        # metadata
        self.__metadata: Optional[Metadata] = metadata

        # extract source properties
        self.__source_properties: VideoProperties = (
            source_properties if source_properties is not None else self.__extract_source_properties()
        )

        # get a key list
        self.keys = [i for i in range(self.get_source_properties().total_frames)]

        # screen bounding box
        self.__screen_bboxes: Dict[int, Optional[List[np.ndarray]]] = {}

        # contour
        self.__contour: Dict[int, Optional[np.ndarray]] = {}
        self.__low_epsilon_contour: Dict[int, Optional[np.ndarray]] = {}

        # have screen
        self.__screen_exist: Dict[int, bool] = {}

        # corners
        self.__corners: Dict[int, Corners] = self.__get_dummy_corners_dict()

        # hidden-edge names
        self.__hidden_edge_names: Dict[int, Optional[HiddenEdge]] = self.__get_dummy_hidden_edge_dict()

        # occluded corners
        self.__occluded_corner_dict: Dict[int, Optional[OccludedCorner]] = self.__get_dummy_occluded_corner_dict()

        # fitted corners
        self.__fitted_corners: Dict[int, Corners] = self.__get_dummy_corners_dict()

        # expansion distance width - expand distance height
        self.__expand_distance_width: Dict[int, float] = {}
        self.__expand_distance_height: Dict[int, float] = {}

        # frame capturer
        self.__source_video_player: VideoPlayer = VideoPlayer(
            video_path=self.__video_path,
            fps=self.get_source_properties().fps,
            size=(self.get_source_properties().frame_width, self.get_source_properties().frame_height),
            bitrate=self.get_source_properties().bit_rate,
        )

        # timi matte player
        self.__timi_matte_player: MaskVideoPlayer = MaskVideoPlayer(
            video_path=self.__timi_matte_path,
            fps=self.get_source_properties().fps,
            size=(self.get_source_properties().frame_width, self.get_source_properties().frame_height),
            dtype=np.float32,
        )

        # screen mask player
        self.__screen_mask_player: MaskVideoPlayer = MaskVideoPlayer(
            video_path=self.__screen_mask_path,
            fps=self.get_source_properties().fps,
            size=(self.get_source_properties().frame_width, self.get_source_properties().frame_height),
            dtype=np.uint8,
        )

    def get_metadata(self) -> Metadata:
        """Get the video metadata.

        Returns:
            The video metadata object
        """
        return self.__metadata

    def set_metadata(self, metadata: Metadata) -> None:
        """Set the video metadata.

        Args:
            metadata: Metadata object to set
        """
        self.__metadata = metadata

    def get_audio_path(self) -> str:
        """Get the path to the extracted audio file.

        Returns:
            Path to the audio file
        """
        if has_audio(self.__video_path):
            split_audio_from_video(self.__video_path, self.__audio_path)
        return self.__audio_path

    def __extract_source_properties(self) -> VideoProperties:
        """Extract video properties from the source file.

        Returns:
            VideoProperties object containing extracted properties
        """
        source_properties = get_video_properties(self.__video_path)
        fps = get_video_fps(self.__video_path)

        logger.info(
            f"Source properties: "
            f"FPS = {fps}, "
            f"Width = {source_properties['width']}, "
            f"Height = {source_properties['height']}, "
            f"Bitrate = {source_properties['bit_rate']}, "
            f"Total frames = {source_properties['nb_frames']}"
        )

        return VideoProperties(
            fps=fps,
            frame_width=source_properties['width'],
            frame_height=source_properties['height'],
            bit_rate=f"{int(int(source_properties['bit_rate']) / 1000)}k",
            total_frames=int(source_properties['nb_frames']),
        )

    def get_source_properties(self) -> VideoProperties:
        """Get the video source properties.

        Returns:
            VideoProperties object containing the properties
        """
        if self.__source_properties is None:
            self.__source_properties = self.__extract_source_properties()
        return self.__source_properties

    def __get_dummy_corners_dict(self) -> Dict[int, Corners]:
        """Create a dictionary of dummy corner objects for each frame.

        Returns:
            Dictionary mapping frame indices to Corners objects
        """
        corners_dict = {}
        for key in range(self.get_source_properties().total_frames):
            corners_dict[key] = Corners()

        return corners_dict

    def __get_dummy_hidden_edge_dict(self) -> Dict[int, Optional[HiddenEdge]]:
        """Create a dictionary of dummy hidden-edge values for each frame.

        Returns:
            Dictionary mapping frame indices to HiddenEdge values
        """
        hidden_edge_dict = {}
        for key in range(self.get_source_properties().total_frames):
            hidden_edge_dict[key] = None

        return hidden_edge_dict

    def __get_dummy_occluded_corner_dict(self) -> Dict[int, Optional[OccludedCorner]]:
        """Create a dictionary of dummy occluded corner values for each frame.

        Returns:
            Dictionary mapping frame indices to OccludedCorner values
        """
        occluded_corner_dict = {}
        for key in range(self.get_source_properties().total_frames):
            occluded_corner_dict[key] = None

        return occluded_corner_dict

    # ---------------------------- SOURCE FRAME ----------------------------
    def init_source_writer(self) -> None:
        """Initialize the source video writer."""
        self.__source_video_player.init_writer()

    def set_frame_data(self, frame_data: np.ndarray) -> None:
        """Set frame data for the video source.

        Args:
            frame_data: NumPy array containing the frame data
        """
        self.__source_video_player.write(frame_data)

    def release_source_writer(self) -> None:
        """Release the source video writer."""
        self.__source_video_player.release_writer()

    def init_source_reader(self) -> None:
        """Initialize the source video reader."""
        self.__source_video_player.init_reader()

    def get_frame_data(self) -> np.ndarray:
        """Get frame data from the video source.

        Returns:
            NumPy array containing the frame data
        """
        return self.__source_video_player.read()

    def release_source_reader(self):
        """Release the source video reader."""
        self.__source_video_player.release_reader()

    # ---------------------------- SOURCE TIMI MATTE ----------------------------
    def init_timi_matte_writer(self):
        """Initialize the TIMI matte writer."""
        self.__timi_matte_player.init_writer()

    def set_timi_matte_data(self, timi_matte: np.ndarray) -> None:
        """Set TIMI matte data.

        Args:
            timi_matte: NumPy array containing the TIMI matte data
        """
        self.__timi_matte_player.write(timi_matte)

    def release_timi_matte_writer(self) -> None:
        """Release the TIMI matte writer."""
        self.__timi_matte_player.release_writer()

    def init_timi_matte_reader(self) -> None:
        """Initialize the TIMI matte reader."""
        self.__timi_matte_player.init_reader()

    def get_timi_matte_data(self) -> np.ndarray:
        """Get TIMI matte data.

        Returns:
            NumPy array containing the TIMI matte data
        """
        return self.__timi_matte_player.read()

    def release_timi_matte_reader(self):
        """Release the TIMI matte reader."""
        self.__timi_matte_player.release_reader()

    # ---------------------------- SOURCE SCREEN MASK ----------------------------
    def init_screen_mask_writer(self):
        """Initialize the screen mask writer."""
        self.__screen_mask_player.init_writer()

    def set_screen_mask_data(self, screen_mask: np.ndarray) -> None:
        """Set screen mask data.

        Args:
            screen_mask: NumPy array containing the screen mask data
        """
        self.__screen_mask_player.write(screen_mask)

    def release_screen_mask_writer(self) -> None:
        """Release the screen mask writer."""
        self.__screen_mask_player.release_writer()

    def init_screen_mask_reader(self) -> None:
        """Initialize the screen mask reader."""
        self.__screen_mask_player.init_reader()

    def get_screen_mask_data(self) -> np.ndarray:
        """Get screen mask data.

        Returns:
            NumPy array containing the screen mask data
        """
        return self.__screen_mask_player.read()

    def release_screen_mask_reader(self):
        """Release the screen mask reader."""
        self.__screen_mask_player.release_reader()

    # ---------------------------- SOURCE SCREEN BOUNDING BOX ----------------------------
    def get_bounding_box_data(self, key: int) -> Optional[np.ndarray]:
        """Get bounding box data for a specific frame.

        Args:
            key: Frame index

        Returns:
            NumPy array containing the bounding box data
        """
        return self.__screen_bboxes[key]

    def set_bounding_box_data(self, key: int, bounding_boxes: List[Optional[np.ndarray]]) -> None:
        """Set bounding box data for a specific frame.

        Args:
            key: Frame index
            bounding_boxes: List of NumPy arrays containing bounding box data
        """
        self.__screen_bboxes[key] = bounding_boxes

    def list_bounding_box_data(self, ins: bool = True) -> Tuple[np.ndarray, int]:
        """List all bounding box data.

        Args:
            ins (bool, optional): Whether to include bounding boxes for frames without screens.
                                Defaults to True.

        Returns:
            Tuple[np.ndarray, int]:
            Array containing bounding box coordinates across frames and total number of frames
        """

        return np.stack(
            [
                self.__screen_bboxes[key][0]
                for key in self.__screen_bboxes.keys()
                if ins
                or self.__screen_bboxes[key][0][0] != 0
                or self.__screen_bboxes[key][0][1] != 0
                or self.__screen_bboxes[key][0][2] != self.get_source_properties().frame_width
                or (self.__screen_bboxes[key][0][3] != self.get_source_properties().frame_height)
            ],
            axis=0,
        ), len(self.__screen_bboxes.keys())

    # ---------------------------- SOURCE CONTOUR ----------------------------
    def get_contour_data(self, key: int) -> Optional[np.ndarray]:
        """Get contour data for a specific frame.

        Args:
            key: Frame index

        Returns:
            NumPy array containing the contour data
        """
        return self.__contour[key]

    def set_contour_data(self, key: int, contour: Optional[np.ndarray]) -> None:
        """Set contour data for a specific frame.

        Args:
            key: Frame index
            contour: NumPy array containing contour data
        """
        self.__contour[key] = contour

    def get_all_contour_data(self) -> List[np.ndarray]:
        """Get contour data for all frames.

        Returns:
            List of NumPy arrays containing contour data for each frame
        """
        arrays = []
        for key, contour in self.__contour.items():
            arrays.append(contour)

        return arrays

    def get_low_epsilon_contour_data(self, key: int) -> Optional[np.ndarray]:
        """Get low epsilon contour data for a specific frame.

        Args:
            key: Frame index

        Returns:
            NumPy array containing the low-epsilon contour data
        """
        return self.__low_epsilon_contour[key]

    def set_low_epsilon_contour_data(self, key: int, low_epsilon: Optional[np.ndarray]) -> None:
        """Set low epsilon contour data for a specific frame.

        Args:
            key: Frame index
            low_epsilon: NumPy array containing low-epsilon contour data
        """
        self.__low_epsilon_contour[key] = low_epsilon

    # ---------------------------- SOURCE SCREEN EXIST STATUS ----------------------------
    def get_screen_exist_data(self, key: int) -> bool:
        """Get screen existence status for a specific frame.

        Args:
            key: Frame index

        Returns:
            Boolean indicating if the screen exists in the frame
        """
        return self.__screen_exist[key]

    def set_screen_exist_data(self, key: int, screen_exist: bool) -> None:
        """Set the screen existence status for a specific frame.

        Args:
            key: Frame index
            screen_exist: Boolean indicating if a screen exists
        """
        self.__screen_exist[key] = screen_exist

    # ---------------------------- SOURCE CORNERS ----------------------------
    def get_corners_data(self, key: int) -> np.ndarray:
        """Get corner data for a specific frame.

        Args:
            key: Frame index

        Returns:
            NumPy array containing corner coordinates
        """
        return self.__corners[key].get_corners_data()

    def set_corners_data(self, key: int, corners: np.ndarray) -> None:
        """Set corner data for a specific frame.

        Args:
            key: Frame index
            corners: NumPy array containing corner coordinates
        """
        self.__corners[key].set_corners_data(corners)

    def get_nb_visible_corners(self, key: int) -> int:
        """Get number of visible corners for a specific frame.

        Args:
            key: Frame index

        Returns:
            Number of visible corners
        """
        return self.__corners[key].get_nb_visible_corners()

    def get_occluded_corner_data(self, key: int) -> Optional[OccludedCorner]:
        """Get occluded corner data for a specific frame.

        Args:
            key: Frame index

        Returns:
            OccludedCorner object if exists, None otherwise
        """
        return self.__occluded_corner_dict[key]

    def set_occluded_corner_data(self, key: int, occluded_corner: Optional[OccludedCorner]) -> None:
        """Set occluded corner data for a specific frame.

        Args:
            key: Frame index
            occluded_corner: OccludedCorner object or None
        """
        self.__occluded_corner_dict[key] = occluded_corner

    def get_fittest_corners_data(self, key: int) -> np.ndarray:
        """Get fitted corner data for a specific frame.

        Args:
            key: Frame index

        Returns:
            NumPy array containing fitted corner coordinates
        """
        return self.__fitted_corners[key].get_corners_data()

    def set_fittest_corners_data(self, key: int, corners: np.ndarray) -> None:
        """Set fitted corner data for a specific frame.

        Args:
            key: Frame index
            corners: NumPy array containing fitted corner coordinates
        """
        self.__fitted_corners[key].set_corners_data(corners)

    def get_all_corners_array(self) -> np.ndarray:
        """Get corner data for all frames.

        Returns:
            NumPy array containing corner coordinates for all frames
        """
        arrays = []
        for key, corners in self.__corners.items():
            arrays.append(corners.get_corners_data())

        return np.array(arrays)

    def get_all_occluded_corner(self) -> List[Optional[OccludedCorner]]:
        """Get occluded corner data for all frames.

        Returns:
            List of OccludedCorner objects for all frames
        """
        arrays = []
        for key, occluded_corner in self.__occluded_corner_dict.items():
            arrays.append(occluded_corner)

        return arrays

    # ---------------------------- SOURCE HIDDEN EDGE ----------------------------
    def get_hidden_edge_data(self, key: int) -> Optional[HiddenEdge]:
        """Get hidden edge data for a specific frame.

        Args:
            key: Frame index

        Returns:
            HiddenEdge object if exists, None otherwise
        """
        return self.__hidden_edge_names[key]

    def set_hidden_edge_data(self, key: int, hidden_edge: Optional[HiddenEdge]) -> None:
        """Set hidden-edge data for a specific frame.

        Args:
            key: Frame index
            hidden_edge: HiddenEdge object or None
        """
        self.__hidden_edge_names[key] = hidden_edge

    def get_all_hidden_edges(self) -> List[Optional[HiddenEdge]]:
        """Get hidden-edge data for all frames.

        Returns:
            List of HiddenEdge objects for all frames
        """
        arrays = []
        for key, hidden_edge in self.__hidden_edge_names.items():
            arrays.append(hidden_edge)

        return arrays

    # ---------------------------- SOURCE EXPANSION SIZE ----------------------------
    def set_expansion_width(self, key: int, width: float) -> None:
        """Set expansion width for a specific frame.

        Args:
            key: Frame index
            width: Expansion width value
        """
        self.__expand_distance_width[key] = width

    def get_expansion_width_max(self) -> float:
        """Get maximum expansion width across all frames.

        Returns:
            Maximum expansion width value
        """
        return max(self.__expand_distance_width.values())

    def set_expansion_height(self, key: int, height: float) -> None:
        """Set expansion height for a specific frame.

        Args:
            key: Frame index
            height: Expansion height value
        """
        self.__expand_distance_height[key] = height

    def get_expansion_height_max(self) -> float:
        """Get maximum expansion height across all frames.

        Returns:
            Maximum expansion height value
        """
        return max(self.__expand_distance_height.values())
