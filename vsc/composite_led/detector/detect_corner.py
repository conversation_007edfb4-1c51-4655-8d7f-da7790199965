import os.path

import cv2
import numpy as np
import onnxruntime as ort
from fvutils.minio import download

from vsc.composite_led.functions.helpers import parse_device
from vsc.utils.config import cfg
from vsc.utils.exception import ONNXRuntimeError
from vsc.utils.package_helpers import create_temp_package_path


class DetectCorners:
    def __init__(
        self,
        checkpoint_path: str = cfg.led.detector.checkpoint_path,
        checkpoint_id: str = cfg.led.detector.checkpoint_id,
        size: int = cfg.led.detector.size,
        iou_threshold: float = cfg.led.detector.iou_threshold,
        conf: float = cfg.led.detector.conf,
        classes: list = cfg.led.detector.classes,
        device: str = "cuda:0",
    ):
        checkpoint_path = create_temp_package_path(checkpoint_path)
        if not os.path.exists(checkpoint_path):
            checkpoint_path = download(checkpoint_id, os.path.dirname(checkpoint_path))

        self.device_type, self.device_id = parse_device(device)
        provider = (
            [("CUDAExecutionProvider", {"device_id": self.device_id})]
            if self.device_type == "cuda"
            else ["CPUExecutionProvider"]
        )

        # Init the model
        try:
            self.model = ort.InferenceSession(checkpoint_path, providers=provider)
        except RuntimeError as e:
            raise ONNXRuntimeError(f"Failed to load ONNX model on {device} \nError: {str(e)}") from e

        self.iou_threshold = iou_threshold
        self.conf = conf
        self.classes = classes
        self.size = size

    def run(self, image: np.ndarray) -> np.ndarray:
        """Function receives an image,
            passes it through YOLOv8 neural network
            and returns an array of detected objects
            and their bounding boxes

        Args:
            image: input image

        Returns:
            Array of bounding boxes in format [[x1,y1,x2,y2,object_type,probability],..]

        """
        input, img_width, img_height = self.prepare_input(image)
        output = self.inference(input)
        return self.process_output(output, img_width, img_height)

    def prepare_input(self, img: np.ndarray) -> tuple:
        """Function used to convert input image to tensor,
            required as an input to YOLOv8 object detection network.
        :return:

        Args:
            img: input image

        Returns: Numpy array in a shape (3,width,height) where 3 is number of color channels

        """
        img_height, img_width = img.shape[:2]
        img = cv2.resize(img, (self.size, self.size))
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB) / 255.0
        img = np.transpose(img, (2, 0, 1))
        img = img.reshape(1, 3, self.size, self.size)
        return img.astype(np.float32), img_width, img_height

    def inference(self, input: np.ndarray) -> list:
        """Function used to pass provided input tensor to
            YOLOv8 neural network and return result

        Args:
            input: Numpy array in a shape (3,width,height)

        """

        outputs = self.model.run(["output0"], {"images": input})
        return outputs[0]

    def process_output(self, output: list, img_width: int, img_height: int) -> np.ndarray:
        """Function used to convert RAW output from YOLOv8 to an array
            of detected objects. Each object contain the bounding box of
            this object, the type of object and the probability

        Args:
            output: Raw output of YOLOv8 network which is an array of shape (1,84,8400)
            img_width: The width of original image
            img_height: The height of original image

        """
        output = output[0].astype(float).transpose()

        boxes = []
        for row in output:
            conf = row[4:].max()
            if conf < self.conf:
                continue
            class_id = row[4:].argmax()
            label = self.classes[int(class_id)]
            xc, yc, w, h = row[:4]
            x1 = int((xc - w / 2) / self.size * img_width)
            y1 = int((yc - h / 2) / self.size * img_height)
            x2 = int((xc + w / 2) / self.size * img_width)
            y2 = int((yc + h / 2) / self.size * img_height)
            boxes.append([x1, y1, x2, y2, label, conf])

        boxes.sort(key=lambda x: x[5], reverse=True)
        result = []
        while len(boxes) > 0:
            result.append(boxes[0])
            boxes = [box for box in boxes if self.iou(box, boxes[0]) < self.iou_threshold]

        return np.array(result)

    def iou(self, box1: list, box2: list) -> float:
        """Function calculates "Intersection-over-union" coefficient for specified two boxes
            https://pyimagesearch.com/2016/11/07/intersection-over-union-iou-for-object-detection/.

        Args:
            box1: First box in format: [x1,y1,x2,y2,object_class,probability]
            box2: Second box in format: [x1,y1,x2,y2,object_class,probability]

        Returns:
            Intersection over union ratio as a float number

        """
        return self.intersection(box1, box2) / self.union(box1, box2)

    def union(self, box1: list, box2: list) -> float:
        """Function calculates union area of two boxes

        Args:
            box1: First box in format [x1,y1,x2,y2,object_class,probability]
            box2: Second box in format [x1,y1,x2,y2,object_class,probability]

        """
        box1_x1, box1_y1, box1_x2, box1_y2 = box1[:4]
        box2_x1, box2_y1, box2_x2, box2_y2 = box2[:4]
        box1_area = (box1_x2 - box1_x1) * (box1_y2 - box1_y1)
        box2_area = (box2_x2 - box2_x1) * (box2_y2 - box2_y1)
        return box1_area + box2_area - self.intersection(box1, box2)

    def intersection(self, box1: list, box2: list) -> float:
        """Function calculates intersection area of two boxes

        Args:
            box1: First box in format [x1,y1,x2,y2,object_class,probability]
            box2: Area of intersection of the boxes as a float number

        """
        box1_x1, box1_y1, box1_x2, box1_y2 = box1[:4]
        box2_x1, box2_y1, box2_x2, box2_y2 = box2[:4]
        x1 = max(box1_x1, box2_x1)
        y1 = max(box1_y1, box2_y1)
        x2 = min(box1_x2, box2_x2)
        y2 = min(box1_y2, box2_y2)
        return (x2 - x1) * (y2 - y1)


if __name__ == "__main__":
    model = DetectCorners()
    image = cv2.imread("data/led/timi_center.png")
    output = model.run(image)
    print(output)
    for out in output:
        cv2.rectangle(
            image,
            (int(out[0]), int(out[1])),
            (int(out[2]), int(out[3])),
            thickness=4,
            color=(255, 12, 34),
        )
    cv2.imwrite("output.png", image)
