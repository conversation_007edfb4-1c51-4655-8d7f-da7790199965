# Ref to: https://github.com/ibaiGorordo/ONNX-YOLOv8-Object-Detection/blob/main/yolov8/YOLOv8.py
import os
import time

import cv2
import numpy as np
import onnxruntime
from codetiming import Timer
from fvutils.minio import download

from vsc.composite_led.detector.yolo_utils import (
    draw_detections,
    multiclass_nms,
    xywh2xyxy,
)
from vsc.composite_led.functions.helpers import parse_device
from vsc.composite_led.functions.mask import MaskScreen
from vsc.utils.config import cfg
from vsc.utils.exception import ONNXRuntimeError
from vsc.utils.logger import logger, logger_perf
from vsc.utils.package_helpers import create_temp_package_path


class YOLOv8ONNX:
    def __init__(
        self,
        checkpoint_path: str = cfg.led.detector.checkpoint_path,
        checkpoint_id: str = cfg.led.detector.checkpoint_id,
        iou_threshold: float = cfg.led.detector.iou_threshold,
        conf_threshold: float = cfg.led.detector.conf,
        classes: list = cfg.led.detector.classes,
        device: str = "cuda:0",
    ):
        self.conf_threshold = conf_threshold
        self.iou_threshold = iou_threshold
        self.classes = list(classes)

        # Download checkpoint if it not exist
        checkpoint_path = create_temp_package_path(checkpoint_path)
        if not os.path.exists(checkpoint_path):
            checkpoint_path = download(checkpoint_id, os.path.dirname(checkpoint_path))

        self.device = device
        self.device_type, self.device_id = parse_device(device)
        # Initialize model
        self.initialize_model(checkpoint_path)

    @Timer("YOLOV8ONNX.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, image):
        try:
            return self.detect_objects(image)
        except AssertionError as e:
            logger.warning(f"AssertionError: {e}")
            return
        except ValueError as e:
            logger.warning(f"Error when inference corners model with message: {e}")
            return

    def initialize_model(self, path):
        provider = (
            [("CUDAExecutionProvider", {"device_id": self.device_id})]
            if self.device_type == "cuda"
            else ["CPUExecutionProvider"]
        )

        # Init the model
        try:
            self.session = onnxruntime.InferenceSession(path, providers=provider)
        except RuntimeError as e:
            raise ONNXRuntimeError(f"Failed to load ONNX model on {self.device} \nError: {str(e)}") from e

        # Get model info
        self.get_input_details()
        self.get_output_details()

    def detect_objects(self, image):
        input_tensor = self.prepare_input(image)

        # Perform inference on the image
        outputs = self.inference(input_tensor)

        self.boxes, self.scores, self.class_ids = self.process_output(outputs)

        if len(self.boxes) == 0:
            logger.warning("No corners were detected from the frame 🙈")
            return

        # Concatenate result
        return np.hstack(
            (
                self.boxes,
                np.expand_dims(self.class_ids, axis=1),
                np.expand_dims(self.scores, axis=1),
            )
        )

    def resize_with_padding(self, image: np.ndarray):
        shape = image.shape[:2]
        r = min(self.input_height / shape[0], self.input_width / shape[1])
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = self.input_width - new_unpad[0], self.input_height - new_unpad[1]
        dw /= 2
        dh /= 2
        if shape[::-1] != new_unpad:
            image = cv2.resize(image, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        image = cv2.copyMakeBorder(image, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))  # add border
        return image

    def prepare_input(self, image: np.ndarray):
        self.img_height, self.img_width = image.shape[:2]
        assert all(
            [self.img_height, self.img_width]
        ), f"Image height and width must be greater than 0. Received image shape: {image.shape}"
        input_img = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        resized = self.resize_with_padding(input_img)
        resized = resized.transpose(2, 0, 1)[None, ...]
        resized = resized.astype("float32") / 255.0
        return resized

    def inference(self, input_tensor, show_execution_time=False):
        start = time.perf_counter()
        outputs = self.session.run(self.output_names, {self.input_names[0]: input_tensor})
        if show_execution_time:
            print(f"Inference time: {(time.perf_counter() - start)*1000:.2f} ms")
        return outputs

    def process_output(self, output):
        predictions = np.squeeze(output[0]).T

        # Filter out object confidence scores below threshold
        scores = np.max(predictions[:, 4:], axis=1)
        predictions = predictions[scores > self.conf_threshold, :]
        scores = scores[scores > self.conf_threshold]

        if len(scores) == 0:
            return [], [], []

        # Get the class with the highest confidence
        class_ids = np.argmax(predictions[:, 4:], axis=1)

        # Get bounding boxes for each object
        boxes = self.extract_boxes(predictions)

        # Apply non-maxima suppression to suppress weak, overlapping bounding boxes
        # indices = nms(boxes, scores, self.iou_threshold)
        indices = multiclass_nms(boxes, scores, class_ids, self.iou_threshold)

        return boxes[indices], scores[indices], class_ids[indices]

    def extract_boxes(self, predictions):
        # Extract boxes from predictions
        boxes = predictions[:, :4]

        # Convert boxes to xyxy format
        boxes = xywh2xyxy(boxes)

        # Scale boxes to the original input image dimension
        boxes = self.scale_boxes((self.input_height, self.input_width), boxes, (self.img_height, self.img_width))

        return boxes

    def scale_boxes(self, img1_shape, boxes, img0_shape, ratio_pad=None, padding=True, xywh=False):

        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (
            round((img1_shape[1] - img0_shape[1] * gain) / 2 - 0.1),
            round((img1_shape[0] - img0_shape[0] * gain) / 2 - 0.1),
        )  # wh padding
        if padding:
            boxes[..., 0] -= pad[0]  # x padding
            boxes[..., 1] -= pad[1]  # y padding
            if not xywh:
                boxes[..., 2] -= pad[0]  # x padding
                boxes[..., 3] -= pad[1]  # y padding
        boxes[..., :4] /= gain
        return self.clip_boxes(boxes, img0_shape)

    def clip_boxes(self, boxes, shape):
        boxes[..., [0, 2]] = boxes[..., [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[..., [1, 3]] = boxes[..., [1, 3]].clip(0, shape[0])  # y1, y2
        return boxes

    def draw_detections(self, image, draw_scores=True, mask_alpha=0.4):
        return draw_detections(image, self.boxes, self.scores, self.class_ids, self.classes, mask_alpha)

    def get_input_details(self):
        model_inputs = self.session.get_inputs()
        self.input_names = [model_inputs[i].name for i in range(len(model_inputs))]

        self.input_shape = model_inputs[0].shape
        self.input_height = self.input_shape[2]
        self.input_width = self.input_shape[3]

    def get_output_details(self):
        model_outputs = self.session.get_outputs()
        self.output_names = [model_outputs[i].name for i in range(len(model_outputs))]


if __name__ == "__main__":
    # For testing
    yolov8_detector = YOLOv8ONNX()
    img_url = "data/led/bugs/issue_91_frame_60.png"
    img = cv2.imread(img_url)
    img_copy = img.copy()

    # Get largest mask screen
    mask = MaskScreen.get_mask_based_on_hsv(img)

    contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    area_list = [cv2.contourArea(contour) for contour in contours]
    largest_mask = np.zeros_like(mask)
    if len(area_list) > 0:
        largest_area_index = area_list.index(max(area_list))
        cv2.drawContours(largest_mask, [contours[largest_area_index]], -1, 255, thickness=cv2.FILLED)

    x1, y1, x2, y2 = MaskScreen.extract_region(largest_mask, 0)
    x1, y1, x2, y2 = MaskScreen.extract_region(largest_mask, int(0.2 * (x2 - x1)))

    cv2.imwrite("largest_mask.png", largest_mask)
    cv2.imwrite("mask.png", mask)
    img = img[y1:y2, x1:x2]
    # Detect Objects
    output = yolov8_detector.run(img)

    from vsc.composite_led.functions.coordinates import Coordinates

    output[:, 0] += x1
    output[:, 1] += y1
    output[:, 2] += x1
    output[:, 3] += y1

    bb = output[:, :4]
    class_idx = output[:, 4]
    confidence_score = output[:, 5]
    indicate = multiclass_nms(bb, class_idx, confidence_score, 0.1)

    bb_mns = bb[indicate]
    class_idx_nms = class_idx[indicate]
    confidence_score_nms = confidence_score[indicate]

    logger.info(f"output_nms: {bb_mns}, class_idx: {class_idx_nms}, confidence_score_nms: {confidence_score_nms}")

    o = np.concatenate((bb_mns, class_idx_nms[:, np.newaxis], confidence_score_nms[:, np.newaxis]), axis=1)
    logger.info(f"confidence: {o}")

    # Draw detections
    combined_img = yolov8_detector.draw_detections(img_copy)
    cv2.imwrite("output.png", combined_img)
