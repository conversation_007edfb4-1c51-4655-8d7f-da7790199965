import os
import traceback
import warnings

import numpy as np
from codetiming import Timer

from vsc.composite_led.functions.helpers import init_model_with_lock
from vsc.composite_led.segmentor.rvm import RVM
from vsc.utils.exception import handle_runtime_error


try:
    from vc2_matting import VC2
except ImportError:
    warnings.warn(f"Import VC2 failed. Exec to docker container to use VC2. Error message: {traceback.format_exc()}")

import gc
import shutil

from vsc.composite_led.base import BaseProcessor
from vsc.utils import cfg
from vsc.utils.logger import logger, logger_perf


def get_matte_from_npy(input_path: str) -> np.ndarray:
    '''
    Load a matte array from a NumPy (.npy) file.

    This function reads a NumPy array from the specified file path and returns it.
    The input NumPy array must be of type `float` and have values within the range [0., 1.].

    Args:
        input_path (str): The path to the .npy file containing the matte.

    Returns:
        np.ndarray: The matte array loaded from the .npy file.
    '''
    assert os.path.exists(input_path), f"path_matte_foreground '{input_path}' does not exist"
    matte = np.load(input_path)
    return matte


class ModelMatting(BaseProcessor):
    @Timer("ModelMatting.__init__", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @handle_runtime_error
    def __init__(self, model: str = cfg.led.model_matting, dir_save: str = "checkpoints/", device: str = "cuda:0"):
        os.makedirs(dir_save, exist_ok=True)

        if model.lower() == "vc2":
            model_folder = os.path.join(dir_save, "VC2")
        else:
            model_folder = os.path.join(dir_save, "RVM")
        os.makedirs(model_folder, exist_ok=True)

        def init_func():
            if model.lower() == "rvm":
                return RVM(
                    checkpoint_path=os.path.join(model_folder, cfg.led.rvm.checkpoint_path),
                    device=device,
                )
            elif model.lower() == "vc2":
                return VC2(dir_save=model_folder, device=device)
            else:
                logger.warning(
                    f"Model '{model}' is not in the supported list ('rvm', 'vc2'). Defaulting to 'RVM' for matting."
                )
                return RVM(
                    checkpoint_path=os.path.join(model_folder, cfg.led.rvm.checkpoint_path),
                    device=device,
                )

        self.model = init_model_with_lock(
            model_folder=model_folder,
            init_func=init_func,
        )

    @Timer("ModelMatting.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @handle_runtime_error
    def run(self, input_path: str, output_path: str, save_folder: bool = True) -> None:
        if save_folder:
            self.model.run(input_path, save_folder=save_folder, output_npy_folder=output_path)
        else:
            self.model.run(input_path, save_folder=save_folder, output_video_path=output_path)
        self._post_processing()

    def _post_processing(self):
        self.release()

    def release(self):
        del self.model
        gc.collect()
