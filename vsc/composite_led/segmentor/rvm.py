import os

import cv2
import numpy as np
import onnxruntime as ort
from codetiming import Timer
from fvutils.minio import download
from fvutils.videoio import FVideoCapture, PixelFormat

from vsc.composite_led.functions.helpers import parse_device
from vsc.utils.config import cfg
from vsc.utils.exception import ONNXRuntimeError
from vsc.utils.logger import logger_perf
from vsc.utils.package_helpers import create_temp_package_path


class RVM:
    def __init__(
        self,
        checkpoint_path: str = cfg.led.rvm.checkpoint_path,
        checkpoint_id: str = cfg.led.rvm.checkpoint_id,
        dtype: np.ndarray = np.float32,
        device: str = "cuda:0",
    ):
        checkpoint_path = create_temp_package_path(checkpoint_path)
        if not os.path.exists(checkpoint_path):
            checkpoint_path = download(checkpoint_id, os.path.dirname(checkpoint_path))

        self.device_type, self.device_id = parse_device(device)
        provider = (
            [("CUDAExecutionProvider", {"device_id": self.device_id})]
            if self.device_type == "cuda"
            else ["CPUExecutionProvider"]
        )
        self.dtype = dtype

        # Init the model
        try:
            self.model = ort.InferenceSession(checkpoint_path, providers=provider)
        except RuntimeError as e:
            raise ONNXRuntimeError(f"Failed to load ONNX model on {device} \nError: {str(e)}") from e

    def _pre_processing(self, frame: np.ndarray) -> np.ndarray:
        img = frame.astype(self.dtype).copy() / 255.0
        img = img[:, :, ::-1]  # BGR --> RGB
        img = np.transpose(img, (2, 0, 1))  # (C,H,W)
        img = np.expand_dims(img, axis=0)  # (B=1,C,H,W)
        return img

    def _post_processing(self, frame: np.ndarray):
        return np.transpose(frame[0], (1, 2, 0))  # (B, C, H, W) --> (H, W, C)

    def _auto_downsample_ratio(self, h: int, w: int):
        ratio = min(512 / max(h, w), 1)
        return ort.OrtValue.ortvalue_from_numpy(
            np.asarray([ratio], dtype=self.dtype), device_type=self.device_type, device_id=self.device_id
        )

    def process_image(self, image: np.ndarray):
        src = self._pre_processing(image)
        downsample_ratio = self._auto_downsample_ratio(src.shape[2], src.shape[3])
        rec = [
            ort.OrtValue.ortvalue_from_numpy(
                np.zeros([1, 1, 1, 1], dtype=self.dtype), device_type=self.device_type, device_id=self.device_id
            )
        ] * 4

        io = self.model.io_binding()

        for name in ['fgr', 'pha', 'r1o', 'r2o', 'r3o', 'r4o']:
            io.bind_output(name, device_type=self.device_type, device_id=self.device_id)

        io.bind_cpu_input('src', src)
        io.bind_ortvalue_input('r1i', rec[0])
        io.bind_ortvalue_input('r2i', rec[1])
        io.bind_ortvalue_input('r3i', rec[2])
        io.bind_ortvalue_input('r4i', rec[3])
        io.bind_ortvalue_input('downsample_ratio', downsample_ratio)

        self.model.run_with_iobinding(io)
        _, pha, *rec = io.get_outputs()
        pha = pha.numpy()
        return self._post_processing(pha)

    def process_video(self, video_input: str, output_npy_folder: str):
        frame_id = 0
        cap = FVideoCapture(video_input, output_pixel_format=PixelFormat.BGR24)
        cap_properties = cap.get_video_properties()
        width = int(cap_properties["width"])
        height = int(cap_properties["height"])
        rec = [
            ort.OrtValue.ortvalue_from_numpy(
                np.zeros([1, 1, 1, 1], dtype=self.dtype), device_type=self.device_type, device_id=self.device_id
            )
        ] * 4
        downsample_ratio = self._auto_downsample_ratio(height, width)
        os.makedirs(output_npy_folder, exist_ok=True)

        for ret, img in cap.read():
            if not ret:
                break
            src = self._pre_processing(img)

            io = self.model.io_binding()

            for name in ['fgr', 'pha', 'r1o', 'r2o', 'r3o', 'r4o']:
                io.bind_output(name, device_type=self.device_type, device_id=self.device_id)

            io.bind_cpu_input('src', src)
            io.bind_ortvalue_input('r1i', rec[0])
            io.bind_ortvalue_input('r2i', rec[1])
            io.bind_ortvalue_input('r3i', rec[2])
            io.bind_ortvalue_input('r4i', rec[3])
            io.bind_ortvalue_input('downsample_ratio', downsample_ratio)

            self.model.run_with_iobinding(io)
            _, pha, *rec = io.get_outputs()
            pha = pha.numpy()
            pha = self._post_processing(pha)
            np.save(os.path.join(output_npy_folder, f"{frame_id}.npy"), pha)
            frame_id += 1

    @Timer("RVM.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, video_input: str, output_npy_folder: str):
        self.process_video(video_input, output_npy_folder)


if __name__ == "__main__":
    model = RVM()
    pha = model.process_image(cv2.imread("data/led/test.png"))
    pha *= 255
    pha = pha.astype(np.uint8)
    cv2.imwrite("data/led/pha.png", pha)
