from dataclasses import dataclass
from enum import Enum, auto
from typing import Optional, Union

import cv2
import numpy as np
import pandas as pd
from fvutils.videoio import FVideoWriter


class OccludedCorner(Enum):
    A = 0
    B = 1
    C = 2
    D = 3


class HiddenEdge(Enum):
    LEFT = 0
    RIGHT = 1
    TOP = 2
    BOTTOM = 3


class ScreenOrientation(Enum):
    STATIC = 0
    ZOOMED = 1
    MOVING = 2


@dataclass
class InputArgs:
    video_path: str
    led_path: str
    save_path: Union[str, None] = None
    is_dev: bool = False


@dataclass
class OutputPaths:
    save_path: str = None
    save_path_stage1: str = None
    save_path_perf_stage1: str = None
    save_path_perf_stage3: str = None
    save_path_matte: str = None
    save_path_matte_npy: str = None
    save_path_mask: str = None
    save_path_mask_processed: str = None
    save_path_csv: str = None
    save_path_csv_processed: Union[str, pd.DataFrame] = None
    save_path_audio: str = None
    save_path_only_video: str = None


@dataclass
class VideoWriters:
    writer_output: Union[FVideoWriter, cv2.VideoWriter] = None
    writer_mask: Union[FVideoWriter, cv2.VideoWriter] = None
    writer_matte: Union[FVideoWriter, cv2.VideoWriter] = None
    writer_mask_processed: Union[FVideoWriter, cv2.VideoWriter] = None
    writer_stage1: Union[FVideoWriter, cv2.VideoWriter] = None


class VideoProcessingMode(Enum):
    FFMPEG = auto()
    CV2 = auto()


class VideoPropeties(Enum):
    TOTAL_FRAME = auto()


@dataclass
class Dimensions:
    """Data class for image dimensions.

    Attributes:
        height (int): Image height.
        width (int): Image width.
    """

    height: int = 0
    width: int = 0


@dataclass
class Location:
    """Data class for corner location.

    Attributes:
        x1 (int): x1 coordinate
        y1 (int): y1 coordinate
        x2 (int): x2 coordinate
        y2 (int): y2 coordinate
    """

    x1: int = 0
    y1: int = 0
    x2: int = 0
    y2: int = 0


@dataclass
class Point:
    """Data class for a Point"""

    x: int = 0
    y: int = 0


@dataclass
class Coordinates:
    """Data class for corner coordinates"""

    data: np.ndarray = None
    data_list: list = None

    def __post_init__(self):
        self.data = self._validate_data(self.data)
        self.data_list = self.as_list(self.data)

    def _validate_data(self, data):
        """Validates and converts data to a 2D numpy array."""

        if data is not None:
            if isinstance(data, list):
                data = np.array(data)
            data_list = [list(item) for item in data if isinstance(item, np.ndarray)]
            data = np.array(data_list)
            data = np.int32(data)
            assert len(data.shape) == 2 and data.shape[1] == 2, f"Coordinates must be 2D (x, y), but give a shape {data.shape}"
        return data

    def as_list(self, data: np.ndarray):
        if data is not None:
            return data.tolist()


@dataclass
class Detection:
    """Data class for detector output.

    Attributes:
        conf (float): Confidences of faces
        boxes (np.ndarray): Bounding boxes of faces

    """

    conf: float = None
    boxes: list[Location] = None


@dataclass
class MaskScreen:
    """Shape: (h,w), value: [0, 255], dtype: uint8"""

    data: np.ndarray = None

    def __post_init__(self):
        self.data = self._validate_mask(self.data)

    def _validate_mask(self, img):
        """Validates and converts the mask image if necessary."""

        if img is not None:
            if img.dtype != 'uint8':
                img = img.astype(np.uint8)  # Convert in-place for efficiency

            if len(img.shape) == 3 and img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            if len(img.shape) == 3 and img.shape[2] == 1:
                img = np.squeeze(img)

            if img.shape[0] == 0 or img.shape[1] == 0:
                raise ValueError("Mask image cannot have zero dimensions")

            if not np.all(np.logical_and(img >= 0, img <= 255)):
                raise ValueError("Mask image values must be within the range [0, 255]")

        return img


@dataclass
class MatteTimi:
    """Shape: (h, w), value: [0, 1], float32"""

    data: np.ndarray = None

    def __post_init__(self):
        self.data = self._validate_mask(self.data)

    def _validate_mask(self, img):
        if img is not None:
            if len(img.shape) == 3 and img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

            if len(img.shape) == 3 and img.shape[2] == 1:
                img = np.squeeze(img)

            if img.shape[0] == 0 or img.shape[1] == 0:
                raise ValueError("Mask image cannot have zero dimensions")

            if not np.all(np.logical_and(img >= 0, img <= 1)):
                raise ValueError("Mask image values must be within the range [0, 1]")

        return img


@dataclass
class Request:
    data: dict = None
    request_id: str = None
    api_name: str = None

    def __post_init__(self):
        if self.data is not None:
            self.request_id = self.data.get("request_id")
            self.api_name = self.data.get("api_name")


@dataclass
class ImageData:
    """The main data class used for passing data between the different facetorch modules.

    Attributes:
        path_input (str): Path to the input image.
        path_output (str): Path to the output image where the resulting image is saved.
        img (np.ndarray): Original image tensor used for drawing purposes.
        dims (Dimensions): Dimensions of the image (height, width).
        det (Detection): Detection data given by the detector.
        version (str): Version of the facetorch library.

    """

    input_args: Optional[InputArgs] = None
    output_paths: Optional[OutputPaths] = None
    video_writers: Optional[VideoWriters] = None
    frame_id: Optional[int] = None
    corners_detected: Optional[Detection] = None
    corner_missing: Optional[str] = None
    coordinates_based_segment: Optional[np.ndarray] = None
    coordinates_based_segment_low_epsilon: Optional[np.ndarray] = None
    coordinates: Optional[np.ndarray] = None
    coordinates_float: Optional[np.array] = None
    coordinates_ori: Optional[np.ndarray] = None
    mask_screen: Optional[np.ndarray] = None
    hidden_name: Optional[str] = None
    matte_timi: Optional[np.ndarray] = None
    timi_on_screen: Optional[bool] = None
    frame: Optional[np.ndarray] = None
    led: Optional[np.ndarray] = None
    final_frame: Optional[np.ndarray] = None
    dims: Optional[Dimensions] = None
    version: Optional[str] = None
    request: Optional[Request] = None
    static_screen: Optional[bool] = False
    expansion_width_range: Optional[float] = None
    expansion_height_range: Optional[float] = None
    fitted_coords_dict: Optional[dict] = None
    have_screen: Optional[bool] = True

    def reset_frame(self) -> None:
        """Reset the original image tensor to empty state."""
        self.frame = np.empty(self.frame.shape)
        self.final_frame = np.empty(self.final_frame.shape)
        self.led = np.empty(self.led.shape)

    def reset_corners_detected(self) -> None:
        """Reset the corners prediction tensors to empty state."""
        self.corners_detected = Detection()

    def reset_coordinates(self) -> None:
        self.coordinates = Coordinates()
        self.coordinates_ori = Coordinates()
        self.coordinates_based_segment = Coordinates()

    def reset_mask(self) -> None:
        self.mask_screen = MaskScreen()
        self.matte_timi = MatteTimi()

    def reset_frame_id(self) -> None:
        self.frame_id = 0

    def update_frame_id(self):
        self.frame_id += 1

    def reset(self, reset_video: bool = False) -> None:
        """Reset the tensors to empty state."""
        if reset_video:
            self.reset_frame_id()

        self.reset_frame()
        self.reset_mask()
        self.reset_corners_detected()
        self.reset_coordinates()

    def set_dims(self) -> None:
        """Set the dimensions attribute from the img attribute."""
        self.dims.height = self.frame.shape[0]
        self.dims.width = self.frame.shape[1]
