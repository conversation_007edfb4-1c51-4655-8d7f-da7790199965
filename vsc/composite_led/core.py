import os
from typing import Any, List, Tuple

from codetiming import Timer
from fvutils.progress import ProgressUpdater

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.constants import SAVE_PATH_SUFFIXES
from vsc.composite_led.datastruct import (
    ImageData,
    InputArgs,
    OutputPaths,
    Request,
    VideoProcessingMode,
    VideoWriters,
)
from vsc.composite_led.functions.screen import Screen
from vsc.composite_led.processor import (
    Stage1Processor,
    Stage2Processor,
    Stage3Processor,
)
from vsc.composite_led.utilizer import VideoReaderProcessor, VideoWriterProcessor
from vsc.utils import cfg
from vsc.utils.logger import logger, logger_perf


class CompositeLEDInference(BaseProcessor):
    def __init__(self, device: str = "cuda:0", weights_folder: str = "checkpoints/"):
        self.reader = VideoReaderProcessor()
        self.writer = VideoWriterProcessor()
        self.stage1 = Stage1Processor(self.reader, self.writer, device=device, weights_folder=weights_folder)
        self.stage2 = Stage2Processor()
        self.stage3 = Stage3Processor(self.reader, self.writer, device=device)

    def _generate_save_paths(self, suffixes: List[str], save_path: str) -> Tuple[str, ...]:
        """Generate paths for saving processed files based on the provided save_path.
        Args:
            suffixes (List[str]): List of suffixes to be appended to the save_path.
            save_path (str): Base path for saving processed files.

        Returns:
            Tuple[str, ...]: Tuple of generated save paths.
        """
        return tuple(save_path[:-4] + suffix for suffix in suffixes)

    def _validate_paths(self, video_path: str, led_path: str):
        """Validate the paths of the video and LED files.

        Args:
            video_path (str): Path to the video file.
            led_path (str): Path to the LED file.

        Raises:
            TypeError: If video_path is not a string.
            ValueError: If video_path does not have a valid extension.
            FileNotFoundError: If video or LED file does not exist.
        """
        if not isinstance(video_path, str):
            raise TypeError(f"Expected 'video_path' to be a string, got {type(video_path).__name__} instead.")

        if not video_path.endswith(".mp4"):
            raise ValueError(f"Invalid video file extension: '{video_path}'. Expected a .mp4 file.")

        if not os.path.isfile(video_path):
            raise FileNotFoundError(f"Video file not found: '{video_path}'.")

        if not os.path.isfile(led_path):
            raise FileNotFoundError(f"LED file not found: '{led_path}'.")

        logger.info(f"{'🏁 ' * 20}\nProcessing video ---{os.path.basename(video_path)}---")

    def _create_video_writers(self, video_writers: VideoWriters, output_path: OutputPaths, data: ImageData) -> None:
        """
        Create video writers.

        Args:
            video_writers (VideoWriters): Video writers instance.
            output_path (OutputPaths): Output paths.
            data (ImageData): For get input args.
        """
        cap, cap_cv2 = self._read_video(data.input_args)
        video_properties_ffmpeg = self.reader.video_properties(cap, VideoProcessingMode.FFMPEG)
        video_properties_cv2 = self.reader.video_properties(cap_cv2, VideoProcessingMode.CV2)

        writers_info = [
            ("writer_output", output_path.save_path_only_video, VideoProcessingMode.FFMPEG, video_properties_ffmpeg),
            ("writer_mask", output_path.save_path_mask, VideoProcessingMode.CV2, video_properties_cv2),
            ("writer_matte", output_path.save_path_matte, VideoProcessingMode.CV2, video_properties_cv2),
            ("writer_mask_processed", output_path.save_path_mask_processed, VideoProcessingMode.CV2, video_properties_cv2),
            ("writer_stage1", output_path.save_path_stage1, VideoProcessingMode.CV2, video_properties_cv2),
        ]

        for writer_attr, output_path, writer_mode, writer_properties in writers_info:
            setattr(
                video_writers,
                writer_attr,
                self.writer.create_writer(output_path, writer_mode, writer_properties),
            )

    def _read_video(self, input_args: InputArgs) -> Tuple[Any, Any]:
        """
        Read video using different processing modes.

        Args:
            input_args (InputArgs): Input arguments.

        Returns:
            Tuple[Any, Any]: Video capture instances.
        """
        cap = self.reader.read(input_args.video_path, VideoProcessingMode.FFMPEG)
        cap_cv2 = self.reader.read(input_args.video_path, VideoProcessingMode.CV2)
        return cap, cap_cv2

    @Timer("CompositeLEDInference._pre_process_input", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _pre_process_input(self, video_path: str, led_path: str, save_path: str, is_dev: bool, request: dict = None):
        """Pre-process input data before running inference.

        Args:
            video_path (str): Path to the video file.
            led_path (str): Path to the LED file.
            save_path (str): Base path for saving processed files.
            is_dev (bool): Flag indicating whether it's a development mode.

        Returns:
            Data: Pre-processed input data.
        """
        self._validate_paths(video_path, led_path)
        input_args = InputArgs(video_path, led_path, save_path, is_dev)
        output_paths = OutputPaths(save_path=save_path)
        request = Request(request)
        video_writers = VideoWriters()
        data = ImageData(input_args=input_args, output_paths=output_paths, video_writers=video_writers, request=request)

        (
            data.output_paths.save_path_stage1,
            data.output_paths.save_path_only_video,
            data.output_paths.save_path_audio,
            data.output_paths.save_path_mask,
            data.output_paths.save_path_mask_processed,
            data.output_paths.save_path_csv,
            data.output_paths.save_path_perf_stage1,
            data.output_paths.save_path_perf_stage3,
            data.output_paths.save_path_matte,
            data.output_paths.save_path_matte_npy,
        ) = self._generate_save_paths(SAVE_PATH_SUFFIXES, save_path)

        self._create_video_writers(data.video_writers, data.output_paths, data)

        data.static_screen, data.have_screen = Screen.check_status(data.input_args.video_path)
        return data

    def release(self, data: ImageData):
        del data

    @Timer("CompositeLEDInference.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(
        self,
        video_path: str,
        led_path: str,
        save_path: str,
        is_dev: bool = False,
        init_information: dict = {"coordinates": None, "expansion_height_range": None, "expansion_width_range": None},
        progress: ProgressUpdater = None,
    ):
        """Run the composite LED inference.

        Args:
            video_path (str): Path to the video file.
            led_path (str): Path to the LED file.
            save_path (str): Base path for saving processed files.
            is_dev (bool, optional): Flag indicating whether it's a development mode. Defaults to False.
        """
        if progress is None:
            progress = ProgressUpdater()

        data = self._pre_process_input(video_path, led_path, save_path, is_dev)
        data.is_dev = is_dev
        data.coordinates = init_information["coordinates"]
        data.expansion_height_range = init_information["expansion_height_range"]
        data.expansion_width_range = init_information["expansion_width_range"]
        if data.have_screen:
            self.stage1.run(data, progress)
            self.stage2.run(data, progress)
            self.stage3.run(data, progress)
        else:
            Screen.handle_missing(data)
        self.release(data)
        progress.complete(message="Completed")
