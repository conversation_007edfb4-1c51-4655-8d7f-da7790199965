import os.path
import time
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Optional, Tuple

import cv2
import deprecation
import numpy as np
from alive_progress import alive_bar
from codetiming import Timer
from fvutils import media
from fvutils.progress import ProgressUpdater
from fvutils.videoio import FVideoCapture, PixelFormat
from tqdm import tqdm

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.datastruct import (
    ImageData,
    InputArgs,
    OutputPaths,
    VideoProcessingMode,
    VideoPropeties,
    VideoWriters,
)
from vsc.composite_led.functions import (
    Coordinates,
    MaskScreen,
    PostProcessing,
    Screen,
    helpers,
)
from vsc.composite_led.processor.corners import CornersValidator
from vsc.composite_led.segmentor.model_matting import ModelMatting, get_matte_from_npy
from vsc.composite_led.utilizer import Draw, VideoReaderProcessor, VideoWriterProcessor
from vsc.utils import cfg, system_utils
from vsc.utils.logger import logger, logger_perf
from vsc.utils.system_utils import save_to_csv, visualize_data


class Stage1Processor(BaseProcessor):
    def __init__(
        self,
        video_reader_processor: VideoReaderProcessor,
        video_writer_processor: VideoWriterProcessor,
        model_matting: str = cfg.led.model_matting,
        device: str = "cuda:0",
        weights_folder: str = "checkpoints/",
    ) -> None:
        """Get mask screen and coordinates of the screen from input."""
        self.video_reader_processor = video_reader_processor
        self.video_writer_processor = video_writer_processor
        self.model_matting = ModelMatting(model_matting, dir_save=weights_folder, device=device)
        self.corners_validator = CornersValidator(device=device, weights_folder=weights_folder)
        self.__matting_reader_iter = None
        super().__init__()

    @Timer("Stage1Processor._process_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _process_coordinates(self, data: ImageData) -> ImageData:
        """
        Process coordinates for the given data.

        Args:
            data (ImageData): Input image data.

        Returns:
            ImageData: Processed data.
        """

        data.corner_missing = None
        data.hidden_name = None
        data.frame = data.frame.astype(np.uint8)

        if len(data.coordinates_based_segment) < 3:
            logger.info(f"No green screen detected in the frame. Coordinates raw detected: {data.coordinates_based_segment}")
            data.coordinates = helpers.create_dummy_value(cfg.led.dummy_value)
            return data

        if (not data.static_screen) or (data.static_screen and data.coordinates is None):
            self.corners_validator.run(data)

        if data.input_args.is_dev:
            Draw.visualize_point(data.frame, data.coordinates, (100, 255, 100), "after_validate", radius=30, offset_y=-30)

        if len(data.coordinates) == 3:
            data.coordinates, data.corner_missing = Coordinates.get_coordinates(
                data.frame,
                data.mask_screen,
                data.coordinates_based_segment_low_epsilon,
                data.coordinates,
                data.input_args.is_dev,
            )

        if len(data.coordinates) == 4:
            data.coordinates = Coordinates.handle_outliers(
                data.coordinates
            )  # `handle_outliers` should be executed before performing `find_hindden_coordinates`
            data.hidden_name = Screen.find_hidden_name(data.coordinates, data.frame)

            # Validate coordinates
            PostProcessing.validate_coordinates(
                data.coordinates, (data.frame.shape[1], data.frame.shape[0]), cfg.led.post_processing.validation_threshold
            )
        else:
            logger.critical(f"[Stage1] Coordinates in frame must be equal 4! Coordinates: {data.coordinates}")
            data.coordinates = helpers.create_dummy_value(cfg.led.dummy_value)

        return data

    def _process_audio(self, intput_args: InputArgs, output_paths: OutputPaths) -> None:
        """
        Process audio from video.

        Args:
            input_args (InputArgs): Input arguments.
            output_paths (OutputPaths): Output paths.
        """
        if media.has_audio(intput_args.video_path):
            media.split_audio_from_video(intput_args.video_path, output_paths.save_path_audio)

    def _get_properties(self, input_args, *args) -> Any:
        """
        Get video properties.

        Args:
            input_args (InputArgs): Input arguments.
            *args (str): Additional arguments.

        Returns:
            Any: Video properties.
        """
        return self.video_reader_processor.get_properties(input_args.video_path, VideoProcessingMode.FFMPEG, *args)

    @Timer("Stage1Processor._pre_process_input", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _pre_process_input(self, data: ImageData) -> Tuple[Any, int]:
        """
        Pre-process input data before running inference.

        Args:
            data (ImageData): Input image data.

        Returns:
            Tuple[Any, Any]: Video capture instances.
        """
        cap = FVideoCapture(data.input_args.video_path, output_pixel_format=PixelFormat.BGR24)
        video_properties = cap.get_video_properties()
        total_frames = int(video_properties["nb_frames"])
        data.reset_frame_id()

        with ThreadPoolExecutor() as executor:
            futures = []
            futures.append(executor.submit(system_utils.set_cpu_cores))
            futures.append(executor.submit(self._process_audio, data.input_args, data.output_paths))
            futures.append(executor.submit(self._get_matte_foreground_from_video, data))
            futures.append(executor.submit(Screen.get_mask, data))

        executor.shutdown(wait=True)

        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                raise e

        return cap, total_frames

    @Timer("Stage1Processor._save_result", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _save_result(self, data: ImageData) -> None:
        """
        Save the result of the inference.

        Args:
            data (ImageData): Input image data.
        """

        with ThreadPoolExecutor() as executor:
            executor.submit(
                helpers.save_coordinates_to_csv,
                data.frame_id,
                data.coordinates,
                data.coordinates_based_segment,
                data.coordinates_based_segment_low_epsilon,
                data.corner_missing,
                data.hidden_name,
                data.output_paths.save_path_csv,
            )

            if data.input_args.is_dev:
                executor.submit(
                    self.video_writer_processor.write, data.video_writers.writer_stage1, VideoProcessingMode.CV2, data.frame
                )
                executor.submit(
                    self.video_writer_processor.write,
                    data.video_writers.writer_matte,
                    VideoProcessingMode.CV2,
                    data.matte_timi,
                )

        executor.shutdown(wait=True)

    @Timer("Stage1Processor._get_matte_foreground_from_video", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _get_matte_foreground_from_video(self, data: ImageData) -> None:
        """
        Generate the matte foreground data using the matting model.

        This method runs the matting model on the given frame to generate
        the matte foreground data and assigns it to `data.matte_timi`.

        Args:
            data (ImageData): An instance of ImageData containing the frame to be processed.

        """
        self.model_matting.run(
            input_path=data.input_args.video_path, output_path=data.output_paths.save_path_matte, save_folder=False
        )

    @Timer("Stage1Processor._get_matte_foreground", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _get_matte_foreground(self, data: ImageData) -> None:
        if self.__matting_reader_iter is None:
            self.__matting_reader_iter = FVideoCapture(
                data.output_paths.save_path_matte, output_pixel_format=PixelFormat.BGR24
            ).read()

        ret, matte = next(self.__matting_reader_iter)
        matte = matte[..., 0].astype(np.float32) / 255.0
        data.matte_timi = matte

    def release(self):
        self.corners_validator.release()

    @Timer("Stage1Processor.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, data: ImageData, progress: ProgressUpdater) -> None:
        cap, total_frames = self._pre_process_input(data)
        progress.set_loop(total_frames, cfg.progress.stage1)
        matting_reader = FVideoCapture(data.output_paths.save_path_matte, output_pixel_format=PixelFormat.BGR24)

        for (ret, data.frame), (ret2, matte) in tqdm(zip(cap.read(), matting_reader.read()), total=total_frames):
            if not ret or not ret2:
                break

            # get matte timi
            matte = matte[..., 0].astype(np.float32) / 255.0
            data.matte_timi = matte

            if not (data.frame_id > 0 and data.static_screen):
                data.mask_screen, data.coordinates_based_segment = MaskScreen.get_screen_mask_and_coordinates(
                    image=cv2.cvtColor(data.frame, cv2.COLOR_BGR2RGB)
                )
                data.coordinates_based_segment_low_epsilon = MaskScreen.get_coordinates(
                    mask=data.mask_screen, epsilon=cfg.led.mask.low_epsilon, old_method=True
                )

                if data.coordinates_based_segment is None:
                    data.coordinates = helpers.create_dummy_value(cfg.led.dummy_value)
                    data.hidden_name = None

                else:
                    data = self._process_coordinates(data)

            self._save_result(data)
            data.update_frame_id()

        matting_reader.release()
        self.video_writer_processor.release(VideoProcessingMode.CV2, data.video_writers.writer_stage1)
        self.release()

    @Timer("Stage1Processor.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @deprecation.deprecated(details="Has been replaced by method run")
    def run_multi_threads(self, data: ImageData, progress: ProgressUpdater) -> None:
        """
        Run the stage 1 inference using multi-threads.

        This method's not working properly with new timi matte video saving. Because it does not guarantee the order of
        frames processed with multi threads when using timi matte video

        Args:
            data (ImageData): Input image data.
        """
        cap, total_frames = self._pre_process_input(data)
        progress.set_loop(total_frames, cfg.progress.stage1)
        matting_reader = FVideoCapture(data.output_paths.save_path_matte, output_pixel_format=PixelFormat.BGR24)

        with ThreadPoolExecutor() as executor, alive_bar(
            total=total_frames, theme="musical", length=150
        ) as bar, progress as observer:
            for (ret, data.frame), (ret2, matte) in zip(cap.read(), matting_reader.read()):
                timer_start = time.time()
                if not ret and not ret2:
                    break
                try:
                    # get matte timi
                    matte = matte[..., 0].astype(np.float32) / 255.0
                    data.matte_timi = matte
                    if not (data.frame_id > 0 and data.static_screen):
                        get_mask_and_coordinates = executor.submit(
                            MaskScreen.get_screen_mask_and_coordinates, cv2.cvtColor(data.frame, cv2.COLOR_BGR2RGB)
                        )

                        result_mask_and_coordinates = get_mask_and_coordinates.result()

                        data.mask_screen, data.coordinates_based_segment = result_mask_and_coordinates

                        data.coordinates_based_segment_low_epsilon = MaskScreen.get_coordinates(
                            data.mask_screen, cfg.led.mask.low_epsilon, old_method=True
                        )

                        if data.coordinates_based_segment is None:
                            data.coordinates = helpers.create_dummy_value(cfg.led.dummy_value)
                            data.hidden_name = None

                        else:
                            data = self._process_coordinates(data)

                    if data.input_args.is_dev:
                        Draw.put_text(data.frame, f"Frame_id: {data.frame_id}", (600, 600), (100, 100, 255))
                        Draw.visualize_point(data.frame, data.coordinates, (100, 0, 100), "final", radius=20, offset_y=30)
                        num_cores_used, cpu_used = 0, 0
                        execution_time = time.time() - timer_start
                        save_to_csv(
                            [data.frame_id, num_cores_used, cpu_used, execution_time], data.output_paths.save_path_perf_stage1
                        )
                        logger_perf.debug(f"Execution time: {execution_time}")

                except KeyboardInterrupt:
                    logger.info("Stopped!")
                    break
                except Exception:
                    logger.critical(f"Error when find coordinates with traceback: {traceback.format_exc()}")
                    data.coordinates = helpers.create_dummy_value(cfg.led.dummy_value)
                    data.hidden_name = None

                self._save_result(data)
                data.update_frame_id()
                bar()
                observer()

            self.video_writer_processor.release(VideoProcessingMode.CV2, data.video_writers.writer_stage1)
            matting_reader.release()
            if data.input_args.is_dev:
                visualize_data(data.output_paths.save_path_perf_stage1)

        self.release()

        executor.shutdown(wait=True)
