import gc
import traceback
from typing import Union

import numpy as np
import pandas as pd
from codetiming import Timer
from fvutils.progress import ProgressUpdater

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.datastruct import (
    ImageData,
    InputArgs,
    OutputPaths,
    ScreenOrientation,
)
from vsc.composite_led.functions import PostProcessing, helpers
from vsc.utils import cfg
from vsc.utils.logger import logger, logger_perf


class Stage2Processor(BaseProcessor):
    def __init__(self) -> None:
        """
        Initialize Stage2Processor.
        """
        pass

    @Timer("Stage2Processor._handle_shaking", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _handle_shaking(self, df: Union[pd.DataFrame, str], input_args: InputArgs, output_paths: OutputPaths) -> pd.DataFrame:
        """
        Handle shaking coordinates when corner missing sometime hidden and sometime appear. Supported shot2-3.

        Args:
            input_args (InputArgs): Input arguments.
            output_paths (OutputPaths): Output paths.

        Returns:
            DataFrame: DataFrame with recalculated coordinates.
        """
        return PostProcessing.handle_shaking(
            df, helpers.get_frame(input_args.video_path), cfg.led.handle_shaking.threshold, output_paths.save_path_mask
        )

    def _stabilize_hidden_coordinates(self, df: pd.DataFrame) -> pd.DataFrame:
        return PostProcessing.stabilize_hidden_coordinates(df)

    @Timer("Stage2Processor.double_check_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def double_check_coordinates(self, df: pd.DataFrame, input_args: InputArgs):
        """
        Double-check the coordinates with rules 1 and 2.

        Args:
            df (DataFrame): DataFrame containing coordinates.
            input_args (InputArgs): Input arguments.

        Returns:
            DataFrame: DataFrame with double-checked coordinates.
        """
        return PostProcessing.double_check_coordinates(df, input_args.video_path, is_dev=input_args.is_dev)

    @Timer("Stage2Processor._stabilize_coordinates_interpolation", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _stabilize_coordinates_interpolation(self, df: pd.DataFrame):
        """
        Stabilize coordinates using interpolation.

        Args:
            df (DataFrame): DataFrame containing coordinates.

        Returns:
            DataFrame: DataFrame with stabilized coordinates.
        """
        return PostProcessing.stabilize_coordinates_interpolation(
            df,
            cfg.led.stabilize.window_size,
            cfg.led.stabilize.std_thresh,
            cfg.led.stabilize.distance_threshold,
        )

    @Timer("Stage2Processor._remove_outliers", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _remove_outliers(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Remove outliers using CSMA in a smoothing function with a small window size

        Args:
            df (DataFrame): DataFrame containing coordinates.

        Returns:
            DataFrame: DataFrame with outliers removed.
        """
        return PostProcessing.smoothing_coordinates(
            df,
            savgol_window_length=cfg.led.smoothing.savgol_window_size_handle_outliers,
            savgol_polyorder=cfg.led.smoothing.savgol_polyorder,
            savgol_padding_mode=cfg.led.smoothing.savgol_padding_mode,
            butter_order=cfg.led.smoothing.butterworth_order,
            butter_cutoff_freq=cfg.led.smoothing.butterworth_cutoff_frequency,
            gauss_sigma=cfg.led.smoothing.gaussian_sigma,
            gauss_padding_mode=cfg.led.smoothing.gaussian_padding_mode,
        )

    @Timer("Stage2Processor._smoothing_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _smoothing_coordinates(self, df: pd.DataFrame, frame: np.ndarray) -> pd.DataFrame:
        """
        Smooth coordinates.

        Args:
            df (DataFrame): DataFrame containing coordinates.

        Returns:
            DataFrame: DataFrame with smoothed coordinates.
        """
        if PostProcessing.check_screen_orientation(df, frame) is ScreenOrientation.ZOOMED:
            logger.info("The scenario is a zoomed screen, using the CMA smoothing function")
            return PostProcessing.smoothing_coordinates(
                df,
                savgol_window_length=cfg.led.smoothing.savgol_window_size,
                savgol_polyorder=cfg.led.smoothing.savgol_polyorder,
                savgol_padding_mode=cfg.led.smoothing.savgol_padding_mode,
                butter_order=cfg.led.smoothing.butterworth_order,
                butter_cutoff_freq=cfg.led.smoothing.butterworth_cutoff_frequency,
                gauss_sigma=cfg.led.smoothing.gaussian_sigma,
                gauss_padding_mode=cfg.led.smoothing.gaussian_padding_mode,
            )
        return df

    @Timer("Stage2Processor._handle_unusual_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _handle_unusual_coordinates(self, df: [pd.DataFrame, str]) -> pd.DataFrame:
        return PostProcessing.handle_unusual_dummy_df(df)

    @Timer("Stage2Processor.release", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def release(self, *args):
        del args
        gc.collect()

    @Timer("Stage2Processor.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, data: ImageData, progress: ProgressUpdater) -> ImageData:
        """
        Run the processing pipeline.

        Args:
            data (ImageData): Input image data.

        Returns:
            ImageData: Processed image data.
        """
        # Initialize variables to None to ensure they exist
        df_handle_unusual = df_handle_shaking = df_recalculate = df_stable = df_handle_outliers = df_smoothed = None

        try:
            if data.static_screen:
                data.output_paths.save_path_csv_processed = PostProcessing.read_csv(data.output_paths.save_path_csv)
                progress.update(cfg.progress.stage2)
                return data

            df_handle_unusual = self._handle_unusual_coordinates(data.output_paths.save_path_csv)
            progress.add_progress(cfg.progress.stage2_step)

            df_handle_shaking = self._handle_shaking(df_handle_unusual, data.input_args, data.output_paths)
            progress.add_progress(cfg.progress.stage2_step)

            df_recalculate = self._stabilize_hidden_coordinates(df_handle_shaking)
            progress.add_progress(cfg.progress.stage2_step)

            df_stable = self._stabilize_coordinates_interpolation(df_recalculate)
            progress.add_progress(cfg.progress.stage2_step)

            df_handle_outliers = self._remove_outliers(df_stable)
            progress.add_progress(cfg.progress.stage2_step)

            df_smoothed = self._smoothing_coordinates(df_handle_outliers, helpers.get_frame(data.input_args.video_path))
            progress.add_progress(cfg.progress.stage2_step)

            self.double_check_coordinates(df_smoothed, data.input_args)
            progress.add_progress(cfg.progress.stage2_step)

            data.output_paths.save_path_csv_processed = df_smoothed

        except Exception:
            logger.critical(f"[Stage2] Error when processing data frame: {traceback.format_exc()}")
            data.output_paths.save_path_csv_processed = PostProcessing.read_csv(data.output_paths.save_path_csv)

        finally:
            self.release(df_handle_unusual, df_handle_shaking, df_recalculate, df_stable, df_handle_outliers, df_smoothed)
            progress.update(cfg.progress.stage2)

        return data
