import os

import numpy as np
from fvutils.videoio import FVideoCapture, PixelFormat

from vsc.utils.logger import logger


class LEDProcessor:
    """
    LEDProcessor handles video or image input and provides a frame generator.
    """

    def __init__(self, led_path: str, loop: bool = True) -> None:
        """
        Initialises the LEDProcessor.

        Args:
            led_path (str): Path to the video or image file.
            loop (bool): Determines the behavior after the video ends.
                - True: Loops back to the start of the video.
                - False: Keeps duplicating the last frame after the video ends.

        Raises:
            AssertionError: If the provided led_path does not exist.
        """
        assert os.path.exists(led_path), "led_path does not exist. Please re-check!!!"

        self.led_path = led_path
        self.last_frame = None

        self._init_capture()
        self._frame_iter = self.frame_generator()
        self.loop = loop
        self.is_image = True if "nb_frames" not in self.capture_properties.keys() else False

    def _init_capture(self):
        """(Re)initialise the FVideoCapture."""
        self.capture = FVideoCapture(self.led_path, output_pixel_format=PixelFormat.BGR24)
        self.capture_properties = self.capture.get_video_properties()

    def frame_generator(self):
        """Yields (ret, frame) for videos or the same frame repeatedly for images."""
        while True:
            for ret, frame in self.capture.read():
                if not ret:
                    break
                self.last_frame = frame
                yield ret, frame

            if not self.loop or self.is_image:
                while True:
                    yield True, self.last_frame

            # Reinitialize the capture to loop
            self.capture.release()
            self._init_capture()

    def read(self):
        """Returns (ret, frame) from the generator."""
        return next(self._frame_iter)

    def release(self):
        """Release self.capture."""
        self.capture.release()
