import os
import shutil
import tempfile
from abc import abstractmethod
from typing import Optional, Tuple, Union

import cv2
import numpy as np
from codetiming import Timer

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.datastruct import ImageData, InputArgs
from vsc.composite_led.functions import PostProcessing, helpers
from vsc.composite_led.functions.coordinates import Coordinates
from vsc.utils import cfg
from vsc.utils.logger import logger_debug, logger_perf
from vsc.utils.minio_utils import is_downloadable, upload, upload_directory


class Validator(BaseProcessor):
    def __init__(self) -> None:
        pass

    @abstractmethod
    def _check_unusual(self):
        pass


class Validator1(Validator):
    pass


class Validator2(Validator):
    pass


class Validator3(Validator):
    """A class to perform validation checks unusual black."""

    def __init__(self) -> None:
        self.message_info_list: list = []
        self.temp_dir: str = tempfile.mkdtemp()

    def _check_unusual(
        self,
        final_frame: np.ndarray,
        coordinates: Union[np.ndarray, list],
        mask_screen: np.ndarray,
        frame_id: int,
        threshold: float = 5e-3,
    ) -> Tuple[Optional[str], Optional[str], int, str]:
        """Check for unusual black areas on the screen.

        Args:
            final_frame (np.ndarray): The final frame.
            coordinates (Union[np.ndarray, List]): The coordinates.
            mask_screen (np.ndarray): The mask screen.
            frame_id (int): The frame ID.
            threshold (float, optional): The threshold value. Defaults to 5e-3.

        Returns:
            Tuple[Optional[str], Optional[str], int, str]: A tuple containing status error, description, frame ID, and solution.
        """
        zero_values_indices = np.all(final_frame == [0, 0, 0], axis=-1)
        placeholder_values = np.ones_like(final_frame) * 255
        placeholder_values[zero_values_indices] = final_frame[zero_values_indices]
        placeholder_values = cv2.cvtColor(placeholder_values, cv2.COLOR_BGR2GRAY)
        placeholder_values = cv2.bitwise_not(placeholder_values)
        placeholder_values_on_screen = cv2.bitwise_and(placeholder_values, mask_screen, mask=mask_screen)
        contours, _ = cv2.findContours(placeholder_values_on_screen, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        area_list = [cv2.contourArea(contour) for contour in contours]

        status_error = None
        description = None
        solution = "Reproduce and debug 👨‍🎤"

        if len(area_list) == 0:
            return status_error, description, frame_id, solution

        max_area = max(area_list)
        area_screen = Coordinates.cal_area(np.array(coordinates))

        if area_screen == 0:
            return status_error, description, frame_id, solution

        ratio = max_area / area_screen

        if ratio > threshold:
            status_error = "rule_3"
            description = "Detected unusual black areas on the screen."

        return status_error, description, frame_id, solution

    def _save_unusual_frame(self, final_frame: np.ndarray, frame_id: int) -> None:
        """Save the frame with unusual black areas.

        Args:
            final_frame (np.ndarray): The final frame.
            frame_id (int): The frame ID.
        """
        temp_img_path = os.path.join(self.temp_dir, f"{frame_id}.jpg")
        cv2.imwrite(temp_img_path, final_frame)

    def _upload_unusual_frame(self, video_path: str) -> str:
        """Upload all the frames violating rule_3 in the video to Minio.

        Args:
            video_path (str): The path to the video.

        Returns:
            str: The directory URL.
        """
        folder_frame_in_minio = os.path.join(cfg.minio.public_dir, cfg.minio.bug_dir, os.path.basename(video_path)[:-4])
        urls_list = upload_directory(self.temp_dir, folder_frame_in_minio)  # ["C100_Shot1/1.jpg", "C100_Shot1/2.jpg"...]
        return os.path.dirname(urls_list[0])

    @Timer("Validator3.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, data: ImageData) -> None:
        """Run the validation check.

        Args:
            data (ImageData): The image data.
        """
        message_info = PostProcessing.check_rule_3(
            data.coordinates_float, data.mask_screen, data.frame_id, threshold=cfg.led.double_check.rule_3.threshold
        )
        if message_info[0] is not None:
            self.message_info_list.append(list(message_info))
            self._save_unusual_frame(data.final_frame, data.frame_id)

    def release(self):
        """Release resources."""
        shutil.rmtree(self.temp_dir)
        self.message_info_list = []

    @Timer("Validator3.summary_log", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def summary_log(self, input_args: InputArgs) -> None:
        """Log and alert the summary information.

        Args:
            data (ImageData): Contains all necessary variables for summary_log.
        """
        if len(self.message_info_list) > 0:
            frame_id_rule_3 = np.array(self.message_info_list)[:, 2].astype(np.uint32).tolist()
            self.message_info_list[0][2] = frame_id_rule_3
            message_info_final = self.message_info_list[0]

            url_dir = self._upload_unusual_frame(input_args.video_path) if input_args.is_dev else None
            PostProcessing.log_and_alert(message_info_final, input_args.video_path, url_dir, input_args.is_dev)
            self.release()
