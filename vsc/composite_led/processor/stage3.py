import ast
import concurrent.futures
import json
import os
import shutil
import time
import traceback
from concurrent.futures import ThreadPoolExecutor
from typing import Tuple, Union

import cv2
import deprecation
import numba as nb
import numpy as np
import pandas as pd
import torch
import torch.nn.functional as F
import torchvision
from alive_progress import alive_bar
from codetiming import Timer
from cv2.ximgproc import guidedFilter
from fvutils import media
from fvutils.progress import ProgressUpdater
from fvutils.videoio import FVideoCapture, PixelFormat

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.datastruct import ImageData, VideoProcessingMode, VideoPropeties
from vsc.composite_led.functions import Coordinates, MaskScreen, PostProcessing, helpers
from vsc.composite_led.functions.composite import (
    composite_image,
    composite_with_pymatting,
)
from vsc.composite_led.functions.helpers import parse_device
from vsc.composite_led.processor.corner_classification import ClassifyCorners
from vsc.composite_led.processor.gf_torch import Gauss<PERSON><PERSON><PERSON>, guidedfilter2d_color
from vsc.composite_led.processor.led import LEDProcessor
from vsc.composite_led.processor.validator import Validator3
from vsc.composite_led.segmentor.model_matting import get_matte_from_npy
from vsc.composite_led.utilizer import Draw, VideoReaderProcessor, VideoWriterProcessor
from vsc.utils import cfg, system_utils
from vsc.utils.exception import NumFrameMismatchError
from vsc.utils.logger import logger, logger_perf
from vsc.utils.system_utils import get_system_info, save_to_csv, visualize_data


class Stage3Processor(BaseProcessor):
    def __init__(
        self,
        video_reader_processor: VideoReaderProcessor,
        video_writer_processor: VideoWriterProcessor,
        device: str = "cuda:0",
    ) -> None:
        """
        Initialize Stage3Processor.

        Args:
            video_reader_processor (VideoReaderProcessor): Video reader processor.
            video_writer_processor (VideoWriterProcessor): Video writer processor.
        """
        self.video_reader_processor = video_reader_processor
        self.video_writer_processor = video_writer_processor
        self.validator3 = Validator3()
        self.device_type, self.device_id = parse_device(device)
        self.device = ":".join([self.device_type, str(self.device_id)])
        self.__init_led_blending_pipeline()

        self.__matting_reader_iter = None

    def __init_led_blending_pipeline(self):
        '''
        Initialise the LED blending pipeline based on the selected processing device (GPU or CPU).

        This function configures the device and sets the appropriate LED blending method
        (either GPU-accelerated or CPU-based) for the pipeline.

        Args:
            None

        Sets:
            self.device (torch.device): The computation device (GPU or CPU).
            self.gauss_filter (GaussFilter): Gaussian filter initialised for GPU (if using GPU).
            self.led_blending_pipeline (function): The selected LED blending method (GPU or CPU).
        '''
        if self.device_type == "cuda":
            self.gauss_filter = GaussFilter((3, 3), 0, device=self.device)
            self.led_blending_pipeline = self.blending_led_gpu
            logger.debug(f"Stage3 will run guided filter on {self.device_type}-{self.device_id}")
        else:
            self.led_blending_pipeline = Stage3Processor.blending_led_cpu
            logger.debug(f"Stage3 will run guided filter on {self.device_type}")

    def _pre_process_input(self, data: ImageData) -> Tuple:
        """
        Pre-process input data.

        Args:
            data (ImageData): Input image data.

        Returns:
            Tuple: Tuple containing pre-processed data.
        """
        vid_cap = FVideoCapture(data.input_args.video_path, output_pixel_format=PixelFormat.BGR24)
        vid_properties = vid_cap.get_video_properties()
        mask_cap = FVideoCapture(data.output_paths.save_path_mask, output_pixel_format=PixelFormat.BGR24)
        mask_properties = mask_cap.get_video_properties()
        vid_total_frames = int(vid_properties["nb_frames"])
        mask_total_frames = int(mask_properties["nb_frames"])

        if vid_total_frames != mask_total_frames:
            raise NumFrameMismatchError(
                vid_total_frames, mask_total_frames, data.input_args.video_path, data.output_paths.save_path_mask
            )

        cap_led = LEDProcessor(data.input_args.led_path)
        return vid_cap, mask_cap, vid_total_frames, cap_led

    @Timer("Stage3Processor._get_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _get_coordinates(self, data: ImageData) -> ImageData:
        """
        Get coordinates from the DataFrame.

        Args:
            data (ImageData): Input image data.

        Returns:
            ImageData: Processed image data.
        """
        data.coordinates = PostProcessing.get_value_in_df(
            data.output_paths.save_path_csv_processed, "coordinates", data.frame_id
        )
        return data

    def _is_dummy_value(self, coordinates: list) -> bool:
        """
        Check if the given coordinates are dummy values.

        Args:
            coordinates (list): Coordinates to check.

        Returns:
            bool: True if the coordinates are dummy values, False otherwise.
        """
        return helpers.is_dummy_value(coordinates, cfg.led.dummy_value)

    @Timer("Stage3Processor._post_processing_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _post_processing_coordinates(self, data: ImageData) -> ImageData:
        """
        Post-process the coordinates.

        Args:
            data (ImageData): Input image data.

        Returns:
            ImageData: Processed image data.
        """
        data.coordinates_float = data.coordinates
        data.coordinates = np.array(data.coordinates_float).round().astype(int).tolist()
        data.coordinates_ori = data.coordinates
        data.coordinates_float = PostProcessing.expanding(
            data.coordinates_float, (data.expansion_width_range, data.expansion_height_range)
        )
        return data

    @Timer("Stage3Processor.calculate_expansion_range", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def calculate_expansion_range(self, data: ImageData):
        df = pd.read_csv(data.output_paths.save_path_csv)
        expansion_distance_width, expansion_distance_height = [], []
        data.fitted_coords_dict = {"A": [], "B": [], "C": [], "D": []}
        for frame_id in range(len(df)):
            A_coord, B_coord, C_coord, D_coord = (json.loads(df[corner][frame_id]) for corner in ["A", "B", "C", "D"])
            if data.expansion_height_range is not None and data.expansion_width_range is not None:
                data.fitted_coords_dict["A"].append(
                    [A_coord[0] - data.expansion_width_range, A_coord[1] - data.expansion_height_range]
                )
                data.fitted_coords_dict["B"].append(
                    [B_coord[0] - data.expansion_width_range, B_coord[1] + data.expansion_height_range]
                )
                data.fitted_coords_dict["C"].append(
                    [C_coord[0] + data.expansion_width_range, C_coord[1] + data.expansion_height_range]
                )
                data.fitted_coords_dict["D"].append(
                    [D_coord[0] + data.expansion_width_range, D_coord[1] - data.expansion_height_range]
                )
                continue

            if isinstance(df["coordinates_based_segment"][frame_id], str) and not np.all(
                np.array([A_coord, B_coord, C_coord, D_coord]) == 0
            ):
                contour_coords = np.array(json.loads(df["coordinates_based_segment"][frame_id]))
                fitted_coords, frame_expansion_width, frame_expansion_height = Coordinates.find_expansion_ranges(
                    (A_coord, B_coord, C_coord, D_coord), contour_coords
                )

                if None in fitted_coords:
                    fitted_coords = [A_coord, B_coord, C_coord, D_coord]

                corner_missing = df["corner_missing"][frame_id]
                if not pd.isna(corner_missing):
                    (x1, y1, x2, y2) = ClassifyCorners.extract_bbox(
                        coords=contour_coords, frame=None, stretch=0, clamp_option=False
                    )
                    A_mask = (x1, y1)
                    B_mask = (x1, y2)
                    C_mask = (x2, y2)
                    D_mask = (x2, y1)
                else:
                    A_mask = fitted_coords[0]
                    B_mask = fitted_coords[1]
                    C_mask = fitted_coords[2]
                    D_mask = fitted_coords[3]

                data.fitted_coords_dict["A"].append(A_mask)
                data.fitted_coords_dict["B"].append(B_mask)
                data.fitted_coords_dict["C"].append(C_mask)
                data.fitted_coords_dict["D"].append(D_mask)

                expansion_distance_width.append(frame_expansion_width)
                expansion_distance_height.append(frame_expansion_height)
            else:
                data.fitted_coords_dict["A"].append(A_coord)
                data.fitted_coords_dict["B"].append(B_coord)
                data.fitted_coords_dict["C"].append(C_coord)
                data.fitted_coords_dict["D"].append(D_coord)

        if data.expansion_height_range is None:
            data.expansion_height_range = max(expansion_distance_height, default=1.0)

        if data.expansion_width_range is None:
            data.expansion_width_range = max(expansion_distance_width, default=1.0)
        logger.debug(f"Expansion width: {data.expansion_width_range}, Expansion height: {data.expansion_height_range}")
        return data

    @Timer("Stage3Processor._post_processing_mask", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _post_processing_mask(self, data: ImageData) -> ImageData:
        """
        Post-process the mask screen.

        Args:
            data (ImageData): Input image data.

        Returns:
            ImageData: Processed image data.
        """
        data.mask_screen = MaskScreen.run(
            data.mask_screen,
            cv2.cvtColor(data.frame, cv2.COLOR_BGR2RGB),
            np.array([data.fitted_coords_dict[corner_name][data.frame_id] for corner_name in ["A", "B", "C", "D"]])
            .round()
            .astype(int),
            data.matte_timi,
            cfg.led.mask.smoothing,
            cfg.led.mask.erode,
        )
        return data

    @deprecation.deprecated(details="Has been replaced by guided filter")
    def _fill_led(self, data: ImageData) -> np.ndarray:
        """
        Fill LED into a frame using perspective transformation function.

        Args:
            data (ImageData): Input image data.

        Returns:
            np.ndarray: Frame with LED filled.
        """
        h, w = data.led.shape[:2]
        source_corners = np.array([[0, 0], [0, h], [w, h], [w, 0]], dtype=np.float32)
        target_corners = np.array(
            [data.coordinates_float[0], data.coordinates_float[1], data.coordinates_float[2], data.coordinates_float[3]],
            dtype=np.float32,
        )
        transform_matrix = cv2.getPerspectiveTransform(source_corners, target_corners)
        result = cv2.warpPerspective(data.led, transform_matrix, (data.frame.shape[1], data.frame.shape[0]))
        return cv2.bitwise_and(result, result, mask=data.mask_screen)

    @Timer("Stage3Processor._composite_led_with_frame", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @deprecation.deprecated(details="Has been replaced by guided filter")
    def _composite_led_with_frame(self, data: ImageData) -> ImageData:
        """
        Composite LED with a image frame.

        Args:
            data (ImageData): Input image data.

        Returns:
            np.ndarray: Composite image.
        """
        bg_main = cv2.bitwise_or(data.frame, data.frame, mask=cv2.bitwise_not(data.mask_screen))
        data.final_frame = bg_main + self._fill_led(data)
        return data

    @staticmethod
    @nb.njit()
    def multiply_with_mask_numba(result, mask_patch):
        """
        Element-wise multiplication of `result` with `mask_patch[..., None]`.

        Parameters:
        - result: 2D NumPy array of shape (H, W, C)
        - mask_patch: 2D NumPy array of shape (H, W)

        Returns:
        - 3D NumPy array of shape (H, W, C), result * mask_patch[..., None].
        """
        h, w, c = result.shape
        new_arr = np.empty((result.shape), dtype=np.float32)
        for i in range(h):
            for j in range(w):
                for k in range(c):
                    new_arr[i, j, k] = result[i, j, k] * mask_patch[i, j]

        return new_arr

    @staticmethod
    @Timer("Stage3Processor.fill_led_1_patch", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def fill_led_1_patch(
        frame_patch: np.ndarray, led_patch: np.ndarray, coordinates: np.ndarray, mask_patch: np.ndarray
    ) -> np.ndarray:
        """
        Applies perspective transformation to fill the LED image into a specified region of a frame patch.

        Args:
            frame_patch (np.ndarray): The region of the frame to process, represented as a patch.
            led_patch (np.ndarray): The LED image to be transformed and blended into the patch.
            coordinates (np.ndarray | tuple | list): The 4 corner coordinates (relative to the patch)
                of the region where the LED image will be placed.
            mask_patch (np.ndarray): The mask of the region to process with values in the range [0.0, 1.0].

        Returns:
            np.ndarray: The result of blending the transformed LED patch with the input frame patch,
            weighted by the provided mask.
        """
        h, w = led_patch.shape[:2]
        source_corners = np.array([[0, 0], [0, h], [w, h], [w, 0]], dtype=np.float32)
        target_corners = np.array(coordinates, dtype=np.float32)
        transform_matrix = cv2.getPerspectiveTransform(source_corners, target_corners)
        result = cv2.warpPerspective(led_patch, transform_matrix, (frame_patch.shape[1], frame_patch.shape[0]))
        return Stage3Processor.multiply_with_mask_numba(result, mask_patch)

    @staticmethod
    @nb.njit()
    def compute_bg_patch(frame_patch: np.ndarray, patch_smoothed: np.ndarray):
        """
        Perform the operation: bg_patch = frame_patch * (1 - patch_smoothed[..., None])
        using numba for optimization.

        Parameters:
            frame_patch (np.ndarray): The input frame patch, shape (H, W, C).
            patch_smoothed (np.ndarray): The smoothed patch, shape (H, W).

        Returns:
            np.ndarray: The resulting bg_patch, shape (H, W, C).
        """
        h, w, c = frame_patch.shape
        bg_patch = np.empty((h, w, c), dtype=np.float32)

        for i in range(h):
            for j in range(w):
                for k in range(c):
                    bg_patch[i, j, k] = frame_patch[i, j, k] * (1.0 - patch_smoothed[i, j])

        return bg_patch

    @staticmethod
    @Timer("Stage3Processor.full_pipeline_matting_cpu", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def blending_led_cpu(
        mask_patch: np.ndarray, frame_patch: np.ndarray, led: np.ndarray, composition_coordinates: np.ndarray
    ):
        '''
        Perform CPU-based LED blending with guided filtering.

        Args:
            mask_patch (np.ndarray): The mask patch for matting.
            frame_patch (np.ndarray): The frame patch to process.
            led (np.ndarray): The LED image to blend.
            composition_coordinates (np.ndarray): The coordinates for LED placement in the frame_patch.

        Returns:
            np.ndarray: The final blended output image as a NumPy array.
        '''
        patch_smoothed = np.clip(
            guidedFilter(cv2.GaussianBlur(frame_patch, (3, 3), 0), mask_patch, 5, 8, dDepth=-1, scale=0.8).astype(np.float32)
            / 255.0,
            0,
            1,
        )

        # Background blending for the patch
        bg_patch = Stage3Processor.compute_bg_patch(frame_patch, patch_smoothed)

        # Fill LED in the patch
        led_patch = Stage3Processor.fill_led_1_patch(frame_patch, led, composition_coordinates, patch_smoothed)

        # Composite the processed patch back into the original frame
        return (bg_patch + led_patch).astype(np.uint8)

    @staticmethod
    def fill_led_1_pytorch(led: torch.Tensor, coordinates: torch.Tensor, mask: torch.Tensor) -> np.ndarray:
        h, w = mask.shape[-2:]
        led_resized = F.interpolate(led, size=(h, w), mode="bilinear", align_corners=False)
        led_h, led_w = led_resized.shape[-2:]
        source_corners = [[0, 0], [0, led_h], [led_w, led_h], [led_w, 0]]
        transformed = torchvision.transforms.functional.perspective(
            led_resized, startpoints=source_corners, endpoints=coordinates
        )
        return transformed * mask

    @Timer("Stage3Processor.full_pipeline_matting_gpu", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def blending_led_gpu(
        self, mask_patch: np.ndarray, frame_patch: np.ndarray, led: np.ndarray, composition_coordinates: np.ndarray
    ):
        '''
        Perform GPU-accelerated LED blending with guided filtering.

        Args:
            mask_patch (np.ndarray): The mask patch for matting.
            frame_patch (np.ndarray): The frame patch to process.
            led (np.ndarray): The LED image to blend.
            composition_coordinates (np.ndarray): The coordinates for LED placement in the frame_patch.

        Returns:
            np.ndarray: The final blended output image as a NumPy array.
        '''
        # Create CUDA streams for asynchronous operations
        stream1 = torch.cuda.Stream()
        stream2 = torch.cuda.Stream()
        stream3 = torch.cuda.Stream()
        stream4 = torch.cuda.Stream()

        def async_transfer(data, stream, dtype=torch.float32):
            with torch.cuda.stream(stream):
                return torch.from_numpy(data).to(device=self.device, dtype=dtype, non_blocking=True)

        # Multi-threaded transfer using ThreadPoolExecutor
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future_mask = executor.submit(async_transfer, mask_patch, stream1, torch.float32)
            future_frame = executor.submit(async_transfer, frame_patch, stream2, torch.float32)
            future_led = executor.submit(async_transfer, led, stream3, torch.float32)
            future_coords = executor.submit(async_transfer, composition_coordinates, stream4, torch.float32)

            # Wait for all transfers to complete
            mask_patch = future_mask.result().unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
            frame_patch = future_frame.result().permute(2, 0, 1).unsqueeze(0)  # Channel-first format
            led = future_led.result().permute(2, 0, 1).unsqueeze(0)  # Channel-first format
            coordinates = future_coords.result()

        # Synchronize all CUDA streams
        torch.cuda.synchronize()

        # Start GPU processing
        patch_smoothed = guidedfilter2d_color(self.gauss_filter(frame_patch), mask_patch, 5, 8).clamp_(0.0, 255.0).div_(255.0)
        bg_patch = frame_patch * (1 - patch_smoothed)
        led_patch = Stage3Processor.fill_led_1_pytorch(led, coordinates, patch_smoothed)

        # Combine results and transfer back to CPU
        output = (bg_patch + led_patch)[0].to(torch.uint8).permute(1, 2, 0).cpu().numpy()

        return output

    @Timer("Stage3Processor.composite_led_with_frame_patch", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def composite_led_with_frame_patch(self, data: ImageData) -> np.ndarray:
        """
        Composites an LED image into a specific patch of the input frame using a mask and a set of coordinates.

        Args:
            data (ImageData): An object containing the following attributes:
                - frame (np.ndarray): The full original frame as an image.
                - led (np.ndarray): The full LED image to be composited.
                - mask_screen (np.ndarray): The full mask with values in the range [0.0, 1.0].
                - coordinates_float (np.ndarray): A 4x2 array of float coordinates specifying the region of interest.
                - fitted_coords_dict (dict): A dictionary containing fitted coordinates for corners.
                - frame_id (int): The identifier of the current frame.

        Returns:
            np.ndarray: The composited frame where the LED image has been integrated into the specified region.
        """
        # Extract region of interest
        x1, y1, x2, y2 = ClassifyCorners.extract_bbox(
            np.array([data.fitted_coords_dict[corner_name][data.frame_id] for corner_name in ["A", "B", "C", "D"]])
            .round()
            .astype(int),
            data.frame,
            0,
        )
        x2 = x2 + 1
        y2 = y2 + 1
        # Extract patches
        frame_patch = data.frame[y1:y2, x1:x2, :]
        mask_patch = data.mask_screen[y1:y2, x1:x2]
        patch_coordinates = [
            [data.coordinates_float[0][0] - x1, data.coordinates_float[0][1] - y1],
            [data.coordinates_float[1][0] - x1, data.coordinates_float[1][1] - y1],
            [data.coordinates_float[2][0] - x1, data.coordinates_float[2][1] - y1],
            [data.coordinates_float[3][0] - x1, data.coordinates_float[3][1] - y1],
        ]

        data.frame[y1:y2, x1:x2] = self.led_blending_pipeline(mask_patch, frame_patch, data.led, np.array(patch_coordinates))

        data.final_frame = data.frame

    @Timer("Stage3Processor.composite_led_and_matting", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @deprecation.deprecated(details="Has been replaced by guided filter")
    def composite_led_and_matting(self, data: ImageData) -> np.ndarray:
        self._composite_led_with_frame(data)
        return self._conserve_foreground(data)

    @Timer("Stage3Processor._has_timi_on_screen", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @deprecation.deprecated(details="Has been replaced by guided filter")
    def _has_timi_on_screen(self, alpha: np.ndarray, coordinates: np.ndarray) -> bool:
        """
        Check if TIMI is on screen.

        Args:
            alpha (np.ndarray): Alpha channel.
            coordinates (np.ndarray): Coordinates.

        Returns:
            bool: True if TIMI is on screen, False otherwise.
        """
        alpha_thresh = (alpha > 0.1).astype(np.uint8)
        mask_screen = np.zeros_like(alpha_thresh)
        cv2.fillPoly(mask_screen, pts=[np.array(coordinates)], color=(1, 1, 1))
        intersection = np.bitwise_and(alpha_thresh, mask_screen)
        return np.any(intersection)

    @Timer("Stage3Processor._refine_matte", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @deprecation.deprecated(details="Has been replaced by guided filter")
    def _refine_matte(
        self,
        alpha: np.ndarray,
        frame: np.ndarray,
        alpha_thresh_refine: float = cfg.led.conserve_fg.alpha_thresh_refine,
        alpha_factor_refine: float = cfg.led.conserve_fg.alpha_factor_refine,
    ) -> np.ndarray:
        """
        Refines the alpha matte of an image to enhance foreground conservation.

        This function processes the given alpha matte by applying noise removal and smoothing techniques.
        It then adjusts the alpha values based on specified thresholds and factors to improve the
        quality of the matte, particularly focusing on preserving the foreground.

        Args:
            alpha (np.ndarray): The initial alpha matte of the image.
            frame (np.ndarray): The original frame.
            alpha_thresh_refine (float, optional): Threshold value for refining alpha.
            alpha_factor_refine (float, optional): Factor to adjust alpha values during refinement.

        Returns:
            np.ndarray: The refined alpha matte.
        """
        if alpha.ndim != frame.ndim and alpha.ndim == 2:
            alpha = alpha[:, :, None]  # Expand dims

        noise, _ = MaskScreen.get_screen_mask_and_coordinates((alpha * frame).astype(np.uint8))

        if _ is None:
            return alpha

        noise = MaskScreen.remove_dense_noise(noise, 1)
        noise = MaskScreen.smoothing(noise, 11)
        noise = np.expand_dims(noise, axis=-1)

        alpha_refined = np.where(noise == 255, alpha * alpha_factor_refine, alpha)

        return np.where(alpha_refined < alpha_thresh_refine, alpha_refined * alpha_factor_refine, alpha_refined)

    @Timer("Stage3Processor._conserve_foreground", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @deprecation.deprecated(details="Has been replaced by guided filter")
    def _conserve_foreground(
        self,
        data: ImageData,
        composite_mode: str = cfg.led.composite.mode,
    ) -> np.ndarray:
        """
        Conserve the foreground in the final frame through foreground matting.

        Args:
            data (ImageData): Input image data.
            composite_mode (str, optional): Composite mode. Defaults to cfg.led.composite.mode.

        Returns:
            np.ndarray: Resulting image.
        """
        alpha, frame, final_frame, final_frame_ori = data.matte_timi, data.frame, data.final_frame, data.final_frame.copy()
        try:
            if not self._has_timi_on_screen(alpha, data.coordinates):
                return final_frame

            x1_alpha, y1_alpha, x2_alpha, y2_alpha = MaskScreen.extract_region(alpha)
            alpha, frame, final_frame = (
                alpha[y1_alpha:y2_alpha, x1_alpha:x2_alpha],
                frame[y1_alpha:y2_alpha, x1_alpha:x2_alpha],
                final_frame[y1_alpha:y2_alpha, x1_alpha:x2_alpha],
            )

            if cfg.led.model_matting == "rvm":
                alpha = self._refine_matte(alpha, frame)

            with ThreadPoolExecutor() as executor:
                future_pymatting = executor.submit(composite_with_pymatting, frame, final_frame, alpha, composite_mode)
                future_composite = executor.submit(composite_image, frame, final_frame, alpha)

                result_pymatting = future_pymatting.result()
                result_composite = future_composite.result()

            (x1_screen, y1_screen), (x2_screen, y2_screen) = data.coordinates_ori[0], data.coordinates_ori[2]
            x1_and, y1_and = max(x1_alpha, x1_screen), max(y1_alpha, y1_screen)
            x2_and, y2_and = min(x2_alpha, x2_screen), min(y2_alpha, y2_screen)

            if x1_and > x2_and or y1_and > y2_and:
                logger.warning(
                    "😰 Unable to determine the intersection between foreground (Timi) and screen, returning composite-blending result"
                )
                return final_frame_ori

            x1_0, y1_0 = x1_and - x1_alpha, y1_and - y1_alpha
            x2_0, y2_0 = x2_and - x1_alpha, y2_and - y1_alpha

            area = np.array([[x1_0, y1_0], [x1_0, y2_0], [x2_0, y2_0], [x2_0, y1_0]])

            mask = np.zeros_like(frame, dtype=np.uint8)
            cv2.fillPoly(mask, pts=[area], color=(1, 1, 1))
            result = np.where(mask == 1, result_pymatting, result_composite)
            final_frame_ori[y1_alpha:y2_alpha, x1_alpha:x2_alpha] = result
            return final_frame_ori

        except Exception:
            logger.error(
                f"An error orcured when process Stage3Processor._conserve_foreground function: \n {traceback.format_exc()}"
            )
            return final_frame_ori

    @Timer("Stage3Processor._merge_video_with_audio", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _merge_video_with_audio(self, data: ImageData, trim_mode=media.MergeTrimMode.VIDEO_LENGTH) -> None:
        """
        Merge video with audio.

        Args:
            data (ImageData): Input image data.
            trim_mode (str, optional): Trim mode. Defaults to media.MergeTrimMode.VIDEO_LENGTH.
        """
        led_path = data.input_args.led_path
        audio_path = data.output_paths.save_path_audio
        save_path_only_video = data.output_paths.save_path_only_video
        save_path = data.output_paths.save_path

        if os.path.isfile(audio_path):
            media.merge_video_with_audio(
                video_path=save_path_only_video,
                audio_path=audio_path,
                output_path=save_path,
                check_duration=False,
                trim_mode=trim_mode,
            )

            if media.has_audio(led_path):
                save_path_temp = save_path[:-4] + "_final.mp4"
                media.merge_video_with_audio(
                    video_path=save_path,
                    audio_path=led_path,
                    output_path=save_path_temp,
                    check_duration=False,
                    keep_video_audio=True,
                    trim_mode=trim_mode,
                )
                shutil.move(save_path_temp, save_path)

            helpers.clear_temp_file(save_path_only_video, audio_path)

        elif media.has_audio(led_path):
            media.merge_video_with_audio(
                video_path=save_path_only_video,
                audio_path=led_path,
                output_path=save_path,
                check_duration=False,
                trim_mode=trim_mode,
            )

            helpers.clear_temp_file(save_path_only_video)

        else:
            os.rename(save_path_only_video, save_path)

    @Timer("Stage3Processor._get_matte_foreground", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def _get_matte_foreground(self, data: ImageData) -> None:
        if self.__matting_reader_iter is None:
            self.__matting_reader_iter = FVideoCapture(
                data.output_paths.save_path_matte, output_pixel_format=PixelFormat.BGR24
            ).read()

        ret, matte = next(self.__matting_reader_iter)
        matte = matte[..., 0].astype(np.float32) / 255.0
        data.matte_timi = matte

    @Timer("Stage3Processor.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, data: ImageData, progress: ProgressUpdater) -> None:
        """
        Run the processing pipeline.

        Args:
            data (ImageData): Input image data.
        """
        system_utils.set_cpu_cores()
        vid_cap, mask_cap, total_frame, cap_led = self._pre_process_input(data)
        matting_reader = FVideoCapture(data.output_paths.save_path_matte, output_pixel_format=PixelFormat.BGR24)
        data.reset_frame_id()
        self.calculate_expansion_range(data)

        progress.set_loop(total_frame, cfg.progress.stage3)

        with ThreadPoolExecutor() as executor, alive_bar(
            total=total_frame, theme="musical", length=150
        ) as bar, progress as observer:
            for (video_ret, data.frame), (mask_ret, data.mask_screen), (matting_ret, matte) in zip(
                vid_cap.read(), mask_cap.read(), matting_reader.read()
            ):
                if not all((video_ret, mask_ret, matting_ret)):
                    break
                try:
                    timer_start = time.time()
                    led_ret, data.led = cap_led.read()
                    matte = matte[..., 0].astype(np.float32) / 255.0
                    data.matte_timi = matte
                    self._get_coordinates(data)

                    if self._is_dummy_value(data.coordinates):
                        data.final_frame = data.frame
                    else:
                        self._post_processing_coordinates(data)

                        self._post_processing_mask(data)

                        self.composite_led_with_frame_patch(data)

                        executor.submit(self.validator3.run, data)

                        # Visualize for debug
                        if data.input_args.is_dev:
                            executor.submit(
                                self.video_writer_processor.write,
                                data.video_writers.writer_mask_processed,
                                VideoProcessingMode.CV2,
                                data.mask_screen,
                            )
                            Draw.visualize_point(data.final_frame, data.coordinates)

                    if data.input_args.is_dev:
                        num_cores_used, cpu_used = 0, 0
                        execution_time = time.time() - timer_start
                        executor.submit(
                            save_to_csv,
                            [data.frame_id, num_cores_used, cpu_used, execution_time],
                            data.output_paths.save_path_perf_stage3,
                        )
                        logger_perf.debug(f"Execution time: {execution_time}")

                except KeyboardInterrupt:
                    logger.warning("Stopped!")
                    break

                except Exception:
                    logger.critical(f"[Stage3] Error when process Composite LED: {traceback.format_exc()}")
                    data.final_frame = data.frame

                self.video_writer_processor.write(
                    data.video_writers.writer_output, VideoProcessingMode.FFMPEG, data.final_frame
                )
                data.update_frame_id()
                bar()
                observer()

        # Ensure all threading tasks are completed before the end
        executor.shutdown(wait=True)

        self.video_writer_processor.release(VideoProcessingMode.FFMPEG, data.video_writers.writer_output)
        cap_led.release()
        matting_reader.release()

        if data.input_args.is_dev:
            self.video_writer_processor.release(VideoProcessingMode.CV2, data.video_writers.writer_mask_processed)

        with ThreadPoolExecutor() as executor:
            executor.submit(self.validator3.summary_log, data.input_args)
            executor.submit(self.release, data)
            executor.submit(self._merge_video_with_audio, data)

            if data.input_args.is_dev:
                # Visualize resource usage
                executor.submit(visualize_data, data.output_paths.save_path_perf_stage3)

        # Ensure all threading tasks are completed before the end
        executor.shutdown(wait=True)

        logger.info(f"Video saved in: {data.output_paths.save_path}")

    def release(self, data: ImageData):
        """
        Release resources, including VRAM.
        """
        Coordinates.release()

        # Delete matte npy folder to save storage.
        helpers.remove_path(data.output_paths.save_path_matte_npy)

        if not data.input_args.is_dev:
            helpers.clear_temp_file(
                data.output_paths.save_path_matte,
                data.output_paths.save_path_stage1,
                data.output_paths.save_path_mask_processed,
                data.output_paths.save_path_csv,
                data.output_paths.save_path_mask,
            )
