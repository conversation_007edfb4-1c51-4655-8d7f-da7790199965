from typing import Tuple, Union

import cv2
import torch
import torch.nn.functional as F


def diff_x(input, r):

    cum_src = input.cumsum(dim=-2)
    left = cum_src[..., r : 2 * r + 1, :]
    middle = cum_src[..., 2 * r + 1 :, :] - cum_src[..., : -2 * r - 1, :]
    right = cum_src[..., -1:, :] - cum_src[..., -2 * r - 1 : -r - 1, :]

    output = torch.cat([left, middle, right], dim=-2)
    return output


def diff_y(input, r):

    cum_src = input.cumsum(dim=-1)
    left = cum_src[..., r : 2 * r + 1]
    middle = cum_src[..., 2 * r + 1 :] - cum_src[..., : -2 * r - 1]
    right = cum_src[..., -1:] - cum_src[..., -2 * r - 1 : -r - 1]
    output = torch.cat([left, middle, right], dim=-1)
    return output


def boxfilter2d_cumsum(x, radius):
    '''
    Fast Box Filter (Average filter) implemented from the paper https://fukushima.web.nitech.ac.jp/paper/2017_iwait_nakamura.pdf
    '''
    return diff_y(diff_x(x, radius), radius)


class GaussFilter:
    """
    A Gaussian filter implemented using PyTorch that applies a 2D Gaussian kernel
    to an input tensor with grouped convolution.

    Args:
        kernel_size (Tuple[int, int]): Size of the Gaussian kernel (height, width).
        sigma (float): Standard deviation for the Gaussian kernel.
        device (Union[torch.device, str]): Device on which the filter will operate (e.g., 'cuda' or 'cpu').

    Methods:
        __call__(x: torch.Tensor) -> torch.Tensor:
            Applies the Gaussian filter to the input tensor.
    """

    def __init__(self, kernel_size: Tuple[int, int], sigma: float, device: Union[torch.device, str]):
        self.device = device  # Initialise device before kernel generation
        self.padding = (kernel_size[1] // 2, kernel_size[1] // 2, kernel_size[0] // 2, kernel_size[0] // 2)
        self.kernel = self._init_gaussian_kernel(kernel_size, sigma)

    def __call__(self, x: torch.Tensor) -> torch.Tensor:
        """
        Applies the Gaussian filter to the input tensor.

        Args:
            x (torch.Tensor): Input tensor of shape (batch_size, channels, height, width).

        Returns:
            torch.Tensor: Filtered tensor of the same shape as the input.
        """
        # Apply padding
        x_padded = F.pad(x, self.padding, mode="reflect")
        # Perform grouped convolution
        return F.conv2d(x_padded, self.kernel, groups=3)

    def _init_gaussian_kernel(self, kernel_size: Tuple[int, int], sigma: float) -> torch.Tensor:
        """
        Initializes the 2D Gaussian kernel for the filter.

        Args:
            kernel_size (Tuple[int, int]): Size of the Gaussian kernel (height, width).
            sigma (float): Standard deviation for the Gaussian kernel.

        Returns:
            torch.Tensor: Gaussian kernel formatted for grouped convolution.
        """
        # Create the Gaussian kernel using OpenCV
        gaussian_kernel = cv2.getGaussianKernel(kernel_size[0], sigma).dot(cv2.getGaussianKernel(kernel_size[1], sigma).T)
        # Convert to PyTorch tensor and format for grouped convolution
        kernel = (
            torch.tensor(gaussian_kernel, dtype=torch.float32, device=self.device)
            .unsqueeze(0)
            .unsqueeze(0)  # Add batch and channel dimensions
            .repeat(3, 1, 1, 1)  # Repeat for RGB (3 input channels)
        )
        return kernel


def guidedfilter2d_color(guide: torch.Tensor, src: torch.Tensor, radius: int, eps: float, scale_factor: float = 1.0):
    """guided filter for a color guide image.
       Reference to Aphrodite (CV-Timi)

    Parameters
    -----
    guide: (B, 3, H, W)-dim torch.Tensor
        Guide image. Must have 3 channels (e.g., RGB image).
    src: (B, 1, H, W)-dim torch.Tensor
        Filtering image. Must have either 1 or 3 channels (e.g., binary image, RGB image).
    radius: int
        Filter radius.
    eps: float
        Regularisation coefficient.
    scale_factor: float, optional. Value range [0.0, 1.0].
        Resize guide and src to scale_factor of their original size for faster computation (Fast Guided Filter). Default is 1.0.

    Notes
    -----
    - Both guide and src must have exactly 4 dimensions.
    - The guide tensor must have exactly 3 channels.
    """

    guide_sub = guide.clone()
    src = F.interpolate(src, scale_factor=scale_factor, mode="bilinear", align_corners=None)
    guide = F.interpolate(guide, scale_factor=scale_factor, mode="bilinear", align_corners=None)
    radius = int(radius * scale_factor)

    guide_r, guide_g, guide_b = torch.chunk(guide, 3, 1)  # b x 1 x H x W
    N = boxfilter2d_cumsum(torch.ones_like(guide_r), radius)

    mean_I = boxfilter2d_cumsum(guide, radius) / N  # b x 3 x H x W
    mean_I_r, mean_I_g, mean_I_b = torch.chunk(mean_I, 3, 1)  # b x 1 x H x W

    mean_p = boxfilter2d_cumsum(src, radius) / N  # b x C x H x W

    mean_Ip = boxfilter2d_cumsum(guide * src, radius) / N
    cov_Ip = (mean_Ip - mean_I * mean_p).unsqueeze(2)

    var_I_rg = boxfilter2d_cumsum(guide_r * guide_g, radius) / N - mean_I_r * mean_I_g  # b x 1 x H x W
    var_I_rb = boxfilter2d_cumsum(guide_r * guide_b, radius) / N - mean_I_r * mean_I_b  # b x 1 x H x W
    var_I_gb = boxfilter2d_cumsum(guide_g * guide_b, radius) / N - mean_I_g * mean_I_b  # b x 1 x H x W
    var_I_rr, var_I_gg, var_I_bb = torch.chunk(boxfilter2d_cumsum(guide * guide, radius) / N - mean_I * mean_I + eps, 3, 1)

    # determinant
    cov_det = (
        var_I_rr * (var_I_gg * var_I_bb - var_I_gb**2)
        + var_I_rg * (2 * var_I_gb * var_I_rb - var_I_rg * var_I_bb)
        - var_I_rb * var_I_gg * var_I_rb
    )

    # inverse
    inv_var_I_rr = var_I_gg * var_I_bb - var_I_gb * var_I_gb  # b x 1 x H x W
    inv_var_I_rg = -(var_I_rg * var_I_bb - var_I_rb * var_I_gb)  # b x 1 x H x W
    inv_var_I_rb = var_I_rg * var_I_gb - var_I_rb * var_I_gg  # b x 1 x H x W
    inv_var_I_gg = var_I_rr * var_I_bb - var_I_rb * var_I_rb  # b x 1 x H x W
    inv_var_I_gb = -(var_I_rr * var_I_gb - var_I_rb * var_I_rg)  # b x 1 x H x W
    inv_var_I_bb = var_I_rr * var_I_gg - var_I_rg * var_I_rg  # b x 1 x H x W

    inv_sigma = (
        torch.stack(
            [
                torch.stack([inv_var_I_rr, inv_var_I_rg, inv_var_I_rb], 1),
                torch.stack([inv_var_I_rg, inv_var_I_gg, inv_var_I_gb], 1),
                torch.stack([inv_var_I_rb, inv_var_I_gb, inv_var_I_bb], 1),
            ],
            1,
        ).squeeze(-3)
        / cov_det
    )

    a = (cov_Ip * inv_sigma).sum(1, keepdim=True).squeeze(1)
    b = mean_p - (a * mean_I).sum(1, keepdim=True)  # b x C x H x W

    mean_a = boxfilter2d_cumsum(a, radius) / N
    mean_b = boxfilter2d_cumsum(b, radius) / N

    guide = guide_sub
    mean_a = F.interpolate(mean_a, guide.shape[-2:], mode="bilinear", align_corners=None)
    mean_b = F.interpolate(mean_b, guide.shape[-2:], mode="bilinear", align_corners=None)

    q = (mean_a * guide).sum(dim=1, keepdim=True) + mean_b
    return q
