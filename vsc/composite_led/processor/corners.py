import gc
import itertools
import os
import shutil
from typing import Union

import cv2
import numpy as np
from codetiming import Timer
from filelock import FileLock

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.datastruct import ImageData
from vsc.composite_led.detector import YOLOv8ONNX, multiclass_nms
from vsc.composite_led.functions import (
    Coordinates,
    MaskScreen,
    MatteForeground,
    PostProcessing,
)
from vsc.composite_led.functions.helpers import init_model_with_lock
from vsc.composite_led.processor.corner_classification import ClassifyCorners
from vsc.composite_led.utilizer import Draw
from vsc.utils import cfg
from vsc.utils.exception import handle_runtime_error
from vsc.utils.logger import logger, logger_perf


class CornersValidator(BaseProcessor):
    def __init__(self, device: str = "cuda:0", weights_folder: str = "checkpoints/"):
        self.device = device
        self.weights_folder = weights_folder

        # Ensure the base weights folder exists.
        os.makedirs(weights_folder, exist_ok=True)

        # Initialise each model component independently.
        self._init_corner_detection()
        self._init_corner_classification()

        # Set the validation function based on configuration.
        self.validate_corner_function = (
            self.validate_corner_based_on_model_detection
            if cfg.led.corner_validation.verification_model == "detection"
            else self.validate_corner_based_on_model_classification
        )

    @handle_runtime_error
    def _init_corner_detection(self):
        cd_folder = os.path.join(self.weights_folder, "CornerDetection")
        os.makedirs(cd_folder, exist_ok=True)

        # Create an init callable for corner detection.
        def init_detection():
            return YOLOv8ONNX(
                checkpoint_path=os.path.join(cd_folder, cfg.led.detector.checkpoint_path),
                device=self.device,
            )

        self.detect_corners_model = init_model_with_lock(
            model_folder=cd_folder,
            init_func=init_detection,
        )

    @handle_runtime_error
    def _init_corner_classification(self):
        cc_folder = os.path.join(self.weights_folder, "CornerClassification")
        os.makedirs(cc_folder, exist_ok=True)

        # Create an init callable for corner classification.
        def init_classification():
            return ClassifyCorners(
                checkpoint_path=os.path.join(cc_folder, cfg.led.corner_classification.checkpoint_path),
                device=self.device,
            )

        self.corner_classifier = init_model_with_lock(
            model_folder=cc_folder,
            init_func=init_classification,
        )

    def is_inside_bbox(self, bbox: list, point: list) -> bool:
        """
        Check if the given point lies inside the bbox.

        Args:
            bbox (list): List representing the rectangle in the form [x1, y1, x2, y2].
            point (list): List representing the point in the form [x, y].

        Returns:
            bool: True if the point lies inside the rectangle, False otherwise.
        """
        x1, y1, x2, y2 = bbox
        x, y = point
        return x1 <= x <= x2 and y1 <= y <= y2

    @Timer("CornersValidator.get_points_inside_bbox", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def get_points_inside_bbox(self, points: Union[list, np.ndarray], bbox: list, frame: np.ndarray) -> list:
        """
        Get points inside the given list of bbox.

        Args:
            points (list or np.ndarray): List of points.
            bbox (list): List of bbox, each represented as [x1, y1, x2, y2].
            frame (np.ndarray): frame.

        Returns:
            list: List of points that lie inside the rectangles.
        """
        points_inside = []
        _, w = frame.shape[:2]

        for point in points:
            for bb in bbox:
                if self.is_inside_bbox(bb, point) or point[0] in [0, 1, w - 1]:
                    points_inside.append(point)
                    break

        return points_inside

    @Timer("CornersValidator.detect_cornes", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def detect_cornes(
        self, frame_ori: np.ndarray, frame: np.ndarray, mask: np.ndarray, is_dev: bool, expand_factor: float = 0.2
    ):
        bboxes_full_frame = self.detect_corners_model.run(frame_ori)
        if bboxes_full_frame is None:
            bboxes_full_frame = []

        # Crop screen before forward to model to improve accuracy
        x1, y1, x2, y2 = self.get_bbox_largest_mask(mask, expand_factor)

        frame_crop = frame_ori[y1:y2, x1:x2]

        bboxes_crop = self.detect_corners_model.run(frame_crop)

        if bboxes_crop is None:
            bboxes_crop = []

        else:
            bboxes_crop[:, 1] += y1
            bboxes_crop[:, 0] += x1
            bboxes_crop[:, 2] += x1
            bboxes_crop[:, 3] += y1

        if len(bboxes_crop) > 0 and len(bboxes_full_frame) > 0:
            bboxes = np.concatenate((bboxes_crop, bboxes_full_frame))

            # Remove overlap bbox
            bb = bboxes[:, :4]
            class_idx = bboxes[:, 4]
            confidence_score = bboxes[:, 5]
            indicate = multiclass_nms(bb, class_idx, confidence_score, cfg.led.detector.iou_threshold)

            bb_mns = bb[indicate]
            class_idx_nms = class_idx[indicate]
            confidence_score_nms = confidence_score[indicate]
            bboxes = np.concatenate((bb_mns, class_idx_nms[:, np.newaxis], confidence_score_nms[:, np.newaxis]), axis=1)

        elif len(bboxes_crop) > 0 and len(bboxes_full_frame) == 0:
            bboxes = bboxes_crop
        else:
            bboxes = bboxes_full_frame

        if len(bboxes) == 0:
            return

        corner_bboxes = Coordinates.get_bbox(bboxes, "corners", self.detect_corners_model.classes)

        # Visualize for debug
        if is_dev:
            Draw.visualize_bbox(frame, corner_bboxes)

        return np.int32(np.array(corner_bboxes)[:, :4]).tolist()

    @Timer("CornersValidator.remove_point_on_foreground", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @staticmethod
    def remove_point_on_foreground(coordinates: dict, alpha: np.ndarray) -> dict:

        alpha = MatteForeground.remove_sparse_noise_matte(alpha)
        alpha = MaskScreen.dilate(alpha, crop_mode=True)

        for corner_name in ["A", "B", "C", "D"]:
            corner_coordinate = coordinates[corner_name]
            if corner_coordinate is not None:
                if alpha[corner_coordinate[1], corner_coordinate[0]] > 0:
                    coordinates[corner_name] = None
        return coordinates

    def remove_noise(
        self, coordinates: Union[list, tuple, np.ndarray], distance_thresh: float = cfg.led.post_processing.distance_noise
    ) -> list:
        """
        Remove noise based on the distance threshold from the given coordinates.

        Args:
            coordinates (np.ndarray): Numpy array representing the coordinates.
            distance_thresh (int, optional): Distance threshold. Default is 100.

        Returns:
            list: List numpy array representing the coordinates after removing noise.
        """
        result = [list(x) for x in coordinates]
        to_remove = set()

        # Mark pairs for removal based on the distance threshold
        for p1, p2 in itertools.combinations(result, 2):
            if Coordinates.cal_distance(p1, p2) < distance_thresh:
                to_remove.add(tuple(p1))
                to_remove.add(tuple(p2))

        # Return a new list excluding the points marked for removal
        filtered_result = [np.array(p) for p in result if tuple(p) not in to_remove]
        return filtered_result

    @staticmethod
    def validate_coordinates_based_on_distances(
        coordinates: Union[list, tuple, np.ndarray],
        distance_thresh: float = cfg.led.post_processing.green_screen_dimension_threshold,
    ) -> list:
        """
        Check if coordinates are valid or not by comparing maximum distance and DISTANCE_THRESH

        Args:
            coordinates (np.ndarray): Numpy array representing the coordinates.
            distance_thresh (int, optional): Distance threshold. Default is 100.

        Returns:
            list: List numpy array representing the coordinates after removing noise.
        """
        if not coordinates:
            return coordinates

        p1 = coordinates[0]
        for p2 in coordinates[1:]:
            distance = Coordinates.cal_distance(p1, p2)
            if distance >= distance_thresh:
                return coordinates

        return []

    @staticmethod
    def find_coords_in_quadrant(raw_coordinates: Union[tuple, list, np.ndarray], frame_ori: np.ndarray) -> dict:
        box_coords = ClassifyCorners.extract_bbox(raw_coordinates, frame_ori, 20, clamp_option=False)
        x1, y1, x2, y2 = box_coords
        anchor_offset = 10000
        anchors = np.array(
            [
                [x1 - anchor_offset, y1 - anchor_offset],
                [x1 - anchor_offset, y2 + anchor_offset],
                [x2 + anchor_offset, y2 + anchor_offset],
                [x2 + anchor_offset, y1 - anchor_offset],
            ]
        )
        contour_coords = raw_coordinates[np.linalg.norm(raw_coordinates[:, np.newaxis, :] - anchors, axis=2).argmin(0)]
        coordinates_in_quadrant = {corner_name: contour_coords[idx] for idx, corner_name in enumerate(["A", "B", "C", "D"])}
        return coordinates_in_quadrant

    @Timer("CornersValidator.get_bbox_largest_mask", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def get_bbox_largest_mask(self, mask: np.ndarray, expansion_factor: float = 0.1) -> tuple:
        largest_mask = MaskScreen.get_largest_mask(mask)

        x1, _, x2, _ = MaskScreen.extract_region(largest_mask, 0)
        return MaskScreen.extract_region(largest_mask, int(expansion_factor * (x2 - x1)))

    @Timer(
        "CornersValidator.validate_corner_based_on_model_detection", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug
    )
    def validate_corner_based_on_model_detection(
        self, data: ImageData, coordinates_in_quadrant: list, coordinates: list, frame_ori: np.asarray
    ) -> list:
        unique_coordinates = set()
        for arr in coordinates_in_quadrant:
            unique_coordinates.add(tuple(arr.flatten()))

        corner_bboxes = self.detect_cornes(frame_ori, data.frame, data.mask_screen, data.input_args.is_dev)

        if corner_bboxes is not None:
            coordinates = self.get_points_inside_bbox(coordinates, corner_bboxes, data.frame)
            for arr in coordinates:
                unique_coordinates.add(tuple(arr.flatten()))

        return [np.array(c) for c in list(unique_coordinates)]

    @Timer(
        "CornersValidator.validate_corner_based_on_model_classification",
        "{name}: {milliseconds:.2f} ms",
        logger=logger_perf.debug,
    )
    def validate_corner_based_on_model_classification(
        self, data: ImageData, coordinates_in_quadrant: dict, contour_coords: list, frame_ori: np.ndarray
    ):
        '''
        Validate corners of green screen using classification model

        Args:
            data (ImageData): Used to extract frame and is_dev arguments.
            coordinates_in_quadrant (list): Coordinates extracted from 4 segments of the green screen.
            coords (list): Coordinates extracted from green screen mask.
            frame_ori (np.ndarray): 2D original frame.
            operation (str) ["or", "and", None]: Config for combination method between coordinates coordinates_validated_by_model and coordinates_in_quadrant.
                                          "Or": Combine 2 results from model and quadrant. "And": Only take the shared results from model and quadrant.
                                          None: Only take result from model.
        Returns:
            coordinates_final (list): Output coordinates.
        '''
        coordinates_after_validation = self.corner_classifier.run(
            contour_coords, coordinates_in_quadrant, frame_ori, data.frame, data.input_args.is_dev
        )
        coordinates_after_validation = [
            coordinate for coordinate in list(coordinates_after_validation.values()) if coordinate is not None
        ]
        return coordinates_after_validation

    @staticmethod
    def check_if_timi_matte_larger_than_threshold(
        matte_timi: np.ndarray, threshold: float = cfg.led.matte.matte_appearance_threshold
    ):
        timi_mass_ratio = MaskScreen.calculate_object_area_to_frame(matte_timi)
        timi_bigger_than_threshold_flag = timi_mass_ratio > threshold
        return timi_bigger_than_threshold_flag

    @Timer("CornersValidator.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(self, data: ImageData) -> ImageData:
        if not hasattr(self, "detect_corners_model") or not hasattr(self, "corner_classifier"):
            self.__init_model()

        frame_ori = data.frame.copy()
        coordinates = data.coordinates_based_segment

        if data.input_args.is_dev:
            Draw.visualize_point(data.frame, coordinates, text="", offset_x=-50, offset_y=-20)
        coordinates_in_quadrant: dict = CornersValidator.find_coords_in_quadrant(coordinates, frame_ori)
        timi_bigger_than_threshold_flag = CornersValidator.check_if_timi_matte_larger_than_threshold(data.matte_timi)

        if timi_bigger_than_threshold_flag:
            coordinates_after_timi_matting: dict = CornersValidator.remove_point_on_foreground(
                coordinates_in_quadrant, data.matte_timi
            )
            coordinates_after_timi_matting_in_list = [
                coordinate_after_timi_matting
                for coordinate_after_timi_matting in list(coordinates_after_timi_matting.values())
                if coordinate_after_timi_matting is not None
            ]
            if len(coordinates_after_timi_matting_in_list) == 4:
                unusual_shape = PostProcessing.validate_coordinates(
                    coordinates_after_timi_matting_in_list,
                    (data.frame.shape[1], data.frame.shape[0]),
                    cfg.led.post_processing.validation_threshold,
                    warning=False,
                )
                if not unusual_shape:
                    data.coordinates = coordinates_after_timi_matting_in_list
                else:
                    data.coordinates = self.validate_corner_function(
                        data, coordinates_after_timi_matting, data.coordinates_based_segment, frame_ori
                    )
            elif len(coordinates_after_timi_matting_in_list) == 3:
                data.coordinates = coordinates_after_timi_matting_in_list
            else:
                data.coordinates = []

        else:
            coordinates_validated_by_model = self.validate_corner_function(
                data, coordinates_in_quadrant, data.coordinates_based_segment, frame_ori
            )
            if len(coordinates_validated_by_model) in [3, 4]:
                data.coordinates = coordinates_validated_by_model
            else:
                data.coordinates = []
        data.coordinates = CornersValidator.validate_coordinates_based_on_distances(data.coordinates)
        return data

    @staticmethod
    def find_coordinates_and_expansion_in_an_image(frame: np.ndarray, matte_timi: np.ndarray):

        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        raw_mask, contour_coords = MaskScreen.get_screen_mask_and_coordinates(frame_rgb)
        coordinates_based_segment_low_epsilon = MaskScreen.get_coordinates(
            raw_mask, epsilon=cfg.led.mask.low_epsilon, old_method=True
        )

        coordinates_in_quadrant: dict = CornersValidator.find_coords_in_quadrant(contour_coords, frame)
        timi_bigger_than_threshold_flag = CornersValidator.check_if_timi_matte_larger_than_threshold(matte_timi)

        if timi_bigger_than_threshold_flag:
            coordinates_after_timi_matting: dict = CornersValidator.remove_point_on_foreground(
                coordinates_in_quadrant, matte_timi
            )
            coordinates_after_timi_matting_in_list = [
                coordinate_after_timi_matting
                for coordinate_after_timi_matting in list(coordinates_after_timi_matting.values())
                if coordinate_after_timi_matting is not None
            ]

            coordinates = coordinates_after_timi_matting_in_list

        else:
            coordinates = [coord for coord in coordinates_in_quadrant.values()]

        coordinates = CornersValidator.validate_coordinates_based_on_distances(coordinates)

        if len(coordinates) == 3:
            coordinates, corner_missing = Coordinates.get_coordinates(
                frame,
                raw_mask,
                coordinates_based_segment_low_epsilon,
                coordinates,
                False,
            )

        if len(coordinates) == 4:
            coordinates = Coordinates.handle_outliers(coordinates)
            _, expansion_width, expansion_height = Coordinates.find_expansion_ranges(coordinates, contour_coords)

        output = {
            "coordinates": [coord.tolist() for coord in coordinates],
            "expansion_width_range": expansion_width,
            "expansion_height_range": expansion_height,
        }
        return output

    def release(self):
        del self.detect_corners_model, self.corner_classifier
        gc.collect()
