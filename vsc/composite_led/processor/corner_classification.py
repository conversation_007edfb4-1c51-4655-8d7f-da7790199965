import os
from typing import List

import cv2
import deprecation
import numpy as np
import onnxruntime as ort
from fvutils.minio import download

from vsc.composite_led.base import BaseProcessor
from vsc.composite_led.datastruct import ImageData
from vsc.composite_led.functions.coordinates import Coordinates
from vsc.composite_led.functions.helpers import parse_device
from vsc.composite_led.utilizer import Draw
from vsc.utils.config import cfg
from vsc.utils.exception import ONNXRuntimeError
from vsc.utils.logger import logger
from vsc.utils.package_helpers import create_temp_package_path


class ClassifyCorners(BaseProcessor):
    def __init__(
        self,
        checkpoint_path: str = cfg.led.corner_classification.checkpoint_path,
        checkpoint_id: str = cfg.led.corner_classification.checkpoint_id,
        device: str = "cuda:0",
    ):

        checkpoint_path = create_temp_package_path(checkpoint_path)
        if not os.path.exists(checkpoint_path):
            checkpoint_path = download(checkpoint_id, os.path.dirname(checkpoint_path))

        self.device_type, self.device_id = parse_device(device)
        provider = (
            [("CUDAExecutionProvider", {"device_id": self.device_id})]
            if self.device_type == "cuda"
            else ["CPUExecutionProvider"]
        )

        # Init the corner classification model
        try:
            self.model = ort.InferenceSession(checkpoint_path, providers=provider)
        except RuntimeError as e:
            raise ONNXRuntimeError(f"Failed to load ONNX model on {device} \nError: {str(e)}") from e

        self.input_name = self.model.get_inputs()[0].name
        self.output_name = self.model.get_outputs()[0].name
        self.input_size = tuple(self.model.get_inputs()[0].shape[-2:])

    @staticmethod
    def extract_bbox(
        coords: np.ndarray,
        frame: np.ndarray,
        stretch: int = cfg.led.corner_classification.coordinate_stretch,
        clamp_option: bool = True,
    ) -> tuple:
        '''
        This function will return the bounding box of the mask screen
        '''
        if clamp_option:
            x1 = np.clip(min(coords[:, 0]) - stretch, 0, frame.shape[1] - 1)
            y1 = np.clip(min(coords[:, 1]) - stretch, 0, frame.shape[0] - 1)

            x2 = np.clip(max(coords[:, 0]) + stretch, 0, frame.shape[1] - 1)
            y2 = np.clip(max(coords[:, 1]) + stretch, 0, frame.shape[0] - 1)
        else:
            x1 = min(coords[:, 0]) - stretch
            y1 = min(coords[:, 1]) - stretch

            x2 = max(coords[:, 0]) + stretch
            y2 = max(coords[:, 1]) + stretch

        return (x1, y1, x2, y2)

    @staticmethod
    @deprecation.deprecated(details="Has been replaced by new_arrange_corners")
    def arrange_corners(
        bbox_coords: list, frame: np.ndarray, patch_ratio: float = cfg.led.corner_classification.patch_ratio
    ) -> dict:
        '''
        Return the dictionary that contains bounding box coordinates and the standardised region
        '''
        x1, y1, x2, y2 = bbox_coords
        patch_width, patch_height = int((x2 - x1) * patch_ratio), int((y2 - y1) * patch_ratio)

        # Extract all the regions
        top_left_region = frame[y1 : y1 + patch_height, x1 : x1 + patch_width, :]
        bot_left_region = frame[y2 - patch_height : y2, x1 : x1 + patch_width, :]
        bot_right_region = frame[y2 - patch_height : y2, x2 - patch_width : x2]
        top_right_region = frame[y1 : y1 + patch_height, x2 - patch_width : x2]

        top_left_bbox_coord = (x1, y1, x1 + patch_width, y1 + patch_height)
        bot_left_bbox_coord = (x1, y2 - patch_height, x1 + patch_width, y2)
        bot_right_bbox_coord = (x2 - patch_width, y2 - patch_height, x2, y2)
        top_right_bbox_coord = (x2 - patch_width, y1, x2, y1 + patch_height)

        # Standardisation
        standardised_top_left_region = top_left_region[::-1, :, :]
        standardised_bot_left_region = bot_left_region
        standardised_bot_right_region = bot_right_region[:, ::-1, :]
        standardised_top_right_region = top_right_region[::-1, ::-1, :]

        # Save in a dictionary
        corner_info = {
            "A": {"image": standardised_top_left_region, "bbox_coords": top_left_bbox_coord},
            "B": {"image": standardised_bot_left_region, "bbox_coords": bot_left_bbox_coord},
            "C": {"image": standardised_bot_right_region, "bbox_coords": bot_right_bbox_coord},
            "D": {"image": standardised_top_right_region, "bbox_coords": top_right_bbox_coord},
        }
        return corner_info

    @staticmethod
    def new_arrange_corners(
        bbox_coords: tuple,
        coordinates_in_quadrant: dict,
        frame: np.ndarray,
        patch_ratio: float = cfg.led.corner_classification.patch_ratio,
    ) -> dict:
        '''
        Return the dictionary that contains bounding box coordinates and the standardised region
        '''
        corner_info = {
            "A": {"image": None, "bbox_coords": None},
            "B": {"image": None, "bbox_coords": None},
            "C": {"image": None, "bbox_coords": None},
            "D": {"image": None, "bbox_coords": None},
        }
        (x1, y1, x2, y2) = bbox_coords
        patch_width, patch_height = int((x2 - x1) * patch_ratio), int((y2 - y1) * patch_ratio)

        for corner_name in ["A", "B", "C", "D"]:
            if coordinates_in_quadrant[corner_name] is not None:
                if corner_name == "A":
                    A_x1, A_y1 = x1, y1
                    A_x2, A_y2 = coordinates_in_quadrant["A"][0] + patch_width, coordinates_in_quadrant["A"][1] + patch_height
                    A_region = frame[A_y1:A_y2, A_x1:A_x2][::-1, :, :]
                    corner_info["A"]["image"] = A_region
                    corner_info["A"]["bbox_coords"] = (A_x1, A_y1, A_x2, A_y2)

                elif corner_name == "B":
                    B_x1, B_y1 = x1, coordinates_in_quadrant["B"][1] - patch_height
                    B_x2, B_y2 = coordinates_in_quadrant["B"][0] + patch_width, y2
                    B_region = frame[B_y1:B_y2, B_x1:B_x2, :]
                    corner_info["B"]["image"] = B_region
                    corner_info["B"]["bbox_coords"] = (B_x1, B_y1, B_x2, B_y2)

                elif corner_name == "C":
                    C_x1, C_y1 = coordinates_in_quadrant["C"][0] - patch_width, coordinates_in_quadrant["C"][1] - patch_height
                    C_x2, C_y2 = x2, y2
                    C_region = frame[C_y1:C_y2, C_x1:C_x2, :][:, ::-1, :]
                    corner_info["C"]["image"] = C_region
                    corner_info["C"]["bbox_coords"] = (C_x1, C_y1, C_x2, C_y2)

                elif corner_name == "D":
                    D_x1, D_y1 = coordinates_in_quadrant["D"][0] - patch_width, y1
                    D_x2, D_y2 = x2, coordinates_in_quadrant["D"][1] + patch_height
                    D_region = frame[D_y1:D_y2, D_x1:D_x2, :][::-1, ::-1, :]
                    corner_info["D"]["image"] = D_region
                    corner_info["D"]["bbox_coords"] = (D_x1, D_y1, D_x2, D_y2)

        return corner_info

    @staticmethod
    def check_a_point_inside_a_bbox(point: tuple, bbox_coord: tuple) -> bool:
        '''
        Return the result after checking a point inside a bounding box
        '''
        x, y = point
        x1, y1, x2, y2 = bbox_coord
        horizontal_condition = (x >= x1) and (x <= x2)
        vertical_condition = (y >= y1) and (y <= y2)
        return True if horizontal_condition and vertical_condition else False

    @staticmethod
    def sort_coordinates(coords: np.ndarray, corner_info: dict) -> dict:
        '''
        Put all the points in each corresponding corner
        '''
        coordinate_dict = {"A": [], "B": [], "C": [], "D": []}
        for coord in coords:
            for corner_name in ["A", "B", "C", "D"]:
                if ClassifyCorners.check_a_point_inside_a_bbox(coord, corner_info[corner_name]["bbox_coords"]):
                    coordinate_dict[corner_name].append(coord)
        return coordinate_dict

    @staticmethod
    def preprocess_image_to_bhwc_numpy(image: np.ndarray, target_shape: tuple) -> np.ndarray:
        '''
        Convert the original read from cv2 to format [1, C, H, W] for inference with onnx model
        '''
        return cv2.resize(image, target_shape).transpose(2, 0, 1)[None, ...].astype("float32") / 255.0

    @staticmethod
    def sigmoid(z):
        return 1 / (1 + np.exp(-z))

    def validating_corner(self, validating_corner_region: np.ndarray) -> np.ndarray:
        '''
        Return the prediction of the validated corner
        0: Correct corner, 1: Incorrect corner
        '''
        validating_corner_region = ClassifyCorners.preprocess_image_to_bhwc_numpy(validating_corner_region, self.input_size)
        prediction = self.model.run([], {self.input_name: validating_corner_region})[0]
        conf_score = ClassifyCorners.sigmoid(prediction.max(-1)[0])
        prediction = prediction.argmax(-1).astype("bool")
        return np.bitwise_not(prediction), conf_score  # Since 0: correct corner, 1: Incorrect corner

    def validate_all_corners(
        self, corner_info: dict, coordinates_in_quadrant: dict, visualisation_frame: np.ndarray, is_dev: bool = False
    ) -> dict:
        for corner_name in ["A", "B", "C", "D"]:
            validity, conf_score = self.validating_corner(corner_info[corner_name]["image"])

            if not validity:
                coordinates_in_quadrant[corner_name] = None
            if is_dev:
                Draw.visualize_bbox(visualisation_frame, [corner_info[corner_name]["bbox_coords"] + (validity[0], conf_score)])

        return coordinates_in_quadrant

    def run(
        self,
        contour_coords: list,
        coordinates_in_quadrant: dict,
        frame_ori: np.ndarray,
        visualisation_frame: np.ndarray,
        is_dev: bool = False,
    ) -> List[np.ndarray]:
        bbox_coords = ClassifyCorners.extract_bbox(np.array(contour_coords), frame_ori)
        corner_info = ClassifyCorners.new_arrange_corners(bbox_coords, coordinates_in_quadrant, frame_ori)
        coordinates_after_validated = self.validate_all_corners(
            corner_info, coordinates_in_quadrant, visualisation_frame, is_dev
        )
        return coordinates_after_validated
