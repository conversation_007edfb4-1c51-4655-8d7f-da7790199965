from abc import abstractmethod

import cv2
import numpy as np


class BaseProcessor:
    @abstractmethod
    def run(self, *args, **kwargs):
        """Abstract method that should implement processing functionality"""

    @abstractmethod
    def release(self, *args, **kwargs):
        """Abstract method that should implement to release resources or perform cleanup after tensor processing."""


class BaseVideoReader:
    @abstractmethod
    def read(self, video_path: str):
        pass

    @abstractmethod
    def video_properties(self, cap):
        pass

    @abstractmethod
    def get_properties(self, cap, *args):
        pass


class BaseVideoWriter:
    def write(self, writer, img):
        img = self.pre_process(img)
        return writer.write(img)

    @abstractmethod
    def create_writer(self, writer_path):
        pass

    def pre_process(self, img: np.ndarray):
        if img.max() == 1:
            img *= 255
        if img.dtype != 'uint8':
            img = cv2.convertScaleAbs(img)
        if len(img.shape) == 2:
            img = np.stack([img] * 3, axis=-1)
        if len(img.shape) == 3 and img.shape[2] == 1:
            img = np.repeat(img[:, :, np.newaxis], 3, axis=2)
        return img

    def release(self, *args):
        for writer in args:
            writer.release()
