import ast
import csv
import os
import re
import shutil
from typing import Any, Callable, Tuple, Union

import cv2
import numpy as np
import pandas as pd
from codetiming import Timer
from filelock import FileLock
from fvutils.videoio import FVideoWriter, PixelFormat

from vsc.utils import cfg
from vsc.utils.exception import ModelInitialisationError
from vsc.utils.logger import logger, logger_perf
from vsc.utils.system_utils import cuda_is_available


@Timer("helpers.save_coordinates_to_csv", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
def save_coordinates_to_csv(
    frame_id: int,
    coordinates: list,
    coordinates_based_segment: list,
    coordinates_based_segment_low_epsilon: list,
    corner_missing: str,
    hidden_name: str,
    save_path: str = "coordinates.csv",
):
    """
    Save the coordinates to a CSV file.

    Args:
        frame_id (int): Identifier of the frame.
        coordinates (List): List of coordinates.
        corner_missing (str): Name of corner missing ("A", "B", "C", "D", None).
        save_path (str, optional): Path to the CSV file. Defaults to "coordinates.csv".

    Raises:
        AssertionError: If the save path does not end with '.csv'.

    Returns:
        None
    """
    assert save_path.endswith("csv"), "Save path must end with '.csv' but given '{save_path}'"
    assert corner_missing in [
        "A",
        "B",
        "C",
        "D",
        None,
    ], f"Corner missing must be in ['A', 'B', 'C', 'D', None] but give '{corner_missing}'"
    assert hidden_name in ["left", "right", None], f"Hidden name must be in ['left', 'right', None] but give '{hidden_name}'"
    if frame_id == 0 and os.path.isfile(save_path):
        os.remove(save_path)

    dir_name = os.path.dirname(save_path)
    if dir_name != '':
        os.makedirs(dir_name, exist_ok=True)

    coordinates = [value.tolist() for value in coordinates]
    coordinates.insert(0, frame_id)
    coordinates.append(corner_missing)
    coordinates.append(hidden_name)
    if coordinates_based_segment_low_epsilon is not None:
        coordinates_based_segment_low_epsilon = [coord.tolist() for coord in coordinates_based_segment_low_epsilon]
    coordinates.append(coordinates_based_segment_low_epsilon)

    if coordinates_based_segment is not None:
        coordinates_based_segment = coordinates_based_segment.tolist()
    coordinates.append(coordinates_based_segment)
    header = [
        "frame_id",
        "A",
        "B",
        "C",
        "D",
        "corner_missing",
        "hidden_name",
        "coordinates_low_epsilon",
        "coordinates_based_segment",
    ]
    coordinates_dict = {}
    for h, coord in zip(header, coordinates):
        coordinates_dict.update({h: coord})

    with open(save_path, "a", newline="") as csvfile:
        if os.stat(save_path).st_size == 0:
            writer = csv.DictWriter(csvfile, fieldnames=header)
            writer.writeheader()

        writer = csv.DictWriter(csvfile, fieldnames=header)
        # Write the data rows
        writer.writerow(coordinates_dict)


def clear_temp_file(*args):
    """
    Remove specified temporary files.

    Args:
        *args (str): Variable number of file paths to be removed.

    Returns:
        None

    Note:
        This function iterates through the provided file paths and removes each file
        if it exists. If a file does not exist or is not accessible, it is silently
        ignored. Use this function to clean up temporary files when they are no longer
        needed.

    Example:
        ```python
        clear_temp_file("temp_file1.txt", "temp_file2.txt")
        ```

        This will attempt to remove the specified temporary files.
    """
    for file in args:
        if os.path.isfile(file):
            os.remove(file)
        else:
            logger.info(f"Don't remove '{file}' because it does not exist.")


def create_writer(
    save_path: str,
    cap: cv2.VideoCapture,
    width: int = None,
    height: int = None,
    fps: float = None,
):
    """
    Create a video writer object.

    Args:
        save_path (str): Path to save the video.
        cap (cv2.VideoCapture): OpenCV VideoCapture object.
        width (int, optional): Width of the video. Defaults to None.
        height (int, optional): Height of the video. Defaults to None.
        fps (float, optional): Frames per second of the video. Defaults to None.

    Returns:
        cv2.VideoWriter: VideoWriter object.
    """
    width = int(cap.get(3)) if width is None else width
    height = int(cap.get(4)) if height is None else height
    fps = cap.get(cv2.CAP_PROP_FPS) if fps is None else fps
    size = (width, height)
    codec = cv2.VideoWriter_fourcc("m", "p", "4", "v")
    return cv2.VideoWriter(save_path, codec, fps, size)


def create_writer_ffmpeg(
    save_path: str,
    video_properties: dict,
    width: int = None,
    height: int = None,
    fps: float = None,
):
    """
    Creates an FVideoWriter instance with the specified parameters.

    Args:
        save_path (str): The path to save the output video file.
        video_properties (dict): A dictionary containing properties of the input video.
        width (int, optional): The width of the output video. If not provided, the width
            from video_properties["width"] is used.
        height (int, optional): The height of the output video. If not provided, the height
            from video_properties["height"] is used.
        fps (float, optional): Frames per second of the output video. If not provided,
            the average frame rate from video_properties["avg_frame_rate"] is used.

    Returns:
        FVideoWriter: An instance of FVideoWriter configured with the specified parameters.

    Raises:
        ValueError: If any of the required video properties is missing in video_properties.

    Note:
        The function uses the FVideoWriter class and requires the PyAv library.
        Make sure to install the library before using this function.

    Example:
        video_props = {
            "width": 1920,
            "height": 1080,
            "avg_frame_rate": 30,
            "r_frame_rate": 30,
            "bit_rate": 5000000,
        }
        writer = create_writer_ffmpeg("output.mp4", video_props, width=1280, fps=25)
    """
    width = int(video_properties["width"]) if width is None else width
    height = int(video_properties["height"]) if height is None else height
    fps = video_properties["avg_frame_rate"] if fps is None else fps
    size = (width, height)
    return FVideoWriter(
        output_path=save_path,
        frame_size=size,
        frame_pixel_format=PixelFormat.BGR24,
        output_pixel_format=PixelFormat.YUV420P,
        fps=fps,
        bitrate=video_properties["bit_rate"],
    )


def create_dummy_value(dummy_value: int = 0):
    """
    Creates a list of numpy arrays, each initialized with the specified dummy value.

    Args:
        dummy_value (int, optional): The value to be used for initializing the numpy arrays. Defaults to 0.

    Returns:
        list: A list of numpy arrays with each element initialized to the specified dummy value.

    """
    return [np.array(value) for value in [[dummy_value, dummy_value]] * 4]


def is_dummy_value(coordinates: list, dummy_value: int = 0) -> bool:
    """
    Checks if the coordinates represent dummy values, i.e., if all elements in the
    coordinates list are zeros.

    Args:
        coordinates (list): A list of lists representing the coordinates.
        dummy_value (int): The value to be used for initializing the numpy arrays. Defaults to 0.

    Returns:
        bool: True if all elements in the coordinates are zeros, False otherwise.

    """
    return all(all(value == dummy_value for value in sublist) for sublist in coordinates)


def create_message(
    status_error: str, description: str, error_info: dict, solution: str = "Reproduce and debug 👨‍🎤", is_dev: bool = False
):
    if not is_dev:
        return f"{description} in {len(error_info['frame_id'])} frames (Violation of {status_error})."
    return f"""
        Error: {status_error}

        Description: {description}

        Details:
            - Video path: {error_info['video_path']}
            - Video url: {error_info['video_url']}
            - Frame id: {error_info['frame_id']}
            - Url dir: {error_info['url_dir']}

        Solution: {solution}
    """


def get_frame(video_path: str, index: int = 0) -> np.ndarray:
    """
    Reads a frame from a video file based on the given index.

    Args:
        video_path (str): The path to the video file.
        index (int, optional): The index of the frame to retrieve. Defaults to 0.

    Returns:
        np.ndarray: The frame of the video at the specified index, represented as a NumPy array.

    Raises:
        FileNotFoundError: If the video file cannot be opened.
        ValueError: If the frame cannot be read from the video file.

    Example:
        frame = get_frame("video.mp4", index=10)
    """
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        raise FileNotFoundError(f"Can't open video {video_path}")

    # Set the position of the video frame to the desired index
    cap.set(cv2.CAP_PROP_POS_FRAMES, index)

    _, frame = cap.read()

    cap.release()

    if frame is None:
        raise ValueError(f"Can't read frame at index {index} from video {video_path}")

    return frame


def get_dimension(coordinates: np.ndarray) -> Tuple[Union[float, int], Union[float, int]]:
    """TODO:
    - Assert coordinates was sorted (counter-clockwise direction).
    """
    width_screen = (coordinates[3][0] - coordinates[0][0] + coordinates[2][0] - coordinates[1][0]) / 2
    height_screen = (coordinates[1][1] - coordinates[0][1] + coordinates[2][1] - coordinates[3][1]) / 2
    return width_screen, height_screen


def check_column_uniformity(df: pd.DataFrame, column_name: str) -> bool:
    """
    Check if all values in a column of a DataFrame are identical.

    Args:
        df (pd.DataFrame): The DataFrame containing the data to be checked.
        column_name (str): The name of the column to be checked.

    Returns:
        bool: Returns True if all values in the column are identical, False otherwise.
    """

    def _convert_to_str(x):
        if isinstance(x, list):
            return str(x)
        elif isinstance(x, np.ndarray):
            return str(x.tolist())
        else:
            return x

    column_values = df[column_name].apply(_convert_to_str)
    return column_values.nunique() == 1


def convert_columns_str_to_list(df: pd.DataFrame, columns: list = ["A", "B", "C", "D"]) -> pd.DataFrame:
    for col in columns:
        df[col] = df[col].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
    return df


def remove_path(path: str):
    """
    Removes the specified file or directory at the given path.

    """
    try:
        if os.path.isdir(path):
            shutil.rmtree(path)
            print(f"Removed folder: {path}")
        elif os.path.isfile(path):
            os.remove(path)
            print(f"Removed file: {path}")
        else:
            print(f"The path does not exist or is not a valid file/folder: {path}")
    except Exception as e:
        print(f"An error occurred when remove path: '{path}': {e}")


def parse_device(device: str) -> Tuple[str, int]:
    if not cuda_is_available():  # If no CUDA is available
        return "cpu", 0

    # Validate device format
    if device == "cpu":
        return "cpu", 0

    # Match formats like 'cuda:0', 'cuda:1', etc.
    match = re.fullmatch(r"cuda:(\d+)", device)
    if match:
        device_type = "cuda"
        device_id = int(match.group(1))
        return device_type, device_id

    # Invalid format
    raise ValueError(f"Invalid device format: '{device}'. Expected 'cpu' or 'cuda:<id>'")


def init_model_with_lock(
    model_folder: str,
    init_func: Callable[[], Any],
    download_in_progress_msg: str = cfg.led.download_in_progress_msg,
    download_success_msg: str = cfg.led.download_success_msg,
    status_filename: str = cfg.led.status_filename,
) -> Any:
    """
    Initialise the model using a locking mechanism to prevent concurrent downloads/initialisations.

    Parameters:
      - dir_save: Base directory where the lock file is stored.
      - model_folder: Folder specific to the model.
      - init_func: A callable that initialises and returns the model instance.
      - download_in_progress_msg: Status message for in-progress state.
      - download_success_msg: Status message for successful initialisation.
      - status_filename: The name of the status file within the model folder.

    Returns:
      - The initialised model instance.
    """
    # Create a lock file based on the model folder name
    lock_file = os.path.normpath(model_folder) + ".lock"
    # Define the status file path inside the model folder
    status_file = os.path.join(model_folder, status_filename)

    with FileLock(lock_file):
        # If the status file exists, check its status.
        if os.path.exists(status_file):
            with open(status_file, "r") as f:
                status = f.read().strip()
            if status != download_success_msg:
                logger.warning(f"Previous download attempt did not complete (status: '{status}'). Cleaning up {model_folder}.")
                # Clean up the model folder for a fresh download.
                shutil.rmtree(model_folder)
                os.makedirs(model_folder, exist_ok=True)

        # Mark the initialisation as in progress.
        with open(status_file, "w") as f:
            f.write(download_in_progress_msg)

        try:
            # Execute the initialisation function to get the model instance.
            model_instance = init_func()
        except Exception as e:
            raise ModelInitialisationError(e) from e

        # Mark the initialisation as complete.
        with open(status_file, "w") as f:
            f.write(download_success_msg)

    return model_instance
