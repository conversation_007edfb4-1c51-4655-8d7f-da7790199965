import traceback
from typing import List, Union

import cv2
import numpy as np
from codetiming import Timer
from numba import njit

from vsc.utils.config import cfg
from vsc.utils.logger import logger, logger_perf


class MaskScreen:
    """Post-processing for mask screen"""

    @staticmethod
    @Timer("MaskScreen.get_mask_based_on_hsv", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def get_mask_based_on_hsv(image: np.ndarray, color_range: List = cfg.led.mask.color_range) -> np.ndarray:
        """
        Get mask based on HSV color range.

        Args:
            image (np.ndarray): Input image.
            color (str, optional): Color name. Defaults to "green".

        Returns:
            np.ndarray: Masked image.
        """
        image_hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
        lower_color = np.array(color_range[0])
        upper_color = np.array(color_range[1])
        return cv2.inRange(image_hsv, lower_color, upper_color)

    @staticmethod
    @Timer("MaskScreen.get_largest_mask", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def get_largest_mask(mask: np.ndarray) -> np.ndarray:
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        area_list = [cv2.contourArea(contour) for contour in contours]
        largest_mask = np.zeros_like(mask)
        if len(area_list) == 0:
            return largest_mask
        largest_area_index = area_list.index(max(area_list))
        cv2.drawContours(largest_mask, [contours[largest_area_index]], -1, 255, thickness=cv2.FILLED)
        return largest_mask

    @staticmethod
    @Timer("MaskScreen.get_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def get_coordinates(mask: np.ndarray, epsilon: float = 1e-5, old_method: bool = False):
        """
        Get coordinates from the mask.

        Args:
            mask (np.ndarray): Input mask.
            epsilon (float, optional): Epsilon value for approximation. Defaults to 0.01.

        Returns:
            np.ndarray: Coordinates of the mask.
        """
        if not old_method:
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)

            if len(contours) == 0:
                return

            # # Find the largest contour by area
            largest_contour = max(contours, key=cv2.contourArea)

            # # Get all points around the largest contour
            contour_points = largest_contour.squeeze()  # Squeeze to remove extra dimensions

            return contour_points.squeeze()
        else:
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            area_list = [cv2.contourArea(contour) for contour in contours]

            if len(area_list) == 0:
                return

            largest_area_index = area_list.index(max(area_list))

            coordinates = cv2.approxPolyDP(
                contours[largest_area_index],
                epsilon * cv2.arcLength(contours[largest_area_index], True),
                True,
            )  # counter-clockwise

            return np.squeeze(coordinates, axis=1)

    @staticmethod
    @Timer("MaskScreen.get_screen_mask_and_coordinates", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def get_screen_mask_and_coordinates(image: np.ndarray, epsilon: float = 1e-5) -> tuple:
        """
        Get screen mask and coordinates based on HSV color range.

        Args:
            image (np.ndarray): Input image.
            color (str, optional): Color name. Defaults to "green".
            epsilon (float, optional): Epsilon value for approximation. Defaults to 0.01.

        Returns:
            Tuple[np.ndarray, np.ndarray]: Screen mask and coordinates.
        """
        mask = MaskScreen.get_mask_based_on_hsv(image)
        coordinates = MaskScreen.get_coordinates(mask, epsilon)
        return mask, coordinates

    @staticmethod
    def morphologyEx(mask: np.ndarray, kernel_size: int) -> np.ndarray:
        """
        Perform morphological operation on the mask.

        Args:
            mask (np.ndarray): Input mask.
            kernel_size (int): Kernel size for morphological operation.

        Returns:
            np.ndarray: Processed mask.
        """
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        return cv2.threshold(mask, 1, 255, cv2.THRESH_BINARY)[1]

    @staticmethod
    @Timer("MaskScreen.smoothing", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def smoothing(mask: np.ndarray, kernel_size: int = 31):
        """
        Smooth the mask using morphological operation.

        Args:
            mask (np.ndarray): Input mask.
            kernel_size (int, optional): Kernel size for morphological operation. Defaults to 31.

        Returns:
            np.ndarray: Smoothed mask.
        """
        return MaskScreen.morphologyEx(mask, kernel_size)

    @staticmethod
    @Timer("MaskScreen.conserve_hand", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def conserve_hand(
        mask_origin: np.ndarray,
        mask_smoothed: np.ndarray,
        kernel_size_denoise: int = 1,
        kernel_size_smooth_hand: int = 3,
    ) -> np.ndarray:
        """
        Conserve the hand in the mask.

        Args:
            mask_origin (np.ndarray): Original mask.
            mask_smoothed (np.ndarray): Smoothed mask.
            kernel_size_denoise (int, optional): Kernel size for denoising. Defaults to 1.
            kernel_size_smooth_hand (int, optional): Kernel size for smoothing hand. Defaults to 3.

        Returns:
            np.ndarray: Mask with conserved hand.
        """
        mask_missing = cv2.subtract(mask_smoothed, mask_origin)
        mask_missing_denoise = MaskScreen.remove_dense_noise(mask_missing, kernel_size_denoise)
        mask_missing_smooth = MaskScreen.smoothing(mask_missing_denoise, kernel_size_smooth_hand)
        return cv2.subtract(mask_smoothed, mask_missing_smooth)

    @staticmethod
    @Timer("MaskScreen.dilate", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def dilate(mask: np.ndarray, kernel_size: int = 5, crop_mode: bool = False) -> np.ndarray:
        """
        Dilate the given mask using a structuring element of the specified size.

        Args:
            mask (np.ndarray): Input mask to be dilated.
            kernel_size (int, optional): Size of the kernel for dilation. Defaults to 5.
            crop_mode (bool, optional): Whether to dilate only the cropped region of the mask. Defaults to False.

        Returns:
            np.ndarray: The dilated mask.
        """
        if mask.size == 0:
            return mask

        if mask.ndim == 3:
            mask = mask[:, :, 0]

        element = cv2.getStructuringElement(
            cv2.MORPH_ELLIPSE,
            (2 * kernel_size + 1, 2 * kernel_size + 1),
            (kernel_size, kernel_size),
        )

        if not crop_mode:
            return cv2.dilate(mask, element)

        x1, y1, x2, y2 = MaskScreen.extract_region(mask)
        cropped_mask = mask[y1:y2, x1:x2]

        if cropped_mask.size == 0:
            return mask

        dilated_cropped_mask = cv2.dilate(cropped_mask, element)
        mask = np.zeros_like(mask)
        mask[y1:y2, x1:x2] = dilated_cropped_mask
        return mask

    @staticmethod
    @Timer("MaskScreen.remove_dense_noise", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def remove_dense_noise(mask: np.ndarray, kernel_erosion: int = 2):
        """
        Remove dense noise from the mask.

        Args:
            mask (np.ndarray): Input mask.
            kernel_erosion (int, optional): Kernel size for erosion. Defaults to 2.

        Returns:
            np.ndarray: Mask with removed dense noise.
        """
        element = cv2.getStructuringElement(
            cv2.MORPH_CROSS,
            (2 * kernel_erosion + 1, 2 * kernel_erosion + 1),
            (kernel_erosion, kernel_erosion),
        )
        mask_denoise = cv2.erode(mask, element)
        mask_conserve_area = cv2.dilate(mask_denoise, element)
        return mask_conserve_area

    @staticmethod
    def calculate_object_area_to_frame(mask: np.ndarray, object_threshold: float = 0.9):
        '''
        Calculate ratio between object area and mask dimension.

        Args:
            mask (np.ndarray): 2D Mask
            object_threshold (float): Threshold used to determine which pixels belong to the object.

        Returns:
            object_ratio (float): Object area / Mask dimension
        '''
        object_area = MaskScreen.calculate_object_area(mask, object_threshold)
        object_ratio = MaskScreen.calculate_ratio_between_object_and_frame(object_area, mask)
        return object_ratio

    @staticmethod
    def calculate_object_area(mask: np.ndarray, object_threshold: float = 0.9):
        if mask.ndim == 3 and mask.shape[-1] == 3:
            mask = cv2.cvtColor(mask, cv2.COLOR_RGB2GRAY)
        threshold_value = int(object_threshold * mask.max())
        area = np.count_nonzero(mask > threshold_value)
        return area

    @staticmethod
    def calculate_ratio_between_object_and_frame(object_area: int, frame: np.ndarray):
        return object_area / (frame.shape[0] * frame.shape[1])

    @staticmethod
    @Timer("MaskScreen.remove_difficult_noise_with_timi_matte", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def remove_difficult_noise_with_timi_matte(
        matte_timi: np.ndarray,
        mask: np.ndarray,
        threshold: float = cfg.led.mask.timi_matte_threshold_for_removing_noise,
        dilation_kernel_size: int = 5,
    ):
        """
        Removes difficult noise using the TIMI matte with adjusted bounding box extraction.

        Args:
            matte_timi (np.ndarray): TIMI matte input.
            mask (np.ndarray): Binary mask input.
            threshold (float): Threshold to binarize the TIMI matte.
            padding (int): Padding added to bounding box to ensure full coverage.

        Returns:
            np.ndarray: Cleaned mask.
        """
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        area_list = [cv2.contourArea(contour) for contour in contours]

        if len(area_list) == 0:
            return mask

        largest_area_index = area_list.index(max(area_list))

        # Iterate over contours and remove those that don't intersect with the dilated mask
        for i, contour in enumerate(contours):
            if i != largest_area_index:
                x, y, w, h = cv2.boundingRect(contour)

                # Apply padding to the bounding box
                x_pad = max(x - dilation_kernel_size, 0)
                y_pad = max(y - dilation_kernel_size, 0)
                w_pad = min(w + 2 * dilation_kernel_size, matte_timi.shape[1] - x_pad)
                h_pad = min(h + 2 * dilation_kernel_size, matte_timi.shape[0] - y_pad)

                # Extract region with padding
                mask_with_only_this_contour = np.zeros((h_pad, w_pad), dtype=np.uint8)
                cv2.fillPoly(
                    mask_with_only_this_contour,
                    pts=[contour - [x_pad, y_pad]],
                    color=1,
                )

                # Dilate within the padded region
                dilated_region = MaskScreen.dilate(
                    (matte_timi[y_pad : y_pad + h_pad, x_pad : x_pad + w_pad] > threshold).astype(np.uint8),
                    kernel_size=dilation_kernel_size,
                )

                # Calculate intersection
                intersection = np.bitwise_and(mask_with_only_this_contour, dilated_region)

                # Remove contour if no intersection
                if not np.any(intersection):
                    cv2.fillPoly(mask, pts=[contour], color=0)
        return mask

    @staticmethod
    @Timer("MaskScreen.remove_sparse_noise", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def remove_sparse_noise(
        mask: np.ndarray,
        coordinates: Union[np.ndarray, List],
        matte_timi: np.ndarray,
        timi_matte_removing_threshold: float = cfg.led.mask.timi_matte_threshold_for_removing_noise,
    ):
        """
        Remove sparse noise from the mask.

        Args:
            mask (np.ndarray): Input mask.
            coordinates (Union[np.ndarray, List]): Coordinates for removing noise.

        Returns:
            np.ndarray: Mask with removed sparse noise.
        """
        if isinstance(coordinates, list):
            coordinates = np.array(coordinates)

        coordinates = coordinates.reshape((-1, 1, 2))
        contours, _ = cv2.findContours(mask, cv2.RETR_TREE, cv2.CHAIN_APPROX_SIMPLE)

        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            center_point = [x + w / 2, y + h / 2]
            result = cv2.pointPolygonTest(coordinates, center_point, False)

            """
                result = -1: Point is outside the polygon
                result = 0: Point is on the polygon
                result = 1: Point is inside the polygon
            """

            if result == -1:
                cv2.fillPoly(mask, pts=[contour], color=0)

        return MaskScreen.remove_difficult_noise_with_timi_matte(matte_timi, mask, timi_matte_removing_threshold)

    @staticmethod
    @Timer("MaskScreen.remove_sparse_noise_based_on_mask", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def remove_sparse_noise_based_on_mask(noise_mask: np.ndarray, final_mask: np.ndarray, kernel: int = 1):
        """
        Removes noise regions from `noise_mask` that do not overlap with `final_mask`.

        Args:
            noise_mask (np.ndarray): Binary mask containing noise regions to filter out.
            final_mask (np.ndarray): Binary mask representing the main mask area.
            kernel (int, optional): Used in MaskScreen.dilate.

        Returns:
            np.ndarray: Updated `noise_mask` with non-overlapping noise regions removed.
        """
        contours, _ = cv2.findContours(noise_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            x_pad = max(x - kernel, 0)
            y_pad = max(y - kernel, 0)
            w_pad = min(w + 2 * kernel, noise_mask.shape[1] - x_pad)
            h_pad = min(h + 2 * kernel, noise_mask.shape[0] - y_pad)
            temp_mask = np.zeros((h_pad, w_pad), dtype=np.uint8)
            cv2.fillPoly(temp_mask, [contour - [x_pad, y_pad]], color=255)
            intersection = MaskScreen.dilate(temp_mask, kernel) & final_mask[y_pad : y_pad + h_pad, x_pad : x_pad + w_pad]
            if not intersection.any():
                noise_mask[y : y + h, x : x + w] = 0
        return noise_mask

    @staticmethod
    @Timer("MaskScreen.denoise", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def denoise(mask: np.ndarray, coordinates: Union[np.ndarray, List], matte_timi: np.ndarray, kernel_erode: int = 2):
        """
        Denoise the mask.

        Args:
            mask (np.ndarray): Input mask.
            coordinates (Union[np.ndarray, List]): Coordinates for denoising.
            kernel_erode (int, optional): Kernel size for erosion. Defaults to 2.

        Returns:
            np.ndarray: Denoised mask.
        """
        mask = MaskScreen.remove_dense_noise(mask, kernel_erode)
        mask = MaskScreen.remove_sparse_noise(mask, coordinates, matte_timi)
        return mask

    @staticmethod
    @Timer("MaskScreen.refine_mask", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def refine_mask(
        mask_smoothed: np.ndarray,
        mask_origin: np.ndarray,
        frame: np.ndarray,
        coordinates: np.ndarray,
        matte_timi: np.ndarray,
    ) -> np.ndarray:
        """
        Refine the mask.

        Args:
            mask_smoothed (np.ndarray): Smoothed mask.
            mask_origin (np.ndarray): Original mask.
            frame (np.ndarray): Input frame.
            coordinates (np.ndarray): Coordinates.

        Returns:
            np.ndarray: Refined mask.
        """
        # Conserve hand
        mask_smoothed = MaskScreen.conserve_hand(mask_origin, mask_smoothed)

        # Reduce green area
        bg_main = cv2.bitwise_or(frame, frame, mask=cv2.bitwise_not(mask_smoothed))
        try:
            missing_mask = MaskScreen.get_mask_based_on_hsv(bg_main)
        except AssertionError:
            missing_mask = np.zeros(bg_main.shape[:2], dtype=np.uint8)
        missing_mask = MaskScreen.remove_sparse_noise(missing_mask, coordinates, matte_timi)
        missing_mask = MaskScreen.remove_sparse_noise_based_on_mask(missing_mask, mask_smoothed)
        return (mask_smoothed + missing_mask).astype(np.uint8)

    @staticmethod
    @njit(fastmath=True)
    def __extract_region_core(mask: np.ndarray, stretch: int = 10):
        """
        Extract the region of interest from a binary mask with optional stretching.

        Args:
            mask (np.ndarray): Input binary mask with values [0, 255] or [0, 1].
            stretch (int): The number of pixels to expand the region of interest. Defaults to 10.

        Returns:
            tuple: Coordinates of the extracted region (x1, y1, x2, y2). If the mask is empty, returns (0, 0, 0, 0).

        Notes:
            - The function assumes the mask is a 2D array.
            - The function will raise an assertion error if the mask is not 2D.
            - The stretching is applied to ensure that the extracted region is slightly larger than the non-zero region.
        """
        assert mask.ndim == 2, f"Dimensions of mask must be equal to 2, but the given mask has shape: {mask.shape}"
        ys, xs = np.nonzero(mask)

        if len(ys) == 0 or len(xs) == 0:  # Handle the case where the mask is empty
            return 0, 0, 0, 0

        y_min = ys.min()
        y_max = ys.max()
        x_min = xs.min()
        x_max = xs.max()

        x1 = max(0, x_min - stretch)
        y1 = max(0, y_min - stretch)
        x2 = min(mask.shape[1] - 1, x_max + stretch)
        y2 = min(mask.shape[0] - 1, y_max + stretch)
        return x1, y1, x2, y2

    @staticmethod
    @Timer("MaskScreen.extract_region", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def extract_region(mask: np.ndarray, stretch: int = 10):
        """
        Extract the region of interest from a binary mask with optional stretching.

        Args:
            mask (np.ndarray): Input binary mask with values [0, 255] or [0, 1], shape `hwc`.
            stretch (int): The number of pixels to expand the region of interest. Defaults to 10.

        Returns:
            tuple: Coordinates of the extracted region (x1, y1, x2, y2). If the mask is empty, returns (0, 0, 0, 0).

        Notes:
            The function converts 3D masks to 2D by selecting the first channel.
        """
        if mask.ndim == 3:
            mask = mask[:, :, 0]

        return MaskScreen.__extract_region_core(mask, stretch)

    @staticmethod
    @Timer("MaskScreen.smooth_with_pyramid", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def smooth_with_pyramid(image: np.ndarray, iters: int = 1):
        image_clone = image.copy()
        for _ in range(iters):
            image_clone = cv2.pyrUp(cv2.pyrDown(image_clone))
        return image_clone

    @staticmethod
    def calibrate_coordinates_for_pyramid(frame: np.ndarray, x1: int, y1: int, x2: int, y2: int):
        '''
        This function makes sure the bounding box dimension is even, so the pyramid can return the correct dimension

        Args:
            frame (np.ndarray): Original frame (NOT cropped screen)
            x1, y1, x2, y2 (int): coordinates of the bounding box

        Returns:
            x1, y1, x2, y2 (int): new coordinates of the bounding box where the pyramid algorithm can safely run
        '''
        if (x2 - x1) % 2 != 0:
            if x2 == frame.shape[1] - 1:
                x1 += 1  # To reduce workload
            else:
                x2 -= 1  # To reduce workload

        if (y2 - y1) % 2 != 0:
            if y2 == frame.shape[0] - 1:
                y1 += 1  # To reduce workload
            else:
                y2 -= 1  # To reduce workload
        return x1, y1, x2, y2

    @staticmethod
    @Timer("MaskScreen.smooth_edge", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def smooth_edge(
        mask_final: np.ndarray,
        frame: np.ndarray,
        iters: int = cfg.led.mask.pyramid_iters,
        use_edge_preserving: bool = cfg.led.mask.use_edge_preserving,
        sigma_s: float = cfg.led.mask.sigma_s,
        sigma_r: float = cfg.led.mask.sigma_r,
    ) -> np.ndarray:
        x1, y1, x2, y2 = MaskScreen.extract_region(mask_final, cfg.led.mask.bbox_stretch)
        calibrated_x1, calibrated_y1, calibrated_x2, calibrated_y2 = MaskScreen.calibrate_coordinates_for_pyramid(
            frame, x1, y1, x2, y2
        )
        processing_patch = frame[calibrated_y1:calibrated_y2, calibrated_x1:calibrated_x2, :]
        if processing_patch.size == 0:
            return mask_final
        processing_patch = MaskScreen.smooth_with_pyramid(processing_patch, iters=iters)
        if use_edge_preserving:
            processing_patch = cv2.edgePreservingFilter(processing_patch, flags=1, sigma_s=sigma_s, sigma_r=sigma_r)
        processing_mask = MaskScreen.get_mask_based_on_hsv(processing_patch)
        mask_final[calibrated_y1:calibrated_y2, calibrated_x1:calibrated_x2] = processing_mask
        return mask_final

    @staticmethod
    @Timer("MaskScreen.run", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def run(
        mask: np.ndarray,
        frame: np.ndarray,
        coordinates: np.ndarray,
        matte_timi: np.ndarray,
        kernel_smoothing: int = 31,
        kernel_erode: int = 2,
    ):
        """
        Run the mask screen post-processing pipeline.

        Args:
            mask (np.ndarray): Input mask.
            frame (np.ndarray): Input frame.
            coordinates (np.ndarray): Coordinates.
            kernel_smoothing (int, optional): Kernel size for smoothing. Defaults to 31.
            kernel_erode (int, optional): Kernel size for erosion. Defaults to 2.

        Returns:
            np.ndarray: Processed mask.
        """
        mask = cv2.cvtColor(mask, cv2.COLOR_BGR2GRAY)
        mask = MaskScreen.smooth_edge(mask, frame)

        mask_denoise = MaskScreen.denoise(mask, coordinates, matte_timi, kernel_erode)

        mask_refined = MaskScreen.refine_mask(mask, mask_denoise, frame, coordinates, matte_timi)

        return mask_refined


class MatteForeground:
    @staticmethod
    @Timer("MatteForeground.remove_sparse_noise_matte", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    def remove_sparse_noise_matte(alpha_matte: np.ndarray):
        if np.issubdtype(alpha_matte.dtype, np.floating) and alpha_matte.max() <= 1:
            alpha_matte = alpha_matte * 255

        alpha_matte = alpha_matte.astype(np.uint8)
        mask = np.zeros_like(alpha_matte)

        try:
            _, binary_mask = cv2.threshold(alpha_matte, 0, 255, cv2.THRESH_BINARY)

            contours, _ = cv2.findContours(binary_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_NONE)
            area_list = [cv2.contourArea(contour) for contour in contours]

            if len(area_list) == 0:
                return mask

            largest_contour_index = np.argmax(area_list)

            for i, contour in enumerate(contours):
                if i != largest_contour_index:
                    cv2.drawContours(mask, [contour], -1, 255, thickness=cv2.FILLED)

            inverted_mask = cv2.bitwise_not(mask)

            return cv2.bitwise_and(alpha_matte, alpha_matte, mask=inverted_mask)

        except Exception:
            logger.warning(f"An error orcured when remove sparse noise in matte: {traceback.format_exc()}")
            return mask
