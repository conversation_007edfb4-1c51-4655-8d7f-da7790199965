import shutil
from typing import <PERSON>ple

import cv2
import numpy as np
from codetiming import Timer
from fvutils.videoio import FVideoCapture, PixelFormat

from vsc.composite_led.datastruct import ImageData, VideoProcessingMode
from vsc.composite_led.functions import Coordinates
from vsc.composite_led.functions.mask import MaskScreen
from vsc.composite_led.utilizer.writer import VideoWriterProcessor
from vsc.utils.logger import logger, logger_perf


class Screen:
    """Handle case: hidden screen"""

    @staticmethod
    def is_hidden(coordinates: np.ndarray, frame: np.ndarray) -> Tuple[bool, bool]:
        """TODO:
        - Assert coordinates in frame area.
        - Support check top and bottom hidden.
        - Handle case: the screen does not move horizontally with the X-axis.
        """

        _, width_frame = frame.shape[:2]
        left_hidden = any(np.where(coordinates[:2, 0] == 0, True, False))
        right_hidden = any(np.where(coordinates[2:, 0] == width_frame - 1, True, False))

        return left_hidden, right_hidden

    @staticmethod
    def find_hidden_name(coordinates: np.ndarray, frame: np.ndarray) -> str:
        assert len(coordinates) == 4, "Number of coordinates should be exact 4."

        coordinates = Coordinates.sort_coordinates(coordinates)

        left_hidden, right_hidden = Screen.is_hidden(coordinates, frame)

        hidden_name = None
        if any([left_hidden, right_hidden]):
            # TODO: Enhance block code below when screen positioned on the left, right, top or bottom hiddened.
            hidden_name = "left" if left_hidden else "right"

        return hidden_name

    @Timer("Screen.get_mask", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @staticmethod
    def get_mask(data: ImageData):
        """
        Extracts a green screen mask for each frame in the video and writes it to the output.

        Args:
            data (ImageData): Contains video input path and writer information.

        Returns:
            None
        """
        cap = FVideoCapture(data.input_args.video_path, output_pixel_format=PixelFormat.BGR24)
        video_properties = cap.get_video_properties()
        frame_width = int(video_properties["width"])
        frame_height = int(video_properties["height"])
        fps = eval(video_properties["avg_frame_rate"])

        writer = cv2.VideoWriter(
            filename=data.output_paths.save_path_mask,
            fourcc=cv2.VideoWriter_fourcc(*'FFV1'),
            fps=fps,
            frameSize=(int(frame_width), int(frame_height)),
            isColor=False,
        )

        for ret, image in cap.read():
            if not ret:
                break
            mask = MaskScreen.get_mask_based_on_hsv(image)
            writer.write(mask)

        writer.release()

    @Timer("Screen.check_status", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @staticmethod
    def check_status(video_path: str, frame_skip: int = 2) -> ImageData:
        """
        Analyzes the video to check if a green screen is present and determines its status.

        Args:
            data (ImageData): Contains video input details and analysis flags.
            frame_skip (int, optional): The number of frames to skip for processing. Defaults to 2.

        Returns:
            ImageData: Updated with green screen status and related flags.
        """
        logger.info(
            "🤺 Starting green screen status check. If no screen is detected, the module will return the unprocessed video as output. If a static screen is detected (no movement), the module will stabilize coordinates and process other elements (image, audio, etc.). Otherwise, the input will be processed with full logic."
        )
        cap = FVideoCapture(video_path, output_pixel_format=PixelFormat.BGR24)

        screen_bbox = []

        frame_count = -1
        for ret, image in cap.read():
            if not ret:
                break

            frame_count += 1
            if frame_count % frame_skip != 0:
                continue

            mask = MaskScreen.get_mask_based_on_hsv(image)
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if contours:
                largest_contour = max(contours, key=cv2.contourArea)
                bbox = cv2.boundingRect(largest_contour)
            else:
                bbox = [0, 0, 0, 0]

            screen_bbox.append(bbox)

        screen_bbox = np.array(screen_bbox)
        screen_area = np.mean(screen_bbox[:, 2] * screen_bbox[:, 3])
        frame_area = mask.shape[0] * mask.shape[1]

        screen_per_frame = screen_area / frame_area
        static_screen, have_screen = False, True
        if screen_per_frame < 1e-3:
            logger.info("👀 No green screen detected in the video input.")
            have_screen = False

        screen_bbox_std = np.std(screen_bbox, axis=0)
        if np.count_nonzero(screen_bbox_std) < 4 and np.mean(screen_bbox_std) < 1:
            logger.info("👀 The green screen in the video input is static (no movement).")
            static_screen = True

        return static_screen, have_screen

    @Timer("Screen.handle_missing", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
    @staticmethod
    def handle_missing(data: ImageData):
        """
        Handles cases where no green screen is detected by copying the input video to the output path.

        Args:
            data (ImageData): Contains video input path and output save path.

        Returns:
            None
        """
        logger.info(
            f"🤨 The detected video input ({data.input_args.video_path}) has no screen, so the system will return the unprocessed video"
        )
        shutil.copy2(data.input_args.video_path, data.output_paths.save_path)
