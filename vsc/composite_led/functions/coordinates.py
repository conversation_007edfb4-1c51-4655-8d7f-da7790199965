import ast
import itertools
import math
import os
from collections import deque
from typing import Any, List, Union
from warnings import simplefilter

import cv2
import deprecation
import numpy as np
import pandas as pd
from scipy.ndimage import gaussian_filter
from scipy.signal import butter, savgol_filter, sosfiltfilt

from vsc.composite_led.datastruct import ScreenOrientation
from vsc.composite_led.detector import YOLOv8ONNX
from vsc.composite_led.detector.yolo_utils import multiclass_nms
from vsc.composite_led.functions import helpers
from vsc.composite_led.functions.mask import MaskScreen, MatteForeground
from vsc.composite_led.utilizer import Draw
from vsc.utils.config import cfg
from vsc.utils.logger import logger, logger_debug
from vsc.utils.minio_utils import is_downloadable, upload


simplefilter(action="ignore", category=pd.errors.PerformanceWarning)


class PostProcessing:
    """Post-processing for coordinates, include: smoothing, remove noise, handle outlier, stabilize"""

    window_size = 20
    smoothing_queue = deque(maxlen=window_size)

    @staticmethod
    def calculate_distance(row, arg1, arg2):
        """
        Calculates the Euclidean distance between two points specified by their row indices.

        Args:
            row (pandas.Series): A pandas Series object.
            arg1 (int): The coordinates of the first point..
            arg2 (int): The coordinates of the second point.

        Returns:
            float: The Euclidean distance between the two points.

        """
        return math.sqrt((row[arg1][0] - row[arg2][0]) ** 2 + (row[arg1][1] - row[arg2][1]) ** 2)

    @staticmethod
    def read_csv(csv_file: Union[str, pd.DataFrame], columns: list = ["A", "B", "C", "D"]):
        """
        Reads a CSV file or DataFrame.

        Args:
            csv_file (Union[str, pd.DataFrame]): The path to the CSV file or a DataFrame object.
            columns (list): A list of column names to process.

        Returns:
            pd.DataFrame: A DataFrame containing the data read from the CSV file or the input DataFrame.

        Raises:
            FileNotFoundError: If the specified CSV file is not found.
        """
        # Read the CSV file into a DataFrame
        df = pd.read_csv(csv_file) if isinstance(csv_file, str) else csv_file

        return helpers.convert_columns_str_to_list(df, columns)

    @staticmethod
    def smoothing_coordinates(
        csv_file: Union[str, pd.DataFrame],
        savgol_window_length: int = 5,
        savgol_polyorder: int = 1,
        savgol_padding_mode: str = "interp",
        butter_order: int = 2,
        butter_cutoff_freq: float = 0.1,
        gauss_sigma: float = 0.15,
        gauss_padding_mode: str = "nearest",
    ) -> pd.DataFrame:
        """
        Smooth the coordinates in the provided CSV file or a DataFrame using the Center Moving Average technique.

        Args:
            csv_file (Union[str, pd.DataFrame]): Path to the CSV file or a DataFrame.
            savgol_window_length (int): Savgol window length in coordinate_smoothing_pipeline function.
            savgol_polyorder (int): Savgol poly order in coordinate_smoothing_pipeline function.
            savgol_padding_mode (str): SavGol padding mode in coordinate_smoothing_pipeline function
            butter_order (int): butter_order in coordinate_smoothing_pipeline function.
            butter_cutoff_freq (float): butter_cutoff_freq in coordinate_smoothing_pipeline function.
            gauss_sigma (float): gauss_sigma in coordinate_smoothing_pipeline function.
            gauss_padding_mode (str): gauss_padding_mode in coordinate_smoothing_pipeline function.

        Returns:
            pd.DataFrame: A DataFrame containing the smoothed coordinates for columns A, B, C, and D.

        Notes:
            This function assumes that the CSV file contains columns named 'frame_id', 'corner_missing', 'A', 'B', 'C', and 'D' representing coordinate values.
        """
        columns = ["A", "B", "C", "D"]

        # Read the CSV file into a DataFrame
        df = PostProcessing.read_csv(csv_file, columns)

        intervals = PostProcessing.find_intervals(df)

        sub_dfs = []
        for interval in intervals:
            start_idx, end_idx, screen_visible_flag = interval
            if screen_visible_flag:
                processed_df = PostProcessing.coordinate_smoothing_pipeline(
                    df.iloc[start_idx:end_idx],
                    butter_order=butter_order,
                    butter_cutoff_freq=butter_cutoff_freq,
                    savgol_window_length=savgol_window_length,
                    savgol_polyorder=savgol_polyorder,
                    savgol_padding_mode=savgol_padding_mode,
                    gauss_sigma=gauss_sigma,
                    gauss_padding_mode=gauss_padding_mode,
                )
            else:
                processed_df = df.iloc[start_idx:end_idx]

            sub_dfs.append(processed_df)

        df_smoothed = pd.concat(sub_dfs, axis=0)
        return df_smoothed

    @staticmethod
    def coordinate_smoothing_pipeline(
        df: pd.DataFrame,
        butter_order: int = 2,
        butter_cutoff_freq: float = 0.1,
        savgol_window_length: int = 5,
        savgol_polyorder: int = 1,
        savgol_padding_mode: str = "interp",
        gauss_sigma: float = 0.15,
        gauss_padding_mode: str = "nearest",
    ):
        '''
        A coordinate smoothing process that includes ButterWorth filter, SavGol filter, Gaussian filter.

        Args:
            df (pd.DataFrame): pandas dataframe that must have 4 columns ["A", "B", "C", "D"].
            butter_order (int): ButterWorth filter order.
            butter_cutoff_freq (float): Cutoff frequency of ButterWorth filter.
            savgol_window_length (int): Savgol Window length.
            savgol_polyorder (int): Savgol polynomial order.
            savgol_padding_mode (str): Savgol padding mode.
            gauss_sigma (float): Gaussian sigma.
            gauss_padding_mode (str): Gauss padding mode.

        Returns:
            smoothed_df (pd.DataFrame): Same format as the input df with full-pipeline smoothed A, B, C, D coordinates.
        '''
        smoothed_df = PostProcessing.smooth_coordinates_with_butterworth_filter(
            df, butter_order=butter_order, butter_cutoff_freq=butter_cutoff_freq
        )
        smoothed_df = PostProcessing.smooth_coordinates_with_savgol_filter(
            smoothed_df, window_length=savgol_window_length, poly_order=savgol_polyorder, mode=savgol_padding_mode
        )
        smoothed_df = PostProcessing.smooth_coordinates_with_gaussian_filter(
            smoothed_df, sigma=gauss_sigma, mode=gauss_padding_mode
        )
        return smoothed_df

    @staticmethod
    def smooth_coordinates_with_butterworth_filter(df: pd.DataFrame, butter_order: int = 2, butter_cutoff_freq: float = 0.1):
        '''
        Use ButterWorth filter to smooth ["A", "B", "C", "D"] coordinates in a dataframe.

        Args:
            df (pd.DataFrame): pandas dataframe that must have 4 columns ["A", "B", "C", "D"].
            butter_order (int): ButterWorth filter order, smaller -> smoother the transition between passband and stopband.
            butter_cutoff_freq (float): Cutoff frequency of ButterWorth filter, smaller -> the higher frequencies will be attenuated, vice versa.

        Returns:
            df (pd.DataFrame): Same format as the input df with ButterWorth smoothed A, B, C, D coordinates.
        '''
        sos = butter(butter_order, butter_cutoff_freq, btype="low", analog=False, output="sos")
        default_padlen = 3 * (2 * len(sos) + 1 - min((sos[:, 2] == 0).sum(), (sos[:, 5] == 0).sum()))
        padlen = default_padlen if default_padlen < len(df) else len(df) // 2
        for corner_name in ["A", "B", "C", "D"]:
            trajectory = np.array(list(map(np.array, df[corner_name].values))).astype(float)
            x_filtered = sosfiltfilt(sos, trajectory[:, 0], padlen=padlen)
            y_filtered = sosfiltfilt(sos, trajectory[:, 1], padlen=padlen)
            df[corner_name] = np.stack([x_filtered, y_filtered], -1).tolist()
        logger.debug(
            f"Butterworth filter with order {butter_order} and cutoff {butter_cutoff_freq} has been used to smooth the coordinates!"
        )
        return df

    @staticmethod
    def find_intervals(df: pd.DataFrame) -> list:
        '''
        Find the intervals that have separate screens to smooth in order not to include the moving momentum (or inertia) between the every transition.
        Args:
            df (pd.DataFrame): pandas dataframe that must have 4 columns ["A", "B", "C", "D"].

        Returns:
            start_transition_idx (int): the start timestep of the screen 1 moving.
            end_transition_idx (int): The start timestep of the screen 2 moving.
        '''
        intervals = []
        start = 0
        zeros_mat = np.array([[0, 0], [0, 0], [0, 0], [0, 0]])
        while start < len(df):
            end = start
            while end + 1 < len(df) and (
                (np.array(df.iloc[end + 1][["A", "B", "C", "D"]].tolist()) != zeros_mat).any()
                == (np.array(df.iloc[start][["A", "B", "C", "D"]].tolist()) != zeros_mat).any()
            ):
                end += 1
            intervals.append((start, end + 1, (np.array(df.iloc[start][["A", "B", "C", "D"]].tolist()) != zeros_mat).any()))
            start = end + 1
        return intervals

    @staticmethod
    def smooth_coordinates_with_savgol_filter(
        df: pd.DataFrame, window_length: int = 5, poly_order: int = 1, mode: str = "interp"
    ):
        '''
        Use Savitzky-Golay filter to smooth ["A", "B", "C", "D"] coordinates in a dataframe.

        Args:
            df (pd.DataFrame): pandas dataframe that must have 4 columns ["A", "B", "C", "D"]
            window_length (int): Window length for function approximation in Savitzky-Golay algorithm, larger -> smoother and more drifted.
            poly_order (int): polynomial order of fitted function for each window in Savitzky-Golay filter, smaller -> less overfitted.
            mode (str): padding mode at the boundary of the processing array, ["interp", "mirror", "constant", "nearest", "wrap"].

        Returns:
            df (pd.DataFrame): Same format as the input df with Savitzky-Golay smoothed A, B, C, D coordinates.
        '''
        if len(df) < window_length and mode == "interp":
            mode = "nearest"
        for corner_name in ["A", "B", "C", "D"]:
            coords = np.array(list(map(np.array, df[corner_name].values))).astype(float)
            smoothed_x = savgol_filter(coords[:, 0], window_length=window_length, polyorder=poly_order, mode=mode)
            smoothed_y = savgol_filter(coords[:, 1], window_length=window_length, polyorder=poly_order, mode=mode)
            df[corner_name] = np.stack([smoothed_x, smoothed_y], -1).tolist()
        logger.debug(
            f"SavGol filter with window_length: {window_length} and poly_order: {poly_order} has been used to smooth the coordinates!"
        )
        return df

    @staticmethod
    def smooth_coordinates_with_gaussian_filter(df: pd.DataFrame, sigma: float = 0.15, mode: str = "nearest"):
        '''
        Use Gaussian filter to smooth ["A", "B", "C", "D"] coordinates in a dataframe.

        Args:
            df (pd.DataFrame): pandas dataframe that must have 4 columns ["A", "B", "C", "D"].
            sigma (float): Sigma of smoothing Gaussian bell, larger -> smoother.
            mode (str): ["reflect", "constant", "nearest", "mirror", "wrap"].

        Returns:
            df (pd.DataFrame): Same format as the input df with Gaussian smoothed A, B, C, D coordinates.
        '''
        for corner_name in ["A", "B", "C", "D"]:
            coords = np.array(list(map(np.array, df[corner_name].values))).astype(float)
            smoothed_x = gaussian_filter(coords[:, 0], sigma=sigma, mode=mode)
            smoothed_y = gaussian_filter(coords[:, 1], sigma=sigma, mode=mode)
            df[corner_name] = np.stack([smoothed_x, smoothed_y], -1).tolist()
        logger.debug(f"Gaussian filter with sigma: {sigma} has been used to smooth the coordinates!")
        return df

    @staticmethod
    def calculate_angle_difference(row: pd.DataFrame, columns: List[str]) -> float:
        """
        Calculate the maximum difference between angles formed by consecutive points.

        Args:
            row (pd.DataFrame): A DataFrame containing points.
            columns (List[str]): A list of column names representing the points.

        Returns:
            float: The maximum difference between angles (in degrees) formed by consecutive points.
        """
        return PostProcessing.calculate_angle_difference_quadrilateral(*map(np.array, [row[col] for col in columns]))

    @staticmethod
    def check_rule_1(df: pd.DataFrame, columns: List[str], threshold: int) -> tuple:
        """
        Check for violations of rule 1 in the DataFrame.

        Args:
            df (pd.DataFrame): The DataFrame containing the data.
            columns (List[str]): A list of column names representing the points.
            threshold (int): The threshold for the angle difference.

        Returns:
            Tuple[Union[str, None], Union[str, None], Union[List[int], None], str]: A tuple containing the status of the error, description, list of frame IDs, and solution.
        """
        df["angle_diff"] = df.apply(PostProcessing.calculate_angle_difference, args=(columns,), axis=1)
        df_unusual = df.loc[df["angle_diff"] > threshold]
        status_error = None
        description = None
        frame_id = None
        solution = "Reproduce and debug 👨‍🎤"

        if len(df_unusual) > 0:
            status_error = "rule_1"
            description = "It seems the coordinates do not form a rectangle"
            frame_id = df_unusual["frame_id"].tolist()

        return status_error, description, frame_id, solution

    @staticmethod
    def check_rule_2(df: pd.DataFrame, columns: List[str], threshold: int = 20) -> tuple:
        """
        Check for violations of rule 2 in the DataFrame.

        Args:
            df (pd.DataFrame): The DataFrame containing the data.
            columns (List[str]): A list of column names representing the points.
            threshold (int, optional): The threshold for outlier detection. Defaults to 20.

        Returns:
            Tuple[Union[str, None], Union[str, None], Union[List[int], None], str]: A tuple containing the status of the error, description, list of frame IDs, and solution.
        """
        # Create columns for previous values
        df[["A_prev", "B_prev", "C_prev", "D_prev"]] = df[columns].shift(1)

        for col in columns:
            # Fill missing values with current values
            df[col + "_prev"].fillna(df[col], inplace=True)

            # Calculate Euclidean distance between current and previous coordinates
            df[col + "_diff"] = df.apply(
                PostProcessing.calculate_distance,
                args=(col, col + "_prev"),
                axis=1,
            )

        def __detect_outliers(row, diff_a, diff_b, diff_c, diff_d, threshold_noise):
            distance_list = [row[diff_a], row[diff_b], row[diff_c], row[diff_d]]
            distance_list = [max(value - threshold_noise - min(distance_list), 0) for value in distance_list]
            count = 0
            for item in distance_list:
                if distance_list.count(item) != 3:
                    count += 1
            return count == 1

        df["has_outliers"] = df.apply(__detect_outliers, args=("A_diff", "B_diff", "C_diff", "D_diff", threshold), axis=1)

        df_unusual = df[df["has_outliers"]]

        status_error = None
        description = None
        frame_id = None
        solution = "Reproduce and debug 👨‍🎤"

        if len(df_unusual) > 0:
            status_error = "rule_2"
            description = "It seems the coordinates had unusual values"
            frame_id = df_unusual["frame_id"].tolist()

        return status_error, description, frame_id, solution

    @staticmethod
    def check_rule_3(coordinates: Union[np.ndarray, list], mask_screen: np.ndarray, frame_id: int, threshold: float):
        '''
        Check whether compositing coordinates can cover the whole compositing mask.

        Args:
            coordinates (np.ndarray or list): Expanded coordinates that will be used to composite LED.
            mask_screen (np.ndarray): final mask screen whose noise has been removed.
            frame_id (int): Index of the frame in the video.

        Returns:
            Tuple[Union[str, None], Union[str, None], int, str]: A tuple containing the status of the error, description, frame ID, and solution.
        '''
        coordinates_based_segment = MaskScreen.get_coordinates(mask_screen)
        A_expanded, B_expanded, C_expanded, D_expanded = coordinates
        W_AB, C_AB = Coordinates.line_from_points(A_expanded, B_expanded)
        W_BC, C_BC = Coordinates.line_from_points(B_expanded, C_expanded)
        W_CD, C_CD = Coordinates.line_from_points(C_expanded, D_expanded)
        W_DA, C_DA = Coordinates.line_from_points(D_expanded, A_expanded)
        dist_A_to_all_pts = np.min(Coordinates.distance_point_to_line(coordinates_based_segment.T, (W_AB, C_AB)))
        dist_B_to_all_pts = np.min(Coordinates.distance_point_to_line(coordinates_based_segment.T, (W_BC, C_BC)))
        dist_C_to_all_pts = np.min(Coordinates.distance_point_to_line(coordinates_based_segment.T, (W_CD, C_CD)))
        dist_D_to_all_pts = np.min(Coordinates.distance_point_to_line(coordinates_based_segment.T, (W_DA, C_DA)))

        status_error = None
        description = None
        solution = "Reproduce and debug 👨‍🎤"
        if np.min([dist_A_to_all_pts, dist_B_to_all_pts, dist_C_to_all_pts, dist_D_to_all_pts]) < threshold:
            status_error = "rule_3"
            description = "Detected unusual black areas on the screen"
        return status_error, description, frame_id, solution

    @staticmethod
    def log_and_alert(message_info: tuple, video_path: str, url_dir: str = None, is_dev: bool = False):
        """
        Log error message and alert if there's an error detected.

        Args:
            message_info (Tuple[str, str, int, str]): A tuple containing status of the error, description, frame ID, and solution.
            video_path (str): The path to the video file.
            url_dir (str): The path to the directory folder images in Minio.
        """
        assert message_info is not None, "message_info is not None"
        assert len(message_info) == 4, "message_info must have 4 elements"

        status_error, description, frame_id, solution = message_info

        if status_error is not None:
            video_url = None

            if is_dev:
                # Upload video input to MinIO for reproducing purposes.
                object_name = os.path.join(cfg.minio.public_dir, cfg.minio.bug_dir, os.path.basename(video_path))
                video_url = f"https://{cfg.minio.minio_endpoint}/{cfg.minio.bucket_name}/{object_name}"
                if not is_downloadable(video_url):
                    video_url = upload(object_name, video_path)

            error_info = {"video_path": video_path, "video_url": video_url, "frame_id": frame_id, "url_dir": url_dir}
            message = helpers.create_message(status_error, description, error_info, solution, is_dev)
            logger_debug.warning(message)

    @staticmethod
    def double_check_coordinates(
        csv_file: Union[str, pd.DataFrame],
        video_path: str,
        rule_1_threshold: float = cfg.led.double_check.rule_1.threshold,
        rule_2_threshold: float = cfg.led.double_check.rule_2.threshold,
        is_dev: bool = False,
    ):
        """
        Perform double check on coordinates based on rules and log/alert if errors are detected.

        Args:
            csv_file (Union[str, pd.DataFrame]): The path to the CSV file or a DataFrame containing the coordinates.
            video_path (str): The path to the video file.
            rule_1_threshold (float, optional): Threshold for rule 1. Defaults to cfg.led.double_check.rule_1.threshold.
            rule_2_threshold (float, optional): Threshold for rule 2. Defaults to cfg.led.double_check.rule_2.threshold.
        """
        columns = ["A", "B", "C", "D"]

        # Read the CSV file into a DataFrame
        df = PostProcessing.read_csv(csv_file, columns)

        # Rule 1: Different max-min angles
        message_info = PostProcessing.check_rule_1(df, columns, rule_1_threshold)
        PostProcessing.log_and_alert(message_info, video_path, is_dev=is_dev)

        # Rule 2: Unusual point
        message_info = PostProcessing.check_rule_2(df, columns, rule_2_threshold)
        PostProcessing.log_and_alert(message_info, video_path, is_dev=is_dev)

        # Rule 3: Black area unusual
        # Perform in end of stage 3.

    @staticmethod
    def stabilize_coordinates_interpolation(
        csv_file: Union[str, pd.DataFrame],
        window_size: int = 30,
        std_thresh: int = 5,
        distance_threshold: int = 10,
    ) -> pd.DataFrame:
        """
        Stabilizes coordinates interpolation in a DataFrame.

        Args:
            csv_file (Union[str, pd.DataFrame]): Path to the CSV file or a DataFrame.
            window_size (int): Size of the window for stabilization.
            std_thresh (int): Standard deviation threshold for stabilization.
            distance_threshold (int): Distance threshold for stabilization.

        Returns:
            pd.DataFrame: Stabilized DataFrame.
        """
        columns = ["A", "B", "C", "D"]

        # Read the CSV file into a DataFrame
        df = PostProcessing.read_csv(csv_file, columns)

        # Stabilize the changing column if the remaining 3 columns are stable
        columns_uniformity_list = [helpers.check_column_uniformity(df, col) for col in columns]
        true_count = sum(columns_uniformity_list)
        if true_count == 3:
            unusual_col = columns[columns_uniformity_list.index(False)]
            first_value = df[unusual_col].iloc[0]
            df[unusual_col] = str(first_value) if isinstance(first_value, list) else first_value
            df = helpers.convert_columns_str_to_list(df, columns)

        for i in range(1, 1 + window_size):
            # Create columns for previous values
            df[[f"A_prev_{i}", f"B_prev_{i}", f"C_prev_{i}", f"D_prev_{i}"]] = df[columns].shift(i)

            for col in columns:
                # Fill missing values with current values
                df[col + f"_prev_{i}"].fillna(df[col], inplace=True)
                # Calculate Euclidean distance between current and previous coordinates
                df[col + f"_diff_{i}"] = df.apply(
                    PostProcessing.calculate_distance,
                    args=(col, col + f"_prev_{i}"),
                    axis=1,
                )

            # Sum the distances for each column
            df[f"diff_{i}"] = df.apply(lambda row: sum(row[col + f"_diff_{i}"] for col in columns), axis=1)

        # Compute standard deviation of the distances
        regex_pattern = r"^diff_\d+$"
        df["diff_std"] = df.filter(regex=regex_pattern).std(axis=1)

        for col in columns:
            # Create stable columns and interpolate values
            df[col + "_stable"] = df[col]

            for index in range(1, len(df)):
                if df.at[index, "diff_std"] < std_thresh and df.at[index - 1, "diff_std"] < std_thresh:
                    df.at[index, col + "_stable"] = df.at[index - 1, col + "_stable"]

        for col in columns:
            # Calculate Euclidean distance between current and stable coordinates
            df[col + "_distance"] = df.apply(PostProcessing.calculate_distance, args=(col, col + "_stable"), axis=1)

        df_stabilized = pd.DataFrame()

        for col in ["frame_id", "corner_missing"]:
            # Copy non-coordinate columns to the stabilized DataFrame
            df_stabilized[col] = df[col]

        # Use stable values if the distance between before and after stabilized < distance_threshold
        for col in columns:
            df_stabilized[col] = df.apply(
                lambda row: (row[col + "_stable"] if row[col + "_distance"] < distance_threshold else row[col]),
                axis=1,
            )

        return df_stabilized

    @staticmethod
    @deprecation.deprecated(details="Details info in issue vsc_30")
    def find_and_replace_consecutive_missing(
        df: pd.DataFrame, corner_missing_recalculate: float, dummy_value: int = -1
    ) -> pd.DataFrame:
        start_index = 0
        for i in range(len(df)):
            if df.iloc[i]["corner_missing"] == corner_missing_recalculate:
                start_index = i
            else:
                print(start_index)
                df.loc[0:start_index, "corner_missing"] = dummy_value
                return df
        # TODO: check the last consecutive missing
        return df

    @staticmethod
    def recalculate_corner_missing(row, index_corner_missing, columns, mask_screen_path):
        _columns = columns[: index_corner_missing - 1] + columns[index_corner_missing:]
        coordinates = [row[corner] for corner in _columns]
        coordinates_ori = ast.literal_eval(row["coordinates_low_epsilon"])
        coordinates_ori = np.array(coordinates_ori)
        mask_screen = helpers.get_frame(mask_screen_path, int(row['frame_id']))
        mask_screen = cv2.cvtColor(mask_screen, cv2.COLOR_BGR2GRAY)
        coordinates = [np.array(c) for c in coordinates]
        corner_missing, _ = Coordinates.find_corner_missing(
            coordinates_ori, coordinates, mask_screen, cfg.led.angle_distortion_threshold - 1
        )
        return corner_missing

    @staticmethod
    def calculate_centroid(coordinates_array: np.ndarray) -> float:
        centroid = np.mean(coordinates_array, axis=0)
        return centroid

    @staticmethod
    def check_screen_orientation(
        df: pd.DataFrame,
        frame: np.ndarray,
        threshold: float = cfg.led.stabilize.screen_moving_threshold,
        columns: list = ["A", "B", "C", "D"],
    ) -> ScreenOrientation:
        screen_start = np.array(df.iloc[0][columns].tolist())
        centroid_start = PostProcessing.calculate_centroid(screen_start)
        screen_end = np.array(df.iloc[-1][columns].tolist())
        centroid_end = PostProcessing.calculate_centroid(screen_end)
        centroid_diff = np.linalg.norm(centroid_start - centroid_end)
        _, width_frame = frame.shape[:2]
        if centroid_diff == 0:
            return ScreenOrientation.STATIC
        if (centroid_diff / width_frame) < threshold:
            return ScreenOrientation.ZOOMED
        return ScreenOrientation.MOVING

    @staticmethod
    def handle_shaking(
        csv_file: Union[str, pd.DataFrame],
        frame: np.ndarray,
        threshold: float = cfg.led.stabilize.screen_moving_threshold,
        mask_screen_path: str = None,
    ) -> pd.DataFrame:
        columns = ["A", "B", "C", "D"]

        # Read the CSV file into a DataFrame
        df = PostProcessing.read_csv(csv_file, columns)

        # Create df to save values processed
        df_processed = df.copy()

        unique_values = set(df["corner_missing"])
        condition_0 = any(
            [len(unique_values) == 2, len(unique_values) == 1 and list(unique_values)[0] in ["A", "B", "C", "D"]]
        )

        condition_2 = PostProcessing.check_screen_orientation(df, frame, threshold) is ScreenOrientation.ZOOMED

        if condition_0 and condition_2:
            logger.info("Recalculate missing corner.")
            # Convert corner missing name to index
            mapping = {value: index + 1 for index, value in enumerate(columns)}
            df['corner_missing'] = df['corner_missing'].fillna(0).replace(mapping)
            corner_missing_max = df['corner_missing'].max()

            df["corner_missing_recalculate"] = df.apply(
                PostProcessing.recalculate_corner_missing,
                args=(corner_missing_max, columns, mask_screen_path),
                axis=1,
            )

            corner_missing_name = columns[corner_missing_max - 1]

            df_processed[corner_missing_name] = df["corner_missing_recalculate"]

            # Re-format
            df_processed[corner_missing_name] = df_processed[corner_missing_name].apply(lambda x: f"[{x[0]}, {x[1]}]")

            return df_processed

        return df

    @staticmethod
    def stabilize_hidden_coordinates(csv_file: Union[str, pd.DataFrame]) -> pd.DataFrame:
        columns = ["A", "B", "C", "D"]

        # Read the CSV file into a DataFrame
        df = PostProcessing.read_csv(csv_file, columns)
        return FindHiddenCoordinatesInterpolation.run(df)

    @staticmethod
    def expanding(coordinates: list, expansion_range: tuple) -> list:
        """
        Expands the coordinates based on a relative factor [0., 1.].

        Args:
            coordinates (list): List of coordinates in the form [x, y].
            expansion_factor (float): a real number for expansion of the processed coordinates. The larger the config, the more it expands

        Returns:
            result (list): List of expanded coordinates.
        """
        assert len(coordinates) == 4, "Number of coordinates must be equal to 4"
        factor_matrix = np.array([[-1, -1], [-1, 1], [1, 1], [1, -1]]) * np.array(expansion_range)
        return (factor_matrix + np.array(coordinates)).tolist()

    @staticmethod
    def shrinking(coordinates: list, factor: int = 10) -> list:
        """
        Shrinks the coordinates based on a specified factor.

        Args:
            coordinates (list): List of coordinates in the form [x, y].
            factor (int, optional): Factor by which the coordinates are to be shrunk. Defaults to 10.

        Returns:
            list: List of shrunken coordinates.
        """
        assert len(coordinates) == 4, "Number of coordinates must be equal to 4"
        factor_matrix = np.array([[-1, -1], [-1, 1], [1, 1], [1, -1]])
        return (np.array(coordinates) - factor_matrix * factor).tolist()

    @staticmethod
    def get_value_in_df(df: pd.DataFrame, col: str, frame_id: int) -> Union[List[int], Any]:
        """
        Retrieve a specific value or a list of values from a pandas DataFrame based on the provided column and frame_id.

        Args:
            df (pd.DataFrame): The pandas DataFrame from which to extract the values.
            col (str): The name of the column to retrieve the value from. If 'coordinates', it returns a list of lists representing the values of columns A, B, C, and D at the given frame_id.
            frame_id (int): The identifier of the frame from which to extract the value.

        Returns:
            Union[List[int], Any]: If 'col' is 'coordinates', it returns a list of lists representing the values of columns A, B, C, and D at the specified 'frame_id'. Otherwise, it returns the value from the specified 'col' at the specified 'frame_id'.

        Notes:
            If 'col' is 'coordinates', the function assumes that the DataFrame has columns named 'A', 'B', 'C', and 'D', and the frame_id column for indexing.

        """
        if col == "coordinates":
            return df[df["frame_id"] == frame_id][["A", "B", "C", "D"]].values.tolist()[0]
        return df.iloc[frame_id][col]

    @staticmethod
    def calculate_angle_difference_quadrilateral(point1, point2, point3, point4):
        vectors = [
            point4 - point1,
            point2 - point1,
            point1 - point2,
            point3 - point2,
            point4 - point3,
            point2 - point3,
            point1 - point4,
            point3 - point4,
        ]

        angles = [
            np.arctan2(
                np.linalg.det([vectors[i], vectors[i + 1]]),
                np.dot(vectors[i], vectors[i + 1]),
            )
            for i in range(0, len(vectors), 2)
        ]

        angles_degrees = np.abs(np.degrees(angles))
        return np.max(angles_degrees) - np.min(angles_degrees)

    @staticmethod
    def validate_coordinates(
        coordinates: np.ndarray, frame_resolution: Union[tuple, list], validation_threshold: float = 10, warning: bool = True
    ):
        if len(coordinates) != 4:
            return

        width, height = frame_resolution
        hidden_edges = [
            coordinates[0][0] == coordinates[1][0] == 0,  # hidden left
            coordinates[1][1] == coordinates[2][1] == height - 1,  # hidden down
            coordinates[2][0] == coordinates[3][0] == width - 1,  # hidden right
            coordinates[3][1] == coordinates[0][1] == 0,  # hidden up
        ]

        allow_larger_difference = any(hidden_edges)
        angle_diff = PostProcessing.calculate_angle_difference_quadrilateral(*map(np.array, coordinates))

        if allow_larger_difference:
            return angle_diff > 65
        if angle_diff > validation_threshold and warning:
            logger.warning(f"Coordinates do not form a rectangle, angle difference is {angle_diff}!")

        return angle_diff > validation_threshold

    @staticmethod
    def handle_unusual_dummy_df(df: Union[pd.DataFrame, str]) -> pd.DataFrame:
        columns = ["A", "B", "C", "D"]

        # Read the CSV file into a DataFrame
        df = PostProcessing.read_csv(df, columns)

        def _replace_abnormal(row):
            def __is_unusual_coordinates(_row):
                return helpers.is_dummy_value(_row[columns].tolist())

            def __format_data(data):
                return f"[{data[0]}, {data[1]}]"

            if 0 < row.name < len(df):
                if (
                    __is_unusual_coordinates(row)
                    and not __is_unusual_coordinates(df.loc[row.name - 1])
                    and not __is_unusual_coordinates(df.loc[row.name + 1])
                ):
                    logger.info(f"Handle unusual dummy coordinates in frame_id: {row.name}")
                    coordinates_prev = PostProcessing.get_value_in_df(df, "coordinates", row.name - 1)
                    coordinates_next = PostProcessing.get_value_in_df(df, "coordinates", row.name + 1)
                    coordinates_mean = np.uint32(np.mean([coordinates_prev, coordinates_next], axis=0))
                    row_processed = df.loc[row.name - 1]
                    for idx, col in enumerate(columns):
                        row_processed[col] = __format_data(coordinates_mean[idx])
                    row_processed["frame_id"] = row.name
                    return row_processed
            return row

        return df.apply(_replace_abnormal, axis=1)

    @staticmethod
    def handle_unusual_dummy(coordinates: np.ndarray, df: pd.DataFrame, frame_id: int) -> np.ndarray:
        """
        Handle unusual dummy coordinates in a specific frame by considering the continuity of frame IDs.

        Args:
            coordinates (np.ndarray): The input coordinates to be processed.
            df (pd.DataFrame): DataFrame containing frame information.
            frame_id (int): The frame ID associated with the input coordinates.

        Returns:
            np.ndarray: Processed coordinates, potentially modified based on continuity conditions.

        Algorithm:
            1. Check if frame IDs within a small range (previous, current, next) exist in the DataFrame.
            2. Retrieve coordinates corresponding to the frame IDs in the DataFrame.
            3. If the input coordinates are a dummy value and the adjacent coordinates are not dummy values,
            replace the input coordinates with the average of adjacent non-dummy coordinates.
        """
        frame_id_continues = [frame_id - 1, frame_id, frame_id + 1]
        if all([x in df["frame_id"] for x in frame_id_continues]):
            coordinates_continues = [
                PostProcessing.get_value_in_df(df, "coordinates", _frame_id) for _frame_id in frame_id_continues
            ]
            if helpers.is_dummy_value(coordinates, cfg.led.dummy_value) and not any(
                [
                    helpers.is_dummy_value(_coordinates, cfg.led.dummy_value)
                    for _coordinates in [
                        coordinates_continues[0],
                        coordinates_continues[-1],
                    ]
                ]
            ):
                """
                TODO: Assert the element in coordinates_continues value is list [[[69, 124], [0, 0], [69, 124]]] not str ["[69, 124]", "[0, 0]", "[69, 124]"]
                """
                coordinates = np.uint32(np.mean([coordinates_continues[0], coordinates_continues[-1]], axis=0))
        return coordinates


class Coordinates:
    """Find 4 corner coordinates of the screen if Timi is on it"""

    coordinates_queue = deque(maxlen=2)
    coordinates_queue_outliers = deque(maxlen=2)

    @staticmethod
    def release():
        """Reset and release resources used by the Coordinates class.

        This method initializes or resets specific variables within the Coordinates class,
        releasing any existing resources and preparing the class for new usage.
        """
        Coordinates.coordinates_queue = deque(maxlen=2)
        Coordinates.coordinates_queue_outliers = deque(maxlen=2)

    @staticmethod
    def find_corner_missing_parallelogram(coordinates: list) -> np.ndarray:
        """
        Find the missing corner based on the given coordinates and frame dimensions.

        Args:
            coordinates (list): List of coordinates representing points.
            frame (np.ndarray): Numpy array representing the frame.

        Returns:
            np.ndarray: Numpy array representing the coordinates of the missing corner.
        """
        assert (
            len(coordinates) == 3
        ), f"Number coordinates muse be equal 3 to using find missing corners parallelogram method, Coordinates given: {coordinates}"
        point1, point2, point3 = map(np.array, coordinates)

        point4_1 = point3[0] + point2[0] - point1[0], point3[1] + point2[1] - point1[1]
        point4_2 = point1[0] + point3[0] - point2[0], point1[1] + point3[1] - point2[1]
        point4_3 = point2[0] + point1[0] - point3[0], point2[1] + point1[1] - point3[1]

        point4_list = [point4_1, point4_2, point4_3]

        angle_diff_list = [
            PostProcessing.calculate_angle_difference_quadrilateral(point1, point2, point3, point4) for point4 in point4_list
        ]

        point4 = point4_list[angle_diff_list.index(min(angle_diff_list))]

        return np.array(point4)

    @staticmethod
    def find_first_quadrant_value(points: list) -> list:
        """
        Find the first quadrant value based on the list of points.

        Args:
            points (list): List of points.

        Returns:
            list: List representing the first quadrant value.
        """
        points = np.array(points)
        distances = np.linalg.norm(points, axis=1)  # List Euclidean distance of each point from the origin (0, 0)
        closest_index = np.argmin(distances)
        closest_point = points[closest_index]
        return closest_point.tolist()

    @staticmethod
    def sort_coordinates(coordinates: Union[np.ndarray, list]) -> np.ndarray:
        """
        Sort the coordinates in a counter-clockwise direction.

        Args:
            coordinates (np.ndarray or list): Array or list of coordinates.

        Returns:
            np.ndarray: Numpy array representing the sorted coordinates in a counter-clockwise direction.
        """
        # TODO: Support data type of coordinates is np.ndarray.
        assert len(coordinates) > 0, "Number of coordinates must be greater than 0"
        if len(coordinates) == 1:
            return coordinates

        centroid = [
            sum(x[0] for x in coordinates) / len(coordinates),
            sum(x[1] for x in coordinates) / len(coordinates),
        ]

        sorted_coordinates = sorted(
            coordinates,
            key=lambda x: (math.atan2(x[1] - centroid[1], x[0] - centroid[0]) + 2 * math.pi) % (2 * math.pi),
            reverse=True,
        )

        first_quadrant_value = Coordinates.find_first_quadrant_value(sorted_coordinates)

        # Convert numpy array to list to using method .index()
        sorted_coordinates = [value.tolist() for value in sorted_coordinates]

        first_quadrant_index = sorted_coordinates.index(first_quadrant_value)

        sorted_coordinates = sorted_coordinates[first_quadrant_index:] + sorted_coordinates[:first_quadrant_index]

        return np.array(sorted_coordinates)

    @staticmethod
    def get_bbox(results: np.ndarray, label: str, classes_name: list) -> list:
        """
        Get the bounding box based on the label from the results.

        Args:
            results (np.ndarray): Numpy array of results.
            label (str): Label for the bounding box.
            classes_name (list): Class names for the bounding box

        Returns:
            list: List representing the bounding box.
        """
        assert label in classes_name, f"label must be in list {classes_name}"
        bbox = results[np.where(results[:, 4] == classes_name.index(label))]
        bbox = np.float32(bbox).tolist()
        return bbox

    @staticmethod
    def stabilize_coordinates(coordinates: np.ndarray, threshold: int = 20) -> list:
        """
        Stabilize the coordinates based on the coordinates frame before and threshold value.

        Args:
            coordinates (np.ndarray): Numpy array representing the coordinates.
            threshold (int, optional): Threshold value. Default is 20.

        Returns:
            list: List representing the stabilized coordinates.
        """
        coordinates = [list(item) for item in coordinates]
        Coordinates.coordinates_queue.append(coordinates)
        if len(Coordinates.coordinates_queue) == 1:
            return [np.array(item) for item in coordinates]

        first_element = Coordinates.coordinates_queue[0]
        second_element = Coordinates.coordinates_queue[1]

        total_distance = 0
        num_of_corners = min(len(first_element), len(second_element))
        for i in range(num_of_corners):
            total_distance += math.sqrt(
                (first_element[i][0] - second_element[i][0]) ** 2 + (first_element[i][1] - second_element[i][1]) ** 2
            )

        Coordinates.coordinates_queue.pop()
        result = first_element if total_distance < threshold else second_element
        return [np.array(item) for item in result]

    @staticmethod
    def detect_outliers(distance_list: list, threshold_noise: int = 6) -> bool:
        """
        Detect outliers based on the distance list and the threshold noise.

        Args:
            distance_list (list): List of distances.
            threshold_noise (int, optional): Threshold value for noise. Default is 6.

        Returns:
            bool: True if outliers are detected, False otherwise.
        """
        assert len(distance_list) == 4, f"distance_list expected 4 element but given {distance_list}"
        if distance_list.count(0) != 3:
            # Normalize to handle case: [0, 1, 9, 0]
            distance_list = [max(value - threshold_noise, 0) for value in distance_list]
        return distance_list.count(0) == 3

    @staticmethod
    def handle_outliers(coordinates: np.ndarray) -> np.ndarray:
        """
        Handle outliers based on the given coordinates.

        Args:
            coordinates (np.ndarray): Numpy array representing the coordinates.

        Returns:
            np.ndarray: Numpy array representing the coordinates after handling outliers.
        """
        coordinates = [list(item) for item in coordinates]
        Coordinates.coordinates_queue_outliers.append(coordinates)
        if len(Coordinates.coordinates_queue_outliers) == 1:
            return np.array(coordinates)

        first_element = Coordinates.coordinates_queue_outliers[0]
        second_element = Coordinates.coordinates_queue_outliers[1]
        if len(first_element) != len(second_element):
            return np.array(coordinates)

        distance_list = []
        for i in range(len(first_element)):
            distance = math.sqrt(
                (first_element[i][0] - second_element[i][0]) ** 2 + (first_element[i][1] - second_element[i][1]) ** 2
            )
            distance_list.append(distance)

        Coordinates.coordinates_queue_outliers.pop()
        result = first_element if Coordinates.detect_outliers(distance_list) else second_element
        return np.array(result)

    @staticmethod
    def distance_point_to_line(point: Union[tuple, list, np.ndarray], line_coeffs: tuple):
        W, C = line_coeffs
        if not isinstance(point, np.ndarray):
            point = np.array(point)
        return (W.dot(point) + C) / np.linalg.norm(W)

    @staticmethod
    def cal_distance(point1: Union[list, tuple, np.array], point2: Union[list, tuple, np.array]) -> float:
        """
        Calculate the Euclidean distance between two points.

        Args:
            point1 (list): List representing the first point.
            point2 (list): List representing the second point.

        Returns:
            float: Euclidean distance between the two points.
        """
        return ((point1[0] - point2[0]) ** 2 + (point1[1] - point2[1]) ** 2) ** 0.5

    @staticmethod
    def calculate_largest_angle(coordinates: Union[np.array, list]) -> tuple:
        """
        Calculate the largest angle among three points and determine the point associated with the largest angle.

        Args:
            coordinates (list): A list of three points, each represented as [x, y].

        Returns:
            tuple: A tuple containing the largest angle (in degrees) and the identifier of the point associated with
                   the largest angle ("A" or "D").
        """
        assert len(coordinates) == 3, "Number of coordinates should be three"
        # TODO:
        #  - Sort coordinates before process
        #  - Support the largest angle B or C

        point1, point2, point3 = map(np.array, coordinates)

        vectors = [
            point3 - point1,
            point2 - point1,
            point1 - point2,
            point3 - point2,
            point1 - point3,
            point2 - point3,
        ]

        angles = [
            np.arctan2(
                np.linalg.det([vectors[i], vectors[i + 1]]),
                np.dot(vectors[i], vectors[i + 1]),
            )
            for i in range(0, len(vectors), 2)
        ]

        angles_degrees = np.abs(np.degrees(angles))
        largest_angle_index = np.argmax(angles_degrees)

        return angles_degrees[largest_angle_index], ("A" if largest_angle_index == 0 else "D")

    @staticmethod
    def find_corner_missing_intersection(
        point1_1: list, point1_2: list, point2_1: list, point2_2: list
    ) -> Union[np.ndarray, None]:
        """
        Find the intersection point of two line segments defined by their endpoints.

        Args:
            point1_1 (list): The coordinates of the first endpoint of the first line segment [x, y].
            point1_2 (list): The coordinates of the second endpoint of the first line segment [x, y].
            point2_1 (list): The coordinates of the first endpoint of the second line segment [x, y].
            point2_2 (list): The coordinates of the second endpoint of the second line segment [x, y].

        Returns:
            np.ndarray: The intersection point [x, y] of the two line segments.

        Raises:
            AssertionError: If the determinant (det) is equal to 0, indicating parallel lines and no unique intersection.
        """

        x1, y1 = point1_1
        x2, y2 = point1_2
        x3, y3 = point2_1
        x4, y4 = point2_2

        det = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if det == 0:
            logger.warning("The determinant must be != 0. Can not find corner missing with intersection method.")
            return

        intersection_x = ((x1 * y2 - y1 * x2) * (x3 - x4) - (x1 - x2) * (x3 * y4 - y3 * x4)) / det
        intersection_y = ((x1 * y2 - y1 * x2) * (y3 - y4) - (y1 - y2) * (x3 * y4 - y3 * x4)) / det

        return np.int32(np.round(np.array([intersection_x, intersection_y])))

    @staticmethod
    def get_point_on_timi(coordinates: np.ndarray, points_inside_rectangles: list[np.ndarray]) -> list:
        """
        Get the points on the given coordinates that are not present in the specified list of points_inside_rectangles.

        Args:
            coordinates (np.ndarray): The array containing coordinates.
            points_inside_rectangles (list): List of points inside rectangles.

        Returns:
            list: List of points on coordinates not present in points_inside_rectangles.
        """

        p_temp = [p.tolist() for p in points_inside_rectangles]
        return [point for point in coordinates.tolist() if point not in p_temp]

    @staticmethod
    def find_nearest_point(
        points: list,
        point: list,
        parallel_line: int,
        axis_name: str,
        distance_noise: float = cfg.led.post_processing.distance_noise,
    ) -> np.ndarray:
        """
        Find the point in the given list of points that is nearest to the specified parallel line along the given axis.

        Args:
            points (list): List of points.
            parallel_line (int): The coordinate value of the parallel line.
            axis_name (str): The axis name, either "x" or "y".

        Returns:
            np.ndarray: The point in the list nearest to the parallel line along the specified axis.
        """

        axis = 0 if axis_name == "x" else 1
        points = [p for p in points if Coordinates.cal_distance(p, point) > distance_noise]
        points = np.array(points)

        distances = np.abs(points[:, axis] - parallel_line)
        nearest_point_index = np.argmin(distances)
        nearest_point = points[nearest_point_index]
        return nearest_point

    @staticmethod
    def get_corner_on_mask(mask: np.ndarray, epsilon: float = 0.005) -> list:
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        area_list = [cv2.contourArea(contour) for contour in contours]

        if len(area_list) == 0:
            return

        largest_area_index = area_list.index(max(area_list))

        coordinates = cv2.approxPolyDP(
            contours[largest_area_index],
            epsilon * cv2.arcLength(contours[largest_area_index], True),
            True,
        )  # counter-clockwise

        return np.squeeze(coordinates, axis=1)  # Reshape from (4, 1, 2) to (4, 2)

    @staticmethod
    def create_sub_points(
        coordinates: np.ndarray,
        mask_screen: np.ndarray,
        sub_point: list,
        coordinates_validated: list[np.ndarray],
        edge_factor: float = 0.25,
    ) -> list:

        corners = Coordinates.get_corner_on_mask(mask_screen)
        coordinates_noise = Coordinates.get_point_on_timi(np.array(corners), coordinates_validated)

        if len(coordinates_noise) == 1:
            # TODO: create a separate function
            x_diff = coordinates[:, 0][:, np.newaxis] - coordinates[:, 0]
            y_diff = coordinates[:, 1][:, np.newaxis] - coordinates[:, 1]
            distances = np.sqrt(x_diff**2 + y_diff**2)
            np.fill_diagonal(distances, np.inf)  # Remove distance between the same point
            height_screen = np.min(distances)
            edge = edge_factor * height_screen
            sub_point = sub_point[0]
            square_coordinates = np.array(
                [
                    [sub_point[0] - edge, sub_point[1] - edge],
                    [sub_point[0] - edge, sub_point[1] + edge],
                    [sub_point[0] + edge, sub_point[1] + edge],
                    [sub_point[0] + edge, sub_point[1] - edge],
                ],
                dtype=np.int32,
            )

            square_coordinates = square_coordinates.reshape((-1, 1, 2))

            mask_screen_temp = mask_screen.copy()
            cv2.fillPoly(mask_screen_temp, [square_coordinates], (0, 0, 0))

            corners = Coordinates.get_corner_on_mask(mask_screen_temp)
            corners = corners.tolist()
            corners.append(sub_point)

            coordinates_noise = Coordinates.get_point_on_timi(np.array(corners), coordinates_validated)

        return coordinates_noise

    @staticmethod
    def find_corner_missing(
        coordinates: np.ndarray,
        points_inside_rectangles: list[np.ndarray],
        mask_screen: np.ndarray,
        angle_distortion_threshold: float = cfg.led.angle_distortion_threshold,
    ) -> tuple[np.ndarray, str]:
        """
        Find the missing corner point in a set of coordinates representing a rectangle.

        Args:
            coordinates (np.ndarray): The original coordinates of the rectangle, each row represented as [x, y].
            points_inside_rectangles (list): A list of points inside the rectangle, each represented as [x, y].

        Returns:
            list: A list of stabilized coordinates representing the rectangle with the missing corner point.

        Note:
            This function assumes that the given coordinates form a rectangle and attempts to find the missing corner
            point based on the distortion angle and stabilization thresholds.

        References:
            - Coordinates.calculate_largest_angle
            - Coordinates.stabilize_coordinates
            - Coordinates.find_nearest_point
            - Coordinates.find_corner_missing_intersection
            - Coordinates.find_corner_missing_parallelogram
        """
        assert (
            len(points_inside_rectangles) == 3
        ), "For the missing corner case, the number of points_inside_rectangles must be exactly 3."
        coordinates_noise = Coordinates.get_point_on_timi(coordinates, points_inside_rectangles)

        largest_angle, largest_point = Coordinates.calculate_largest_angle(points_inside_rectangles)

        corner_missing_name = "C" if largest_point == "A" else "B"

        if largest_angle < angle_distortion_threshold and len(coordinates_noise) > 0:
            if len(coordinates_noise) == 1:
                coordinates_noise = Coordinates.create_sub_points(
                    coordinates, mask_screen, coordinates_noise, points_inside_rectangles
                )
            if len(coordinates_noise) == 1:
                logger.warning(f"Sub-points creation failed. Coordinates_noise: {coordinates_noise}")
                corner_missing = Coordinates.find_corner_missing_parallelogram(points_inside_rectangles)
            else:
                # Screen is distorted (Case: Intro - Camera moving)
                if largest_point == "A":
                    B = points_inside_rectangles[1]
                    b_sub = Coordinates.find_nearest_point(coordinates_noise, B, B[1], "y")
                    D = points_inside_rectangles[2]
                    d_sub = Coordinates.find_nearest_point(coordinates_noise, D, D[0], "x")
                    corner_missing = Coordinates.find_corner_missing_intersection(B, b_sub, D, d_sub)
                else:
                    # Largest_point == "D"
                    A = points_inside_rectangles[0]
                    a_sub = Coordinates.find_nearest_point(coordinates_noise, A, A[0], "x")
                    C = points_inside_rectangles[1]
                    c_sub = Coordinates.find_nearest_point(coordinates_noise, C, C[1], "y")
                    corner_missing = Coordinates.find_corner_missing_intersection(A, a_sub, C, c_sub)
                if corner_missing is None:
                    logger.warning("Determinant is zero. Finding the missing corner using the parallelogram method.")
                    corner_missing = Coordinates.find_corner_missing_parallelogram(points_inside_rectangles)
        else:
            corner_missing = Coordinates.find_corner_missing_parallelogram(points_inside_rectangles)

        return corner_missing, corner_missing_name

    @staticmethod
    def validate_coordinate_of_screen(
        coordinates_ori: np.ndarray, coordinates_validated: list, mask_screen: np.ndarray
    ) -> tuple:
        """
        Validates and stabilizes a set of coordinates representing a screen.

        Args:
            coordinates_ori (np.ndarray): The original coordinates representing the screen.
            coordinates_validated (list): A list of points inside the screen, each represented as [x, y].
            mask_screen (np.ndarray): A mask representing the screen area.

        Returns:
            tuple: A tuple containing:
                - A list of stabilized coordinates representing the screen, including the missing corner point.
                - The name of the missing corner point, if applicable.

        Note:
            This function assumes that the given coordinates form a screen and attempts to validate and stabilize
            the coordinates by sorting, stabilizing, and adding the missing corner point. It handles cases where
            the camera is stationary by stabilizing the coordinates using a stable threshold.

        References:
            - Coordinates.sort_coordinates_clockwise
            - Coordinates.stabilize_coordinates
            - Coordinates.find_corner_missing
        """
        corner_missing_name = None
        if len(coordinates_validated) != 3:
            return coordinates_validated, corner_missing_name

        coordinates_validated = Coordinates.sort_coordinates(coordinates_validated)

        # Stabilize coordinates (Only works when the camera is stationary)
        coordinates_validated = Coordinates.stabilize_coordinates(
            coordinates_validated, cfg.led.post_processing.stable_threshold
        )

        corner_missing, corner_missing_name = Coordinates.find_corner_missing(
            coordinates_ori, coordinates_validated, mask_screen
        )
        coordinates_validated.append(corner_missing)

        return coordinates_validated, corner_missing_name

    @staticmethod
    def cal_area(coordinates: np.ndarray) -> float:
        coordinates = Coordinates.sort_coordinates(coordinates)

        # Calculate the area using the shoelace formula
        x = coordinates[:, 0]
        y = coordinates[:, 1]
        area = 0.5 * np.abs(np.dot(x, np.roll(y, 1)) - np.dot(y, np.roll(x, 1)))

        return area

    @staticmethod
    def get_coordinates(
        frame: np.ndarray,
        mask_screen: np.ndarray,
        coordinates_ori: np.ndarray,
        coordinates_validated: list[np.ndarray],
        is_dev: bool = True,
    ) -> tuple[np.ndarray, str]:
        """
        Get the coordinates based on the frame and the given coordinates when Timi on the screen.

        Args:
            frame (np.ndarray): Numpy array representing the frame.
            coordinates_validated (list): A list of points inside the screen, each represented as [x, y].
            is_dev (bool, optional): Boolean flag for development. Default is True.

        Returns:
            np.ndarray: Numpy array representing the obtained coordinates.
        """
        # Find and return full coordinates if has corner missing
        coordinates_full, corner_missing = Coordinates.validate_coordinate_of_screen(
            coordinates_ori, coordinates_validated, mask_screen
        )

        if is_dev:
            Draw.visualize_point(frame, coordinates_full, (0, 0, 100), "find_missing")

        coordinates_sorted = Coordinates.sort_coordinates(coordinates_full)
        return coordinates_sorted, corner_missing

    @staticmethod
    def line_from_points(p1: Union[tuple, list, np.ndarray], p2: Union[tuple, list, np.ndarray]):
        x1, y1 = p1
        x2, y2 = p2
        k1 = y2 - y1
        k2 = -(x2 - x1)

        C = -(k1 * x1 + k2 * y1)
        W = np.array([k1, k2])
        return W, C

    @staticmethod
    def fittest_expanding(coordinates: Union[list, tuple, np.ndarray], contour_points: np.ndarray):
        A_coord, B_coord, C_coord, D_coord = coordinates
        W_AB, C_AB = Coordinates.line_from_points(A_coord, B_coord)
        W_BC, C_BC = Coordinates.line_from_points(B_coord, C_coord)
        W_CD, C_CD = Coordinates.line_from_points(C_coord, D_coord)
        W_DA, C_DA = Coordinates.line_from_points(D_coord, A_coord)

        W_vector = np.stack([W_AB, W_BC, W_CD, W_DA], -1)
        C_vector = np.array([C_AB, C_BC, C_CD, C_DA]).reshape(-1, 4)

        AB_closest_point, BC_closest_point, CD_closest_point, DA_closest_point = contour_points[
            ((contour_points.dot(W_vector) + C_vector) / np.linalg.norm(W_vector, axis=0).reshape(-1, 4)).argmin(0)
        ]

        new_C_DA = -W_DA.dot(DA_closest_point) + 2.0 * np.linalg.norm(W_DA)
        new_C_AB = -W_AB.dot(AB_closest_point) + 2.0 * np.linalg.norm(W_AB)
        new_C_BC = -W_BC.dot(BC_closest_point) + 2.0 * np.linalg.norm(W_BC)
        new_C_CD = -W_CD.dot(CD_closest_point) + 2.0 * np.linalg.norm(W_CD)

        expanded_A_coord = Coordinates.find_intersection((W_DA, new_C_DA), (W_AB, new_C_AB))
        expanded_B_coord = Coordinates.find_intersection((W_AB, new_C_AB), (W_BC, new_C_BC))
        expanded_C_coord = Coordinates.find_intersection((W_BC, new_C_BC), (W_CD, new_C_CD))
        expanded_D_coord = Coordinates.find_intersection((W_CD, new_C_CD), (W_DA, new_C_DA))

        return expanded_A_coord, expanded_B_coord, expanded_C_coord, expanded_D_coord

    @staticmethod
    def find_expansion_ranges(coordinates: Union[list, tuple, np.ndarray], contour_points: np.ndarray):
        fitted_coords = Coordinates.fittest_expanding(coordinates, contour_points)
        diff = np.abs(np.array(coordinates) - np.array(fitted_coords))
        expansion_width, expansion_height = np.max(diff, axis=0)
        return fitted_coords, expansion_width, expansion_height

    @staticmethod
    def find_intersection(line1_coeffs, line2_coeffs):
        """
        Finds the intersection point of two lines given their equations.

        Parameters:
            line1_coeffs (tuple): Coefficients (k1, k2, C) of the first line.
            line2_coeffs (tuple): Coefficients (m1, m2, D) of the second line.

        Returns:
            tuple: Coordinates of the intersection point (x, y), or None if lines are parallel.
        """
        W1, C1 = line1_coeffs
        W2, C2 = line2_coeffs

        # Create the coefficient matrix for the system of equations
        A = np.array([W1, W2])

        # Create the constant matrix
        B = np.array([-C1, -C2])

        try:
            # Solve the system using numpy's linear solver
            intersection_point = np.linalg.solve(A, B)
            return tuple(intersection_point)
        except np.linalg.LinAlgError:
            # This exception is raised if the matrix is singular (i.e., lines are parallel)
            return None


class FindPoint:
    @staticmethod
    def based_on_length_and_direction(A: list, C: list, D: list, d: Union[float, int]) -> list:
        """
        Find the coordinate of point B such that:
            - AB is parallel to CD
            - ||AB|| = d

        Parameters:
            A (List[float]): Coordinates of point A (x_a, y_a).
            C (List[float]): Coordinates of point C (x_c, y_c).
            D (List[float]): Coordinates of point D (x_d, y_d).
            d (Union[float, int]): Length of AB.

        Returns:
            List[float]: Coordinates of point B (x_b, y_b).

        Formula Description:
            Vector CD: CD = (x_d - x_c, y_d - y_c)
            Length of CD: ||CD|| = sqrt((x_d - x_c)^2 + (y_d - y_c)^2)
            Unit vector v parallel to CD: v = CD / ||CD||
            Vector AB with length d and parallel to CD: AB = d * v
            Coordinates of point B: B(x_a + AB_x, y_a + AB_y)
        """
        # Vector CD
        CD = (D[0] - C[0], D[1] - C[1])  # CD = (x_d - x_c, y_d - y_c)

        # Length of CD
        length_CD = max(np.linalg.norm(CD), 1e-7)  # ||CD|| = sqrt((x_d - x_c)^2 + (y_d - y_c)^2)

        # Unit vector v parallel to CD
        v = (CD[0] / length_CD, CD[1] / length_CD)  # v = CD / ||CD||

        # Vector AB with length d and parallel to CD
        AB = (d * v[0], d * v[1])  # AB = d * v

        # Coordinates of point B
        B = [int(A[0] + AB[0]), int(A[1] + AB[1])]  # B(x_c + AB_x, y_c + AB_y)

        return B


class FindHiddenCoordinatesInterpolation:
    @staticmethod
    def is_consecutive(frame_ids):
        return all(x == y - 1 for x, y in zip(frame_ids[:-1], frame_ids[1:]))

    @staticmethod
    def is_consecutive_dataframe(df):
        hidden_left_values = df["frame_id"]
        if hidden_left_values.empty:
            return False
        frame_ids = hidden_left_values.sort_values().tolist()
        return FindHiddenCoordinatesInterpolation.is_consecutive(frame_ids)

    @staticmethod
    def get_ratio_for_hiding(df, df_hidden) -> bool:
        """
        - id_start > 0.
        - is_dummy_data(coordinates[frame_id = id_start] - 1) --> False.
        """
        frame_id_start = df_hidden.iloc[0]["frame_id"]

        if frame_id_start == 0:
            return None

        coordinates_frame_before_hide = df[df["frame_id"] == frame_id_start - 1][["A", "B", "C", "D"]].to_numpy().tolist()[0]

        if helpers.is_dummy_value(coordinates_frame_before_hide):
            return None

        width_screen, height_screen = helpers.get_dimension(coordinates_frame_before_hide)

        return width_screen / height_screen

    @staticmethod
    def get_ratio_for_appearing(df, df_hidden) -> bool:
        """
        - id_stop < total_frame
        - is_dummy_data(coordinates[frame_id = id_end] + 1) --> False.

        """
        frame_id_end = df_hidden.iloc[-1]["frame_id"]
        if frame_id_end == len(df) - 1:
            return None

        coordinates_frame_after_hide = df[df["frame_id"] == frame_id_end + 1][["A", "B", "C", "D"]].to_numpy().tolist()[0]

        if helpers.is_dummy_value(coordinates_frame_after_hide):
            return None

        width_screen, height_screen = helpers.get_dimension(coordinates_frame_after_hide)

        return width_screen / height_screen

    @staticmethod
    def get_ratio(df, df_hidden, ratio_default=16 / 9):
        ratio = FindHiddenCoordinatesInterpolation.get_ratio_for_appearing(df, df_hidden)
        if ratio is None:
            ratio = FindHiddenCoordinatesInterpolation.get_ratio_for_hiding(df, df_hidden)
        if ratio is None:
            ratio = ratio_default
        return ratio

    @staticmethod
    def recalculate_hidden_coordinates(row):
        columns = ["A", "B", "C", "D"]
        coordinates = np.array([row[corner] for corner in columns])
        ratio = row["ratio_hidden"]
        hidden_name = row["hidden_name"]

        if np.isnan(ratio):
            return

        _, height_screen = helpers.get_dimension(coordinates)
        width_actual = int(ratio * height_screen)

        if hidden_name == "left":
            coordinates[0] = FindPoint.based_on_length_and_direction(
                coordinates[3], coordinates[3], coordinates[0], width_actual
            )
            coordinates[1] = FindPoint.based_on_length_and_direction(
                coordinates[2], coordinates[2], coordinates[1], width_actual
            )
        if hidden_name == "right":
            coordinates[2] = FindPoint.based_on_length_and_direction(
                coordinates[1], coordinates[1], coordinates[2], width_actual
            )
            coordinates[3] = FindPoint.based_on_length_and_direction(
                coordinates[0], coordinates[0], coordinates[3], width_actual
            )

        return coordinates

    @staticmethod
    def replace_hidden_value(row):
        coordinates_hidden = row["coordinates_hidden"]
        if coordinates_hidden is None:
            return row
        if row['hidden_name'] == 'left':
            row["A"] = coordinates_hidden[0]
            row["B"] = coordinates_hidden[1]
        if row['hidden_name'] == 'right':
            row["C"] = coordinates_hidden[2]
            row["D"] = coordinates_hidden[3]
        return row

    @staticmethod
    def run(df: pd.DataFrame) -> pd.DataFrame:
        df_hidden_left = df[df["hidden_name"] == "left"]
        df_hidden_right = df[df["hidden_name"] == "right"]

        if len(df_hidden_left) > 0 and FindHiddenCoordinatesInterpolation.is_consecutive_dataframe(df_hidden_left):
            ratio_hidden_left = FindHiddenCoordinatesInterpolation.get_ratio(df, df_hidden_left)
            df.loc[df["hidden_name"] == "left", "ratio_hidden"] = ratio_hidden_left
            df["coordinates_hidden"] = df.apply(FindHiddenCoordinatesInterpolation.recalculate_hidden_coordinates, axis=1)
            df.to_csv("debug.csv", index=False)
            df = df.apply(FindHiddenCoordinatesInterpolation.replace_hidden_value, axis=1)

        if len(df_hidden_right) > 0 and FindHiddenCoordinatesInterpolation.is_consecutive_dataframe(df_hidden_right):
            ratio_hidden_right = FindHiddenCoordinatesInterpolation.get_ratio(df, df_hidden_right)
            df.loc[df["hidden_name"] == "right", "ratio_hidden"] = ratio_hidden_right
            df["coordinates_hidden"] = df.apply(FindHiddenCoordinatesInterpolation.recalculate_hidden_coordinates, axis=1)
            df = df.apply(FindHiddenCoordinatesInterpolation.replace_hidden_value, axis=1)

        return df
