import numpy as np
from codetiming import Timer
from numba import njit

from vsc.utils.logger import logger_perf


@njit(fastmath=True)
def blend(foreground: np.ndarray, background: np.ndarray, alpha: np.ndarray) -> np.ndarray:
    assert foreground.shape == background.shape, "Foreground and background images must have the same shape."
    assert foreground.shape[2] in [
        1,
        3,
    ], "Channel Foreground and background must be equal 1 or 3."
    assert alpha.shape[:2] == foreground.shape[:2], "Alpha mask must have the same dimension as the foreground image."
    return alpha * foreground + (1 - alpha) * background


@Timer("simple_composite.composite_image", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
def composite_image(foreground: np.ndarray, background: np.ndarray, alpha: np.ndarray) -> np.ndarray:
    """
    Composite two images using an alpha mask.

    Args:
        foreground (np.ndarray): The foreground image, with shape (height, width, channels).
        background (np.ndarray): The background image, with the same shape as the foreground.
        alpha (np.ndarray): The alpha mask, with shape (height, width) or (height, width, channels).

    Returns:
        numpy.ndarray: The composite image, with the same shape as the input images.

    Raises:
        AssertionError: If the shapes of the foreground and background images are not the same.
        AssertionError: If the number of channels in the foreground and background images is not 1 or 3.
        AssertionError: If the shapes of the input images and alpha mask are incompatible.

    The function composites the foreground over the background using the provided alpha mask.
    The alpha values control the transparency of the foreground, and the formula for composition is:

        composite = foreground * alpha + background * (1 - alpha)

    The resulting composite image is converted to an unsigned 8-bit integer format compatible with OpenCV (cv2).
    If alpha values are in the range [0, 255], they are normalized to the range [0, 1] before compositing.

    """
    # Check shape compatibility
    assert foreground.shape == background.shape, "Foreground and background images must have the same shape."
    assert foreground.shape[2] in [
        1,
        3,
    ], "Channel Foreground and background must be equal 1 or 3."
    assert alpha.shape[:2] == foreground.shape[:2], "Alpha mask must have the same dimension as the foreground image."

    # Ensure alpha is in the range [0, 1]
    if alpha.max() > 1:
        alpha = alpha / 255.0

    if alpha.ndim == 2:
        alpha = alpha[:, :, None]

    composite = blend(foreground, background, alpha)

    return composite.astype(np.uint8)


if __name__ == "__main__":
    fg1 = np.random.randint(0, 256, size=(256, 256, 1), dtype=np.uint8)
    bg1 = np.random.randint(0, 256, size=(256, 256, 1), dtype=np.uint8)
    fg2 = np.random.randint(0, 256, size=(256, 256, 3), dtype=np.uint8)
    bg2 = np.random.randint(0, 256, size=(256, 256, 3), dtype=np.uint8)
    mask1 = np.random.rand(256, 256, 3)
    mask2 = np.random.rand(256, 256, 1)
    mask3 = np.random.rand(256, 256)

    for factor in [1, 255]:
        for fg, bg in zip([fg1, fg2], [bg1, bg2]):
            for mask in [mask1, mask2, mask3]:
                composite_image(fg, bg, mask * factor)
