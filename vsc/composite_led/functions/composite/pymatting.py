"""
Mar 7, 2023

Author: <PERSON><PERSON> TRAN

For video composition
"""

from concurrent.futures import ThreadPoolExecutor

import numpy as np
import pymatting
from codetiming import Timer

from vsc.composite_led.functions.composite.simple_composite import blend
from vsc.utils.logger import logger_perf
from vsc.utils.system_utils import cuda_is_available


def pre_processing_input_single(img: np.ndarray):
    assert isinstance(img, np.ndarray), "Input must be np.ndarray."
    if img.max() > 1:
        img = img / 255
    if img.ndim == 3 and img.shape[2] == 1:
        img = np.squeeze(img)
    return img


@Timer("pymatting.pre_processing_input", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
def pre_processing_input(*args):
    with ThreadPoolExecutor() as executor:
        output = list(executor.map(pre_processing_input_single, args))
    return output


@Timer("pymatting.composite_with_pymatting", "{name}: {milliseconds:.2f} ms", logger=logger_perf.debug)
def composite_with_pymatting(fg: np.ndarray, bg: np.ndarray, mask: np.ndarray, blending_mode: str = "pymatting") -> np.ndarray:
    """Composite by using pymatting lib."""
    fg, bg, mask = pre_processing_input(fg, bg, mask)

    if blending_mode == "cupy" and cuda_is_available():
        from vsc.composite_led.functions.composite.mode import cupy

        foreground = cupy.estimate_foreground_ml_cupy(
            fg, mask, regularization=1e-5, n_small_iterations=20, n_big_iterations=5, small_size=64
        )
    else:
        foreground = pymatting.estimate_foreground_ml(fg, mask)
        logger_perf.info("Mode: Pymatting")

    if len(mask.shape) == 2:
        mask = mask[:, :, np.newaxis]
    comp = blend(foreground, bg, mask)

    return (comp * 255).astype(np.uint8)
