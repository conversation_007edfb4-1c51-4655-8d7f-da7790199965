from typing import Any, Union

from vsc.composite_led.base import BaseVideoWriter
from vsc.composite_led.datastruct import VideoProcessingMode
from vsc.composite_led.functions import helpers


class VideoWriterOpenCV(BaseVideoWriter):
    def create_writer(self, writer_path: str, cap: Any) -> Any:
        """Create an OpenCV video writer.

        Args:
            writer_path (str): The path to save the video.
            cap (Any): The video capture object.

        Returns:
            Any: The video writer object.
        """
        return helpers.create_writer(writer_path, cap)


class VideoWriterFFMPEG(BaseVideoWriter):
    def create_writer(self, writer_path: str, video_properties: dict) -> Any:
        """Create an FFMPEG video writer.

        Args:
            writer_path (str): The path to save the video.
            video_properties (dict): Properties of the video.

        Returns:
            Any: The video writer object.
        """
        return helpers.create_writer_ffmpeg(writer_path, video_properties)


class VideoWriterProcessor:
    def __init__(self) -> None:
        pass

    def create_writer_factory(
        self, video_type: Union[str, VideoProcessingMode]
    ) -> Union[VideoWriterOpenCV, VideoWriterFFMPEG]:
        """Create a video writer based on the video type.

        Args:
            video_type (Union[str, VideoProcessingMode]): The type of video processing mode.

        Returns:
            Union[VideoWriterOpenCV, VideoWriterFFMPEG]: An instance of a video writer.

        Raises:
            ValueError: If an invalid video type is provided.
        """
        if video_type == VideoProcessingMode.CV2:
            return VideoWriterOpenCV()
        elif video_type == VideoProcessingMode.FFMPEG:
            return VideoWriterFFMPEG()
        else:
            raise ValueError("Invalid video type")

    def create_writer(self, writer_path: str, mode: Union[str, VideoProcessingMode], property: Any) -> Any:
        """Create a video writer based on the mode.

        Args:
            writer_path (str): The path to save the video.
            mode (Union[str, VideoProcessingMode]): The video processing mode.
            property (Any): The property of the video.

        Returns:
            Any: The video writer object.
        """
        writer = self.create_writer_factory(mode)
        return writer.create_writer(writer_path, property)

    def write(self, writer_object: Any, mode: Union[str, VideoProcessingMode], img: Any) -> None:
        """Write an image to the video.

        Args:
            writer_object (Any): The video writer object.
            mode (Union[str, VideoProcessingMode]): The video processing mode.
            img (Any): The image to be written to the video.
        """
        writer = self.create_writer_factory(mode)
        return writer.write(writer_object, img)

    def release(self, mode: Union[str, VideoProcessingMode], *args: Any) -> None:
        """Release the resources associated with the video writer.

        Args:
            mode (Union[str, VideoProcessingMode]): The video processing mode.
            *args (Any): Variable length argument list.
        """
        writer = self.create_writer_factory(mode)
        writer.release(*args)
