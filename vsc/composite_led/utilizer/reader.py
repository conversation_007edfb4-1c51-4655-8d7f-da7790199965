import os
from typing import List, Union

import cv2
import numpy as np
from fvutils.videoio import FVideoCapture

from vsc.composite_led.base import BaseVideoReader
from vsc.composite_led.datastruct import VideoProcessingMode, VideoPropeties


class VideoReaderOpenCV(BaseVideoReader):
    def read(self, video_path: str) -> cv2.VideoCapture:
        """Read the video using OpenCV.

        Args:
            video_path (str): The path to the video file.

        Returns:
            cv2.VideoCapture: An OpenCV VideoCapture object.
        """
        return cv2.VideoCapture(video_path)

    def video_properties(self, cap: cv2.VideoCapture) -> cv2.VideoCapture:
        """Get the video properties.

        Args:
            cap (cv2.VideoCapture): An OpenCV VideoCapture object.

        Returns:
            cv2.VideoCapture: An OpenCV VideoCapture object.
        """
        return cap

    def get_properties(self, cap: cv2.VideoCapture, *args: str) -> List:
        """Get specific video properties.

        Args:
            cap (cv2.VideoCapture): An OpenCV VideoCapture object.
            *args (str): Variable length argument list of video properties to retrieve.

        Returns:
            List: A list containing the requested video properties.
        """
        properties = []
        for property in args:
            if property == VideoPropeties.TOTAL_FRAME:
                total_frame = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                properties.append(total_frame)
        return properties


class VideoReaderFFMPEG(BaseVideoReader):
    def read(self, video_path: str) -> FVideoCapture:
        """Read the video using FFMPEG.

        Args:
            video_path (str): The path to the video file.

        Returns:
            FVideoCapture: An FVideoCapture object.
        """
        return FVideoCapture(video_path)

    def video_properties(self, cap: FVideoCapture) -> dict:
        """Get the video properties.

        Args:
            cap (FVideoCapture): An FVideoCapture object.

        Returns:
            dict: A dictionary containing video properties.
        """
        return cap.get_video_properties()

    def get_properties(self, cap: FVideoCapture, *args: str) -> List:
        """Get specific video properties.

        Args:
            cap (FVideoCapture): An FVideoCapture object.
            *args (str): Variable length argument list of video properties to retrieve.

        Returns:
            List: A list containing the requested video properties.
        """
        properties = []
        video_properties = self.video_properties(cap)
        for property in args:
            if property == VideoPropeties.TOTAL_FRAME:
                total_frame = int(video_properties["nb_frames"])
                properties.append(total_frame)
        return properties


class VideoReaderProcessor:
    def __init__(self) -> None:
        pass

    def create_reader(self, video_mode: Union[str, VideoProcessingMode]) -> BaseVideoReader:
        """Create a video reader based on the specified video mode.

        Args:
            video_mode (Union[str, VideoProcessingMode]): The video processing mode.

        Returns:
            BaseVideoReader: An instance of a video reader.

        Raises:
            ValueError: If an invalid video mode is provided.
        """
        if video_mode == VideoProcessingMode.CV2:
            return VideoReaderOpenCV()
        elif video_mode == VideoProcessingMode.FFMPEG:
            return VideoReaderFFMPEG()
        else:
            raise ValueError("Invalid video type")

    def video_properties(self, cap, video_mode: Union[str, VideoProcessingMode]):
        """Get the video properties using the appropriate video reader.

        Args:
            cap (Union[cv2.VideoCapture, FVideoCapture]): The video capture object.
            video_mode (Union[str, VideoProcessingMode]): The video processing mode.

        Returns:
            Union[cv2.VideoCapture, dict]: The video properties.
        """
        reader = self.create_reader(video_mode)
        return reader.video_properties(cap)

    def read(self, video_path: str, video_mode: Union[str, VideoProcessingMode]):
        """Read the video using the appropriate video reader.

        Args:
            video_path (str): The path to the video file.
            video_mode (Union[str, VideoProcessingMode]): The video processing mode.

        Returns:
            Union[cv2.VideoCapture, FVideoCapture]: The video capture object.
        """
        self.validate_video_path(video_path)
        reader = self.create_reader(video_mode)
        return reader.read(video_path)

    def validate_video_path(self, path: str):
        if not os.path.exists(path):
            raise FileNotFoundError(f"File {path} not found!")

        cap = cv2.VideoCapture(path)
        if not cap.isOpened():
            cap.release()
            raise Exception(f"OpenCV can not open file: {path}")
        cap.release()

    def get_properties(self, video_path: str, video_mode: Union[str, VideoProcessingMode], *args) -> Union[int, list]:
        """Get specific video properties using the appropriate video reader.

        Args:
            video_path (str): The path to the video file.
            video_mode (Union[str, VideoProcessingMode]): The video processing mode.
            *args (str): Variable length argument list of video properties to retrieve.

        Returns:
            Union[int, List[Union[int, float]]]: The requested video properties.
        """
        reader = self.create_reader(video_mode)
        cap = reader.read(video_path)
        properties = reader.get_properties(cap, *args)
        if len(properties) == 1:
            return properties[0]
        return properties


def read_frame_in_video(video_path: str, index: int, normalize: bool = False) -> np.ndarray:
    cap = cv2.VideoCapture(video_path)
    cap.set(cv2.CAP_PROP_POS_FRAMES, index)
    ret, frame = cap.read()
    cap.release()
    if normalize:
        frame = frame.astype(np.float32) / 255.0

    return frame
