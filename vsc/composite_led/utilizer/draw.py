from typing import Union

import cv2
import numpy as np


class Draw:
    @staticmethod
    def put_text(
        frame: np.ndarray,
        text: str = None,
        org: tuple = (0, 0),
        color: tuple = (255, 0, 0),
    ):
        """Put text on the frame.

        Args:
            frame (np.ndarray): The frame to put text on.
            text (Optional[str], optional): The text to put on the frame. Defaults to None.
            org (Tuple[int, int], optional): The origin point (x, y) of the text. Defaults to (0, 0).
            color (Tuple[int, int, int], optional): The color of the text in BGR format. Defaults to (255, 0, 0).
        """
        cv2.putText(
            frame,
            text,
            org,
            cv2.FONT_HERSHEY_SIMPLEX,
            2,
            color,
            2,
            cv2.LINE_AA,
        )

    @staticmethod
    def visualize_point(
        final_frame: np.ndarray,
        coordinates: np.ndarray,
        color: tuple = (255, 0, 0),
        text: str = None,
        radius: int = 10,
        offset_x: int = 20,
        offset_y: int = 0,
    ):
        """
        Visualize points on the given final frame.

        Args:
            final_frame (np.ndarray): Numpy array representing the final frame.
            coordinates (np.ndarray): Numpy array representing the coordinates.
            color (tuple, optional): Color of the points in BGR format. Defaults to (255, 0, 0).
            text (str, optional): Text to display near the points. If None, coordinate will be used. Defaults to None.
            radius (int, optional): Radius of the points. Defaults to 10.
            ffset_x (int): The amount of horizontal offset to apply to the points.
                Positive values move the points to the right, negative values move them to the left.
                Defaults to 20.
            offset_y (int): The amount of vertical offset to apply to the points.
                Positive values move the points downward, negative values move them upward.
                Defaults to 0

        Returns:
            None
        """
        for coord in coordinates:
            if len(coord) == 1:
                coord = coord[0]
            _text = str(coord) if text is None else text

            Draw.put_text(final_frame, _text, (coord[0] + offset_x, coord[1] + offset_y), color)
            cv2.circle(final_frame, coord, radius=radius, color=color, thickness=-1)

    @staticmethod
    def visualize_bbox(frame: np.ndarray, results: Union[list, np.ndarray]):
        """
        Visualize bounding boxes on the given frame.

        Args:
            frame (np.ndarray): Numpy array representing the frame.
            results (list or np.ndarray): List of results from Yolo. [x1, y1, x2, y2, class_id, confidence_score]

        Returns:
            None
        """
        for result in results:
            label = str(result[4])
            confidence_score = str(round(result[5] * 100, 2)) + "%"
            x1, y1, x2, y2 = np.int32(result[:4])
            cv2.putText(
                frame,
                " ".join((label, confidence_score)),
                (x1, y1 + 100),
                cv2.FONT_HERSHEY_SIMPLEX,
                2,
                (0, 255, 255),
                2,
                cv2.LINE_AA,
            )
            cv2.rectangle(frame.astype(np.uint8), (x1, y1), (x2, y2), color=(255, 0, 255), thickness=2)
