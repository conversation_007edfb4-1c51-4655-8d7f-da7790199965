import argparse
import os
import sys
import time

import pandas as pd
from line_profiler import LineProfiler


sys.path.insert(0, "")
from vsc.composite_led_v2.constants import OUTPUT_EXT, TEXT_EXT
from vsc.composite_led_v2.core_v2 import CompositeLEDInferenceV2
from vsc.tools.split_merge_v2.merge import Merger
from vsc.tools.split_merge_v2.split import Splitter
from vsc.vsc2_cli import inference


def parse_args():
    parser = argparse.ArgumentParser("VSC Evaluation CLI")
    parser.add_argument("-s", "--source_folder_path", type=str, required=True)
    parser.add_argument("-l", "--led_path", type=str, required=True)
    parser.add_argument("-o", "--output_folder_path", type=str, required=True)
    parser.add_argument("-d", "--detail", action="store_true")

    return parser.parse_args()


def get_all_file_paths(root_folder):
    file_paths = []
    for dirpath, _, filenames in os.walk(root_folder):
        for filename in filenames:
            full_path = str(os.path.join(dirpath, filename))
            relative_path = os.path.relpath(full_path, root_folder)
            file_paths.append(relative_path)
    return file_paths


def task(source_path, led_path, output_path) -> float:
    start = time.time()
    inference(source_path, led_path, output_path)
    end = time.time()
    return end - start


def detail_task(source_path, led_path, output_path, detail_path) -> float:
    profiler = LineProfiler()

    start = time.time()
    inference(source_path, led_path, output_path)
    profiler.add_function(Splitter.run)
    profiler.add_function(CompositeLEDInferenceV2.extract_source_corners)
    profiler.add_function(CompositeLEDInferenceV2.run_corners_stabilizing)
    profiler.add_function(CompositeLEDInferenceV2.run_corners_post_processing)
    profiler.add_function(CompositeLEDInferenceV2.run_post_processing_and_blending)
    profiler.add_function(Merger.run)
    profiler.runcall(
        inference,
        source_path=source_path,
        led_path=led_path,
        output_path=output_path,
    )
    end = time.time()
    t = end - start

    with open(detail_path, 'w') as f:
        profiler.print_stats(stream=f)

    return t


def main():
    args = parse_args()
    path_list = get_all_file_paths(args.source_folder_path)
    output_video_folder_path = os.path.join(args.output_folder_path, "videos")
    output_csv_path = os.path.join(args.output_folder_path, "benchmark.csv")
    os.makedirs(output_video_folder_path, exist_ok=True)
    os.makedirs(os.path.join(output_video_folder_path, "detail"), exist_ok=True)
    result_df = pd.DataFrame(columns=["Name", "Time"])

    for path in path_list:
        source_path = os.path.join(args.source_folder_path, path)
        output_path = os.path.join(output_video_folder_path, path)
        if args.detail:
            detail_path = os.path.join(output_video_folder_path, "detail", path.replace(OUTPUT_EXT, TEXT_EXT))
            os.makedirs(os.path.dirname(detail_path), exist_ok=True)
            execution_time = detail_task(
                source_path=source_path,
                led_path=args.led_path,
                output_path=output_path,
                detail_path=detail_path,
            )
        else:
            execution_time = task(source_path, args.led_path, output_path)
        result_df.loc[len(result_df.index)] = [path, execution_time]
        result_df.to_csv(output_csv_path, index=False)


if __name__ == "__main__":
    main()
