<!-- PROJECT SHIELDS -->
<!--
*** I'm using markdown "reference style" links for readability.
*** Reference links are enclosed in brackets [ ] instead of parentheses ( ).
*** See the bottom of this document for the declaration of the reference variables
*** for contributors-url, forks-url, etc. This is an optional, concise syntax you may use.
*** https://www.markdownguide.org/basic-syntax/#reference-style-links
-->
[![pre-commit][pre-commit-shield]][pre-commit-url]
[![Imports: isort][isort-shield]][isort-url]
[![Code style: black][black-shield]][black-url]

<!-- PROJECT LOGO -->
<br />
<div align="center">
  <a href="https://gitlab.ftech.ai/computer-vision/projects/timi/vsc">
    <img src="https://i.imgur.com/QuEZzFL.png" alt="Logo" width="100" height="100">
  </a>

  <h3 align="center">Video Screen Composition</h3>

  <p align="center">
    A module to composite a LED into the Screen 🚀
    <br />
    <a href="https://gitlab.ftech.ai/computer-vision/projects/timi/vsc/-/wikis/"><strong>Explore the docs »</strong></a>
    <br />
    <br />
    <a href="https://onplus.com.vn/danh-muc/175">Timi News</a>
    ·
    <a href="https://gitlab.ftech.ai/computer-vision/projects/timi/vsc/-/boards">Report Bug</a>
    ·
    <a href="https://gitlab.ftech.ai/computer-vision/projects/timi/vsc/-/boards">Request Feature</a>
  </p>
</div>

---
<!-- 📋 TABLE OF CONTENTS -->
## 📋 Table of Contents

* [About the Project](#about-the-project)
  * [Built With](#built-with)
* [Getting Started](#getting-started)
  * [Prerequisites](#prerequisites)
  * [Installation](#installation)
* [Usage](#usage)
  * [Run](#run)
  * [Gitlab CI](#gitlab-ci)
* [Contributing to project](#contributing-to-project)
* [Contributors](#contributors)


<!-- ABOUT THE PROJECT -->
<h2 id="about-the-project"> 📑 About the project </h2>

![Intro](https://i.imgur.com/mBd6YHK.png)

The VSC (Video Screen Composition) module is designed to seamlessly integrate an LED, represented by either an image or a video, into a frame with a green-screen background. The primary objective of this module is to automate the process of video production, eliminating the need for manual editing using graphic design software.

By leveraging VSC, users can effortlessly incorporate LED visuals into their videos without the hassle of post-production editing. This functionality streamlines the video creation process, saving both time and effort for content creators.

With VSC, the vision of dynamic and visually captivating videos becomes easily achievable, empowering users to focus more on content creation and less on technical intricacies.


<h3 id="built-with"> 💻 Built with </h3>

- **Programming Language**: Python
- **Deep Learning Models**: Object Detection, Segmentation
- **Image Processing Techniques**: OpenCV, NumPy
- **Code Quality Testing**: isort, black, flake8, SonarQube, Unit Test
- **Containerization**: Docker, Docker-compose
- **Continuous Integration/Continuous Deployment (CI/CD)**: Automated workflows for code quality checks and deployment.


![Technology Stacks](https://i.imgur.com/2XruAH7.png)
<p align="center">CI/CD pipeline</p>


<h2 id="getting-started"> 🛠 Getting started </h2>

<h3 id="prerequisites"> 📎 Prerequisites </h3>

- Python `3.10.13`
- Pip `24.0`
- Conda `23.9.0`
- Docker `24.0.7`


<h2 id="installation"> 📎 Installation </h2>

1. Clone the repo
```bash
git clone https://gitlab.ftech.ai/computer-vision/projects/timi/vsc
cd vsc
```

2. Create your own virtual environment and activate it.
```bash
conda create --name vsc python=3.10.13 -y
conda activate vsc
```

3. Install packages
```bash
pip install -r requirements.txt
pre-commit install
```

<h2 id="usage">  🤖 Usage </h2>

Make sure you have a proper `.env.dev` file by duplicating the `template.env` file.


<h3 id="run"> 📎 Run with Docker</h3>

- `make linting`: pull the Docker image from the registry.
- `make build_image`: build the Docker image with the current version code.
- `make build_package`: build the module to package.
- `make push_package`: push the package to Axiom.
- `make test`: run all unit tests and integration tests (with non-optimized backends).
- `make up`: start all services.
- `make down`: stop all services.


<h3 id="gitlab-ci"> 📎 Setup gitlab-ci</h3>

#### The `.gitlab-ci.yml` is separate into 5 common stages:

- `linting`: checks code conventions using `isort`, `black`, and `flake8`.
- `sonarqube-check`: scans code quality with SonarQube tool.
- `build`: builds the Docker image.
- `test`: runs unit tests and integration tests.
- `push`:
   - `push_image`: pushes the Docker image to the registry.
   - `push_package`: build and push the package to Axiom.

More details can be found in the [CI/CD pipeline GitLab][CI/CD_pipeline]

<h2 id="contributing-to-project"> 👋 Contributing to project </h2>

1. Create your feature branch (e.g: `git checkout -b khanghn/feat/feature_a`).
2. Commit your changes (`git commit -m 'feat: add feature_a to enhance quality output' (#101)`).
3. Push to the branch (`git push origin khanghn/feat/feature_a`).
4. Create a merge request.
5. Resolve all comments from the reviewer.

All commits should adhere to [conventional commits](https://www.conventionalcommits.org/en/v1.0.0/) to maintain a clear `repository graph` and uphold a professional standard ✨.

<!-- 👨‍💻 CONTRIBUTORS -->
<h2 id="contributors"> 👨‍💻 Contributors </h2>

| Contributor | Role                            | Description                                                                                    |
|-------------|---------------------------------|------------------------------------------------------------------------------------------------|
| @quangtn2   | Project Manager                 | Responsible for coordination and strategic planning and reviewing code.                        |
| @khanghn    | Developer & Maintainer          | Developing and overseeing the maintenance of the entire project pipeline. |


<!-- MARKDOWN LINKS & IMAGES -->
<!-- https://www.markdownguide.org/basic-syntax/#reference-style-links -->
[profile]: https://github.com/KudoKhang
[issue]: https://github.com/KudoKhang/python-project-template/issues
[project]: https://github.com/KudoKhang/python-project-template
[pre-commit-shield]: https://img.shields.io/badge/pre--commit-enabled-brightgreen?logo=pre-commit
[pre-commit-url]: https://github.com/pre-commit/pre-commit
[isort-shield]: https://img.shields.io/badge/%20imports-isort-%231674b1?style=flat&labelColor=ef8336
[isort-url]: https://pycqa.github.io/isort/
[black-shield]: https://img.shields.io/badge/code%20style-black-000000.svg
[black-url]: https://github.com/psf/black
[CI/CD_pipeline]: https://gitlab.ftech.ai/computer-vision/projects/timi/vsc/-/pipelines
