import time

import argparse
import os
from vsc import CompositeLEDInference, VSC_Postprocess, VSC_Preprocess


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_file", type=str, required=True)
    parser.add_argument("--led_path", type=str, required=True)
    parser.add_argument("--output_folder", type=str, required=True)
    parser.add_argument("--save_path", type=str, required=True)
    parser.add_argument("--ckpt_folder", type=str, default="checkpoints/")
    parser.add_argument('--device', type=str, default='cuda:0')
    parser.add_argument('--cleanup_mode', type=str, default='all')
    parser.add_argument('--num_overlap_frames', type=int, default=0)
    return parser.parse_args()


def main():
    args = parse_args()
    video_led_pairs, init_information = VSC_Preprocess.run(
        video_path=args.input_file,
        led_path=args.led_path,
        output_folder=args.output_folder,
        max_interval_duration=200,
        num_overlap_frames=args.num_overlap_frames,
        ckpt_folder=args.ckpt_folder,
        threshold=400,
        device=args.device,
    )

    processed_segments = []
    for segment_file, led_file in video_led_pairs:
        if len(video_led_pairs) > 1:
            segment_name = os.path.basename(segment_file).replace(".mp4", "")
            led_name = os.path.basename(led_file).replace(".mp4", "")
            intermediate_folder = os.path.join(os.path.split(args.save_path)[0], "intermediates")
            os.makedirs(intermediate_folder, exist_ok=True)
            output_path = os.path.join(intermediate_folder, f"{segment_name}_{led_name}.mp4")
        else:
            output_path = args.save_path

        instance = CompositeLEDInference(device=args.device, weights_folder=args.ckpt_folder)
        instance.run(
            video_path=segment_file,
            led_path=led_file,
            save_path=output_path,
            is_dev=False,
            init_information=init_information,
        )
        processed_segments.append(output_path)

    VSC_Postprocess.run(
        video_path=args.input_file,
        led_path=args.led_path,
        processed_segments=processed_segments,
        video_led_pairs=video_led_pairs,
        num_overlap_frames=args.num_overlap_frames,
        final_output_path=args.save_path,
        cleanup_mode=args.cleanup_mode,
    )


if __name__ == "__main__":
    start = time.time()
    main()
    end = time.time()
    print(f"Time elapsed: {end - start}")
