# test case 9 (static screen, led video, satisfy split condition, led shorter than source, led and source have different audios)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/output_lipsync/89616567-4bf2-4618-af5f-8737628626e0.mp4" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_09/output.mp4" \
  --max_interval_duration 1800 \
  --threshold 5400

# test case 1 (static screen, led image, satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/leds/daophuquy.jpg" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_01/output.mp4" \
  --max_interval_duration 1800 \
  --threshold 5400

# test case 2 (static screen, led image, don't satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/leds/daophuquy.jpg" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_02/output.mp4" \
  --max_interval_duration -1 \
  --threshold 5400

# test case 3 (static screen, led video, satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -l "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_03/output.mp4" \
  --max_interval_duration 1800 \
  --threshold 5400

# test case 4 (static screen, led video, don't satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -l "/data/dwg/temps/vsc/issues/225/inputs/long_shot2_fullhd_4m.mp4" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_04/output.mp4" \
  --max_interval_duration -1 \
  --threshold 5400

# test case 5 (non-static screen, led image, satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/new_videos/C164_shot1.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/leds/daophuquy.jpg" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_05/output.mp4" \
  --max_interval_duration 240 \
  --threshold 480

# test case 6 (non-static screen, led image, don't satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/new_videos/C164_shot1.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/leds/daophuquy.jpg" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_06/output.mp4" \
  --max_interval_duration -1 \
  --threshold 480

# test case 7 (non-static screen, led video, satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/new_videos/C164_shot1.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/new_videos/C164_shot1.mp4" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_07/output.mp4" \
  --max_interval_duration 240 \
  --threshold 480

# test case 8 (non-static screen, led video, don't satisfy split condition)
python3 vsc/vsc2_cli.py \
  -s "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/new_videos/C164_shot1.mp4" \
  -l "/data/dwg/datasets/vsc_datasets/videos/checklist_v2/new_videos/C164_shot1.mp4" \
  -o "/data/dwg/temps/vsc/issues/225/debugs_review1/debug_08/output.mp4" \
  --max_interval_duration -1 \
  --threshold 480
