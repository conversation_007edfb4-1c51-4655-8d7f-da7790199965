# Git Workflow

Git workflow là một quy trình làm việc với Git để quản lý mã nguồn và phát triển phần mềm. Việc thống nhất 1 workflow giữa các thành viên sẽ giúp quy trình phát triển phần mềm được liền mạch, hạn chế conflict và sẵn sàng release sản phẩm liên tục.

## Chia nhánh

<p align="center">
  <img src="https://i.imgur.com/901z3aR.png" />

</p>
<p align="center">
   <i>Design</i>
</p>

__Master Branch__: Luôn ở trạng thái ổn định, việc deploy sẽ được thực hiện từ nhánh này. Không ai được phép làm việc trực tiếp trên branch này, chỉ có thể merge vào nó từ các branch kh<PERSON>c (cụ thể trong dự án này là 2 nhánh `dev` và nhánh `hotfix`) sau khi đã kiểm tra kỹ.

__Dev Branch__: Là nhánh cho việc phát triển chính của dự án. Tất cả các việc như tạo feature mới, fix bug, update… sẽ được checkout từ nhánh này.

__Feature, fix_bug, update branch__: Các nhánh này sẽ giải quyết một công việc cụ thể ví dụ tạo chức năng mới, sửa lỗi… Sau khi nhánh này đã giải quyết được vấn đề đặt ra sẽ được merge lại về nhánh `dev`.

# Review và merge code

- Code trước khi tạo Merge Request cần đảm bảo tuân theo code convention bao gồm `isort`, `black`, `flake8` và vượt qua các unit test.
- Code từ các branch feature, fix_bug, update… khi merge về `dev` tùy thuộc vào khối lượng code thay đổi, phạm vi thay đổi (có liên quan trực tiếp tới API đang chạy) để quyết định cần review hay không.
- Sau khi code ở nhánh `dev` được review kỹ sẽ tiến hành merge vào `master`.
- Nếu có lỗi trên nhánh `master` cần hotfix sẽ checkout ra nhánh `hotfix` sau khi xử lý sẽ được merge lại. Tuyệt đối không code trực tiếp trên nhánh `master`

## Commit code

- Các commit cần link tới issue liên quan (nếu có) để tiện theo dõi.
- Các commit có nội dung trùng nhau nên thực hiện `squash` trước đi push lên.

## Changelog

- Nên viết changelog để tiện theo dõi các thay đổi giữa các version được release.
- Sử dụng tag để đánh đấu version với mục đích dễ dàng theo dõi các thay đổi cập nhật và rollback la phiên bản ổn định nhanh chóng nếu code release gặp lỗi.
