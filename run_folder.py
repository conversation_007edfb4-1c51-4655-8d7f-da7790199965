import argparse
import os

from tqdm import tqdm

from vsc import CompositeLEDInference


def parse_args():
    parser = argparse.ArgumentParser()
    parser.add_argument('--input_folder', type=str, required=True)
    parser.add_argument('--led_path', type=str, required=True)
    parser.add_argument('--output_folder', type=str, required=True)

    args = parser.parse_args()
    return args


def main():
    args = parse_args()

    os.makedirs(args.output_folder, exist_ok=True)
    for video_name in tqdm(os.listdir(args.input_folder)):
        input_video_path = os.path.join(args.input_folder, video_name)
        output_video_path = os.path.join(args.output_folder, video_name)

        instance = CompositeLEDInference(
            device="cuda:0",
        )
        instance.run(video_path=input_video_path, led_path=args.led_path, save_path=output_video_path)


if __name__ == '__main__':
    main()
