SHELL=/bin/bash

linting:
	./deployment/deploy.sh linting

build_image:
	./deployment/deploy.sh build_image

push_image:
	./deployment/deploy.sh push_image

test:
	./deployment/deploy.sh test

build_package:
	./deployment/deploy.sh build_package

push_package:
	./deployment/deploy.sh push_package

up:
	./deployment/deploy.sh up

down:
	./deployment/deploy.sh down

run_docker:
	./deployment/deploy.sh run_docker
