FROM nvidia/cuda:11.8.0-cudnn8-runtime-ubuntu22.04

WORKDIR /vsc

ARG DEBIAN_FRONTEND=noninteractive
ENV TZ=Asia/Ho_Chi_Minh

# Install base utilities
RUN apt-get update && apt-get install -y --no-install-recommends tzdata unzip ffmpeg libsm6 libxext6 wget software-properties-common curl make automake gcc g++ nvidia-settings nvidia-modprobe\
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Set timezone
RUN ln -snf /usr/share/zoneinfo/"$TZ" /etc/localtime && echo "$TZ" > /etc/timezone

# Install Python 3.10 from ppa
RUN add-apt-repository ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
    python3.10 \
    python3-pip \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Link python3.10 to python3 and python
RUN ln -sf /usr/bin/python3.10 /usr/bin/python3 && \
    ln -sf /usr/bin/python3 /usr/bin/python && \
    ln -sf /usr/bin/pip3 /usr/bin/pip

# # Install pip. Ref to: https://stackoverflow.com/questions/69503329/pip-is-not-working-for-python-3-10-on-ubuntu/69527217#69527217
RUN curl -sS https://bootstrap.pypa.io/get-pip.py | python3.10

# Upgrade setuptools to fix error: "python setup.py egg_info". Ref to: https://stackoverflow.com/questions/35991403/pip-install-unroll-python-setup-py-egg-info-failed-with-error-code-1
RUN pip install --upgrade setuptools
RUN python -m pip install --upgrade pip
RUN pip install torch==2.0.0+cu118 --extra-index-url https://download.pytorch.org/whl/cu118 torchvision==0.15.0+cu118 --extra-index-url https://download.pytorch.org/whl/cu118

ENV PYTHONUNBUFFERED 1
ENV PYTHONDONTWRITEBYTECODE 1
ENV PYTHONPATH "${PYTHONPATH}:${PWD}"

# Install FastDeploy SDK with both CPU and GPU support
RUN wget http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/fastdeploy_part_00 && \
    wget http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/fastdeploy_part_01 && \
    wget http://minio.dev.ftech.ai/vc2-lib-1.0.0-6811ad54/fastdeploy_part_02 && \
    cat fastdeploy_part_00 fastdeploy_part_01 fastdeploy_part_02 > fastdeploy_new.zip && \
    unzip fastdeploy_new.zip && \
    pip install fastdeploy_gpu_python-1.0.7-cp310-cp310-manylinux1_x86_64.whl && \
    rm -rf fastdeploy_part_00 fastdeploy_part_01 fastdeploy_part_02 fastdeploy_new.zip fastdeploy_gpu_python-1.0.7-cp310-cp310-manylinux1_x86_64.whl

# Remove blinker to fix error: "Found existing installation: blinker 1.4 42.89 error: uninstall-distutils-installed-package 42.89 42.89 × Cannot uninstall blinker 1.4 42.89 ╰─> It is a distutils installed project and thus we cannot accurately determine which files belong to it which would lead to only a partial uninstall.". Ref to: https://github.com/rsmusllp/king-phisher/issues/300#issuecomment-394360146
RUN apt remove python3-blinker -y
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

# CMD to keep the container running
CMD ["tail", "-f", "/dev/null"]
