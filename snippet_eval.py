import argparse
import os
import pandas as pd
import time
from fvutils.media import get_video_fps
from line_profiler import LineProfiler

from vsc import CompositeLEDInferenceV2, SplitMerge


def get_all_file_paths(root_folder):
    file_paths = []
    for dirpath, _, filenames in os.walk(root_folder):
        for filename in filenames:
            full_path = str(os.path.join(dirpath, filename))
            relative_path = os.path.relpath(full_path, root_folder)
            file_paths.append(relative_path)
    return file_paths


def parse_args():
    parser = argparse.ArgumentParser("VSC CLI")
    parser.add_argument("-s", "--source_folder", type=str, required=True, help="Path to the source video folder.")
    parser.add_argument("-l", "--led_path", type=str, required=True, help="Path to the LED file.")
    parser.add_argument("-o", "--output_folder", type=str, required=True, help="Path to save the output video folder.")
    parser.add_argument("--data_folder", type=str, default=None, help="Path to the temp data folder, Use uuid if None.")
    parser.add_argument("--device", type=str, default="cuda:0", help="Device to use for inference.")
    parser.add_argument("--weights_folder", type=str, default="checkpoints", help="Path to the weights folder.")
    parser.add_argument("--max_interval_duration", type=int, default=60, help="Maximum duration for each sub-session, in seconds.")
    parser.add_argument("--threshold", type=int, default=180, help="Minimum duration for splitting, in seconds.")
    parser.add_argument("--detail", action="store_true", help="Whether to save detail execution time information.")

    return parser.parse_args()


class SplitMergeInference:
    def __init__(
        self,
        max_interval_duration: int = 60,
        threshold: int = 180,
        weight_folder: str = "checkpoints",
        device: str = "cuda:0",
        data_folder: str = None
    ):
        self.split_merge = SplitMerge(data_folder=data_folder, device=device, weights_folder=weight_folder)
        self.pipe = CompositeLEDInferenceV2(device=device, weights_folder=weight_folder)
        self.max_interval_duration = max_interval_duration
        self.threshold = threshold

    def run(self, source_path: str, led_path: str, output_path: str):
        fps = get_video_fps(source_path)
        session_list, metadata = self.split_merge.split(
            video_path=source_path,
            led_path=led_path,
            max_interval_duration=int(self.max_interval_duration * fps),
            threshold=int(self.threshold * fps),
        )

        for session in session_list:
            self.pipe.run(session)

        self.split_merge.merge(session_list, metadata, output_path)
        self.split_merge.release()


def inference(args, source_path, led_path, output_path) -> float:
    start = time.time()
    runner = SplitMergeInference(
        max_interval_duration=args.max_interval_duration,
        threshold=args.threshold,
        weight_folder=args.weights_folder,
        device=args.device,
        data_folder=args.data_folder,
    )

    runner.run(source_path, led_path, output_path)
    end = time.time()
    t = end - start
    return t


def detail_inference(args, source_path, led_path, output_path, detail_path) -> float:
    profiler = LineProfiler()

    start = time.time()
    profiler.add_function(CompositeLEDInferenceV2.extract_source_corners)
    profiler.add_function(CompositeLEDInferenceV2.run_corners_stabilizing)
    profiler.add_function(CompositeLEDInferenceV2.run_corners_post_processing)
    profiler.add_function(CompositeLEDInferenceV2.run_post_processing_and_blending)
    profiler.runcall(
        inference,
        args=args,
        source_path=source_path,
        led_path=led_path,
        output_path=output_path,
    )
    end = time.time()
    t = end - start

    with open(detail_path, 'w') as f:
        profiler.print_stats(stream=f)

    return t


def main():
    args = parse_args()

    output_video_folder_path = os.path.join(args.output_folder, "videos")
    output_detail_folder_path = os.path.join(args.output_folder, "details")

    output_csv_path = os.path.join(args.output_folder, "benchmark.csv")
    result_df = pd.DataFrame(columns=["Name", "Time"])

    for path in get_all_file_paths(args.source_folder):
        source_path = os.path.join(args.source_folder, path)
        output_path = os.path.join(output_video_folder_path, path)
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        if args.detail:
            detail_path = os.path.join(output_detail_folder_path, path.replace(".mp4", ".txt"))
            os.makedirs(os.path.dirname(detail_path), exist_ok=True)

            execution_time = detail_inference(args, source_path, args.led_path, output_path, detail_path)
        else:
            execution_time = inference(args, source_path, args.led_path, output_path)

        result_df.loc[len(result_df.index)] = [path, execution_time]
        result_df.to_csv(output_csv_path, index=False)


if __name__ == "__main__":
    main()
