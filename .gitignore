# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]

# C extensions
*.so

# Distribution / packaging
.Python
env/
venv/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# PyInstaller
#  Usually these files are written by a python script from a template
#  before PyInstaller builds the exe, so as to inject date/other infos into it.
*.manifest
*.spec


*.png
*.jpg
*.jpeg
*.gif
*.mp4
*.mov

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.json
coverage_re/

# Translations
*.mo
*.pot
mc
# Sphinx documentation
docs/_build/

# PyBuilder
target/

# DotEnv configuration
.env
.env.*
!template.env


# Database
*.db
*.rdb

# Pycharm
.idea
.uuid

# VS Code
.vscode/

# Spyder
.spyproject/

# Jupyter NB Checkpoints
.ipynb_checkpoints/

# exclude data from source control by default
/data/

# Mac OS-specific storage files
.DS_Store
`
# vim
*.swp
*.swo

# Mypy cache
.mypy_cache/

*.log
weights/
logs/

resources/**
#!resources/**/
!.gitkeep

*.csv

scio_data/
builds/
*.html

# Data
data/
results/
checkpoints/
notebooks/

# Config
vsc/conf/default.yaml

# SonarQube
.scannerwork

# Allow training code
!notebooks/Training_CornerDetection_with_YoloV8.ipynb
