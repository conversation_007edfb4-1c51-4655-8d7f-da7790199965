import os
import subprocess
import uuid

from fvutils.videoio import FVideoCapture, PixelFormat

from vsc.composite_led.functions.screen import Screen
from vsc.composite_led.processor.corners import CornersValidator
from vsc.composite_led.segmentor.model_matting import ModelMatting
from vsc.utils import cfg
from vsc.utils.logger import logger


class VideoDurationAdjuster:
    def __init__(self, vid_dir: str, target_vid_dir: str, output_dir: str, method: str = 'duplicate'):
        self.vid_dir = vid_dir
        self.target_vid_dir = target_vid_dir
        self.output_dir = output_dir
        self.method = method

    def _trim_video(self, frames2, fps2):
        return [
            "ffmpeg",
            "-y",
            "-i",
            self.vid_dir,
            "-vf",
            f"select='between(n,0,{frames2-1})',setpts=N/{fps2}/TB",
            "-c:v",
            "libx264",
            "-crf",
            "18",
            "-preset",
            "ultrafast",
            "-r",
            str(fps2),
            "-c:a",
            "aac",
            "-b:a",
            "192k",
            self.output_dir,
        ]

    def _copy_or_reencode_video(self, fps2):
        return [
            "ffmpeg",
            "-y",
            "-i",
            self.vid_dir,
            "-c:v",
            "libx264",
            "-crf",
            "18",
            "-preset",
            "ultrafast",
            "-r",
            str(fps2),
            "-c:a",
            "aac",
            "-b:a",
            "192k",
            self.output_dir,
        ]

    def _pad_video(self, frames1, frames2, fps2):
        pad_duration = (frames2 - frames1) / fps2
        return [
            "ffmpeg",
            "-y",
            "-i",
            self.vid_dir,
            "-vf",
            f"tpad=stop_mode=clone:stop_duration={pad_duration},setpts=N/{fps2}/TB",
            "-c:v",
            "libx264",
            "-crf",
            "18",
            "-preset",
            "ultrafast",
            "-r",
            str(fps2),
            "-c:a",
            "aac",
            "-b:a",
            "192k",
            self.output_dir,
        ]

    def _loop_video(self, frames1, frames2, fps2):
        loop_count = (frames2 // frames1) + 1
        return [
            "ffmpeg",
            "-y",
            "-stream_loop",
            str(loop_count - 1),
            "-i",
            self.vid_dir,
            "-vf",
            f"select='between(n,0,{frames2-1})',setpts=N/{fps2}/TB",
            "-c:v",
            "libx264",
            "-crf",
            "18",
            "-preset",
            "ultrafast",
            "-r",
            str(fps2),
            "-c:a",
            "aac",
            "-b:a",
            "192k",
            self.output_dir,
        ]

    def _run_ffmpeg(self, cmd):
        try:
            p = subprocess.run(cmd, capture_output=True, text=True, check=True)
            logger.debug(f"FFMPEG Subprocess Exit Status: {p.returncode}")
            logger.debug(f"stderr: {p.stderr}")
        except subprocess.CalledProcessError as e:
            logger.error(f"FFMPEG Subprocess Exit Status: {e.returncode}")
            logger.error(f"stderr: {e.stderr}")
            raise

    def run(self):
        frames1, fps1 = VSC_Preprocess.get_num_frames_and_fps(self.vid_dir)
        frames2, fps2 = VSC_Preprocess.get_num_frames_and_fps(self.target_vid_dir)

        logger.debug(f"Frames1: {frames1}, FPS1: {fps1}")
        logger.debug(f"Frames2: {frames2}, FPS2: {fps2}")

        if frames1 > frames2:
            cmd = self._trim_video(frames2, fps2)
        elif frames1 == frames2:
            cmd = self._copy_or_reencode_video(fps2)
        elif self.method == "duplicate":
            cmd = self._pad_video(frames1, frames2, fps2)
        else:
            cmd = self._loop_video(frames1, frames2, fps2)

        self._run_ffmpeg(cmd)


class VSC_Preprocess:
    @staticmethod
    def split_and_extract_segments(
        video_path: str,
        output_folder: str,
        max_interval_duration: int = 100,
        expected_overlap_frames: int = 5,
        script_path: str = "./scripts/bashscripts/extract_segments.sh",
    ):
        '''
        Splits a video into overlapping segments and extracts them using an external Bash script.

        Args:
            video_path (str): Path to the input video file.
            max_interval_duration (int, optional): Maximum duration of each segment in frames. Defaults to 100.
            expected_overlap_frames (int, optional): Number of overlapping frames between consecutive segments. Defaults to 5.
            script_path (str, optional): Path to the Bash script used for segment extraction. Defaults to './scripts/bashscripts/extract_segments.sh'.

        Returns:
            tuple: A tuple containing:
                - frame_indices (list of tuple): A list of (start_frame, end_frame) pairs for each segment.
                - segment_files (list of str): A list of file paths for the extracted video segments.

        Notes:
            - The last segment does not apply overlap and captures the remaining frames to the end of the video.
            - Segments are saved in a newly created output folder named '<video_name>_segments' in the same directory as the input video.
            - The Bash script is expected to accept arguments in the form: video_path start_frame end_frame output_file.
        '''
        # Create output folder
        video_name = os.path.splitext(os.path.basename(video_path))[0]

        # Open video
        cap = FVideoCapture(video_path, output_pixel_format=PixelFormat.BGR24)
        total_frames = int(cap.get_video_properties()["nb_frames"])
        cap.release()

        frame_indices = []
        start_frame = 0

        while start_frame < total_frames:
            end_frame = min(start_frame + max_interval_duration - 1, total_frames - 1)

            # If it's the last segment, don't apply overlap
            if end_frame == total_frames - 1:
                frame_indices.append((start_frame, total_frames))
                break

            frame_indices.append((start_frame, end_frame))
            start_frame = end_frame + 1 - expected_overlap_frames  # Move to next start without repeating last frame

        # Generate segment file names
        segment_files = [
            os.path.join(output_folder, f"{video_name}_segment_{i+1}_{uuid.uuid4().hex}.mp4")
            for i in range(len(frame_indices))
        ]

        # Extract segments using the provided script
        for ith, (start_idx, end_idx) in enumerate(frame_indices):
            try:
                p = subprocess.run(["bash", script_path, video_path, str(start_idx), str(end_idx), segment_files[ith]])
                logger.debug(f"Vsc_Preprocess.split_and_extract_segments FFMPEG Subprocess Exit Status: {p.returncode}")
                logger.debug(f"stderr: {p.stderr}")
            except subprocess.CalledProcessError as e:
                logger.error(f"Vsc_Preprocess.split_and_extract_segments FFMPEG Subprocess Exit Status: {e.returncode}")
                logger.error(f"stderr: {e.stderr}")
                raise

        logger.debug(f"Segments saved in: {output_folder}")
        return frame_indices, segment_files

    @staticmethod
    def get_num_frames_and_fps(video_path: str) -> tuple[int, float]:
        properties = FVideoCapture(video_path).get_video_properties()
        num_frames = int(properties["nb_frames"])
        fps = eval(properties["avg_frame_rate"])
        return num_frames, fps

    @staticmethod
    def process_static_screen(video_path: str, ckpt_folder: str) -> dict:
        model_matting = ModelMatting(model=cfg.led.model_matting, dir_save=ckpt_folder, device="cpu")
        ret, first_frame = next(FVideoCapture(video_path, output_pixel_format=PixelFormat.BGR24).read())
        logger.debug(f"ret: {ret}, first_frame shape: {first_frame.shape}")
        matte_timi = model_matting.model.process_image(first_frame)
        init_information = CornersValidator.find_coordinates_and_expansion_in_an_image(first_frame, matte_timi)
        return init_information

    @staticmethod
    def split_video_and_led(
        video_path: str, led_path: str, output_folder: str, max_interval_duration: int, num_overlap_frames: int
    ):

        video_output_folder = os.path.join(output_folder, "video_segments")
        led_output_folder = os.path.join(output_folder, "led_segments")
        os.makedirs(video_output_folder, exist_ok=True)
        os.makedirs(led_output_folder, exist_ok=True)

        frame_indices, segment_files = VSC_Preprocess.split_and_extract_segments(
            video_path=video_path,
            output_folder=video_output_folder,
            max_interval_duration=max_interval_duration,
            expected_overlap_frames=num_overlap_frames,
        )

        if led_path.endswith(".mp4"):
            adjusted_led_dir = led_path.replace(".mp4", f"_adjusted_{uuid.uuid4().hex}.mp4")
            VideoDurationAdjuster(vid_dir=led_path, target_vid_dir=video_path, output_dir=adjusted_led_dir).run()
            _, led_files = VSC_Preprocess.split_and_extract_segments(
                video_path=adjusted_led_dir,
                output_folder=led_output_folder,
                max_interval_duration=max_interval_duration,
                expected_overlap_frames=num_overlap_frames,
            )

            os.remove(adjusted_led_dir)

        else:
            led_files = [led_path for _ in range(len(segment_files))]

        return segment_files, led_files

    @staticmethod
    def prepare_video(video_path: str):
        static_screen, have_screen = Screen.check_status(video_path)
        return static_screen, have_screen

    @staticmethod
    def run(
        video_path: str,
        led_path: str,
        output_folder: str,
        max_interval_duration: int,
        num_overlap_frames: int,
        ckpt_folder: str,
        threshold: int = 9000,
    ) -> tuple[list[str], list[str], dict]:

        # Define default initialization information.
        default_info = {
            "coordinates": None,
            "expansion_width_range": None,
            "expansion_height_range": None,
        }

        if max_interval_duration == -1:
            logger.debug("max_interval_duration == -1, VSC will switch to full-sequence mode.")
            return [(video_path, led_path)], default_info

        num_frames, _ = VSC_Preprocess.get_num_frames_and_fps(video_path=video_path)
        if num_frames < threshold:
            logger.debug(
                f"The video has {num_frames} frames and it is smaller than threshold. Therefore, VSC will not apply split-and-merge algorithm and switch to full-sequence mode!"
            )
            return [(video_path, led_path)], default_info

        # Prepare video to get screen information.
        static_screen, have_screen = VSC_Preprocess.prepare_video(video_path)

        if max_interval_duration >= num_frames:
            logger.debug(
                f"The max_interval_duration ({max_interval_duration}) is larger than num_frames ({num_frames}) of the {video_path}. Therefore, VSC will not apply split-and-merge algorithm and switch to full-sequence mode!"
            )
            return [(video_path, led_path)], default_info

        # if no screen is detected, return the original file paths as a single pair.
        if not have_screen:
            logger.debug(f"The {video_path} does not have green screen. Hence, VSC.Preprocess will return the original video!")
            return [(video_path, led_path)], default_info

        # For long videos with a detected screen:
        if static_screen:
            logger.debug(
                f"The {video_path} satisfies split-and-merge's condition. VSC is processing init_information and splitting the video..."
            )
            # Process static screen and split the video and LED files.
            init_information = VSC_Preprocess.process_static_screen(video_path, ckpt_folder)
            video_files, led_files = VSC_Preprocess.split_video_and_led(
                video_path, led_path, output_folder, max_interval_duration, num_overlap_frames
            )
            # Merge the corresponding video and LED files into pairs.
            merged_files = list(zip(video_files, led_files))
            return merged_files, init_information
        else:
            # When the screen is not static (but exists), return the original file pair.
            logger.debug(
                f"The green screen in {video_path} video is not static. Therefore, split-and-merge algorithm cannot be applied. Falling into full-sequence mode!"
            )
            return [(video_path, led_path)], default_info
