#!/bin/bash

if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <directory1> <directory2>"
    exit 1
fi

DIR1="$1"
DIR2="$2"

OUTPUT_DIR="${DIR1}_horizontal_concatenation"
mkdir -p "$OUTPUT_DIR"

for file1 in "$DIR1"/*; do
    filename=$(basename "$file1")
    file2="$DIR2/$filename"

    if [ -f "$file2" ]; then
        ffmpeg -i "$file1" -i "$file2" -filter_complex hstack "$OUTPUT_DIR/$filename" -y
    else
        echo "File $filename not exist in folder $DIR2"
    fi
done

echo "Horizontal concatenation completed! Output in $OUTPUT_DIR"
