#!/bin/bash

if [ "$#" -ne 1 ]; then
  echo "Usage: bash $0 [source_directory]"
  exit 1
fi

SOURCE_DIR="$1"
DEST_DIR="$1_only_output"

mkdir -p "$DEST_DIR"

UNWANTED_SUFFIXES=("_mask" "_mask_processed" "_matte" "_stage1")

contains_unwanted_suffix() {
  local filename="$1"
  for suffix in "${UNWANTED_SUFFIXES[@]}"; do
    if [[ "$filename" == *"$suffix".mp4 ]]; then
      return 0 # True
    fi
  done
  return 1 # False
}

for file in "$SOURCE_DIR"/*.mp4; do
  filename=$(basename "$file")
  if ! contains_unwanted_suffix "$filename"; then
    echo "$filename"
    mv "$file" "$DEST_DIR"
  fi
done
