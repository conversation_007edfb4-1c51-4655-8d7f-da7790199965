#!/bin/bash

# Usage: ./extract_segment.sh input.mp4 start_frame end_frame output.mp4

input="$1"
start_frame="$2"
end_frame="$3"
output="$4"

# Get original video properties
video_bitrate=$(ffmpeg -i "$input" 2>&1 | grep -oP 'bitrate: \K[0-9]+' || echo "5000")
fps=$(ffmpeg -i "$input" 2>&1 | awk '/Video:/ {match($0, /[0-9.]+ fps/); print substr($0, RSTART, RLENGTH-4)}')
echo "Detected FPS: $fps"
audio_bitrate=$(ffmpeg -i "$input" 2>&1 | grep -oP 'Audio:.* \K[0-9]+(?= kb/s)' || echo "192")

# Frame-accurate segment extraction with re-encoding
ffmpeg -y -i "$input" \
-vf "select='between(n,$start_frame,$end_frame)',setpts=N/${fps}/TB" \
-c:v libx264 -b:v "${video_bitrate}k" -r "$fps" \
-preset ultrafast -crf 0 \
-c:a aac -b:a "${audio_bitrate}k" \
"$output"
