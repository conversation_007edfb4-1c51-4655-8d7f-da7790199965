#!/bin/bash

if [ -z "$1" ]; then
    echo "Usage: $0 <directory>"
    exit 1
fi

DIR="$1"

declare -A file_suffixes
file_suffixes=(
    ["_stage1.mp4"]="_stage_1_h264.mp4"
    ["_matte.mp4"]="_matte_h264.mp4"
)

# <PERSON><PERSON><PERSON><PERSON> qua tất cả các file trong thư mục
for file in "$DIR"/*; do
    for suffix in "${!file_suffixes[@]}"; do
        if [[ "$file" == *"$suffix" ]]; then
            output_file="${file%$suffix}${file_suffixes[$suffix]}"
            ffmpeg -i "$file" -c:v libx264 -preset medium -crf 23 -c:a aac -b:a 192k "$output_file"
        fi
    done
done

echo "Conversion completed!"
