#!/bin/bash

if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <folder> <duration>"
    exit 1
fi

FOLDER=$1
DURATION=$2

if [ ! -d "$FOLDER" ]; then
    echo "The folder $FOLDER does not exist."
    exit 1
fi

OUTPUT_FOLDER="$FOLDER/trimmed_$DURATION"
mkdir -p "$OUTPUT_FOLDER"

for VIDEO in "$FOLDER"/*; do
    if [[ -f $VIDEO ]]; then
        BASENAME=$(basename "$VIDEO")
        OUTPUT_VIDEO="$OUTPUT_FOLDER/trimmed_$BASENAME"

        ffmpeg -i "$VIDEO" -t "$DURATION" -c copy "$OUTPUT_VIDEO" -y

        echo "Trimmed $VIDEO and saved as $OUTPUT_VIDEO"
    fi
done

echo "All videos have been trimmed."
