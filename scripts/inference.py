import os
from datetime import datetime

import pandas as pd
from codetiming import Timer
from fvutils import media
from fvutils.progress import ProgressUpdater
from matplotlib import pyplot as plt
from tqdm import tqdm

from vsc import CompositeLEDInference, VSC_Postprocess, VSC_Preprocess
from vsc.composite_led.base import BaseProcessor
from vsc.utils.folder_utils import create_date_path
from vsc.utils.logger import logger


class Performance(BaseProcessor):
    def __init__(self, input_folder: str, postfix: str = "_perf.csv") -> None:
        self.header = ["input", "k_factor", "execution_time_stage1", "execution_time_stage2", "execution_time_stage3"]
        self.input_folder = input_folder
        self.postfix = postfix
        self.line_break = "\n\t\t\t\t\t✶✷✸✹✺✻✼✽✾✿❀❁❂❃❄❅❆❇❈❉❊❋\n"
        self.performances = []
        self.save_path = None

    def _create_save_path(self) -> str:
        if self.input_folder.endswith(".mp4"):
            prefix = self.input_folder[:-4]
        elif self.input_folder.endswith("/"):
            prefix = self.input_folder[:-1]
        else:
            prefix = self.input_folder

        self.save_path = prefix + self.postfix
        return self.save_path

    def _save_metrics(self, index: bool = False):
        self.performances = [item for sublist in self.performances for item in sublist]
        data = pd.DataFrame(self.performances)
        self._create_save_path()
        data.to_csv(self.save_path, index=index, header=self.header)
        logger.info(f"📢 Benchmark k_factor file saved at '{self.save_path}'{self.line_break}")

    def _summarize_metrics(self):
        df = pd.read_csv(self.save_path)

        df['total_execution_time'] = df[['execution_time_stage1', 'execution_time_stage2', 'execution_time_stage3']].sum(
            axis=1
        )

        # View the first 5 rows
        logger.info(f"5 first rows:\n{df.head()}{self.line_break}")

        # Generate descriptive statistics
        logger.info(f"Summary statistics:\n{df.describe()}{self.line_break}")

        # Summary statistics grouped by input_name
        pd.set_option('display.max_columns', None)
        pd.set_option('display.max_rows', None)

        metrics = ['mean', 'std', 'min', 'max']
        grouped = df.groupby('input').agg(
            {
                'k_factor': metrics,
                'execution_time_stage1': metrics,
                'execution_time_stage2': metrics,
                'execution_time_stage3': metrics,
                'total_execution_time': metrics,
            }
        )

        logger.info(f"Summary statistics grouped by input_name:\n{grouped}{self.line_break}")

        df.to_csv(self.save_path, index=False)
        logger.info(f"📢 Benchmark k_factor file saved at '{self.save_path}'{self.line_break}")

    def run(
        self,
        device: str = "cuda:0",
        weights_folder: str = "checkpoints",
        max_interval_duration: int = 2500,
        num_overlap_frames: int = 0,
        threshold: int = 9000,
        cleanup_mode: str = "all",
        times: int = 1,
        is_dev: bool = False,
    ):
        try:
            for _ in tqdm(range(times)):
                inference = Inference(self.input_folder, is_dev)
                perf = inference.run(
                    device, weights_folder, max_interval_duration, num_overlap_frames, threshold, cleanup_mode
                )
                self.performances.append(perf)
            self._save_metrics()
            self._summarize_metrics()
            self.release()
        except KeyboardInterrupt:
            logger.info("Stopped!")

    def release(self):
        self.performances = []
        self.save_path = None

    @staticmethod
    def get_execution_time(key: str, round_number: int = 2):
        try:
            return round(Timer.timers.mean(key), round_number)
        except KeyError:
            return 0

    @staticmethod
    def log_metrics(input_file: str, save_path: str, visualize: bool = False) -> list:
        durations = media.get_media_duration(input_file)

        execution_time_all = Performance.get_execution_time('CompositeLEDInference.run')
        stage1_execution_time = Performance.get_execution_time('Stage1Processor.run')
        stage2_execution_time = Performance.get_execution_time('Stage2Processor.run')
        stage3_execution_time = Performance.get_execution_time('Stage3Processor.run')

        stage1_per_cent = stage1_execution_time * 100 // execution_time_all
        stage2_per_cent = stage2_execution_time * 100 // execution_time_all
        stage3_per_cent = stage3_execution_time * 100 // execution_time_all

        k_factor = round(execution_time_all / durations, 2)

        logger.info(f"Stage1: {stage1_execution_time}s / {execution_time_all}s = {stage1_per_cent} %")
        logger.info(f"Stage2: {stage2_execution_time}s / {execution_time_all}s = {stage2_per_cent} %")
        logger.info(f"Stage3: {stage3_execution_time}s / {execution_time_all}s = {stage3_per_cent} %")
        logger.info(f"K factor: {k_factor}")

        data = []
        metrics = ["count", "total", "min", "max", "mean", "median", "stdev"]
        for process in Timer.timers.keys():
            values = [eval(f"Timer.timers.{metric}('{process}')") for metric in metrics]

            # Formatting
            values[1::] = [round(v * 1e3, 2) for v in values[1::]]

            data.append([process] + values)

        df = pd.DataFrame(data, columns=["process"] + metrics)

        postfix = str(datetime.now().strftime("%Y-%m-%d-%H-%M-%S"))
        save_folder_benchmark = save_path[:-4] + "_performances"
        os.makedirs(save_folder_benchmark, exist_ok=True)
        csv_file = os.path.join(save_folder_benchmark, f"benchmark_metrics_{postfix}.csv")
        df.to_csv(csv_file, index=False, header=True)

        if visualize:
            df = df.dropna()
            for metric in metrics:
                plt.figure(figsize=(10, 8))
                metric_data = df[['process', metric]].set_index('process')
                total_value = metric_data[metric].sum()
                metric_data['percentage'] = (metric_data[metric] / total_value) * 100
                filtered_data = metric_data[metric_data['percentage'] >= 1]
                plt.pie(filtered_data[metric], labels=filtered_data.index, autopct='%1.1f%%', startangle=140)
                plt.title(f"Distribution of {metric} (Processes with >= 1%)")
                plt.savefig(f"{os.path.join(save_folder_benchmark, metric)}_filtered_pie_chart.png")

        logger.info(f"⏳ Benchmark metrics file saved at '{save_folder_benchmark}'")

        Timer.timers.clear()

        return [input_file, k_factor, stage1_execution_time, stage2_execution_time, stage3_execution_time]


class Inference(BaseProcessor):
    def __init__(
        self,
        input_folder: str,
        is_dev: bool = True,
        data_root: str = "data/led/",
        output_folder: str = "results/led",
        led_path: str = "data/led/LED_1.jpg",
    ):
        self.input_folder = input_folder
        self.data_root = data_root
        self.output_folder = output_folder
        self.led_path = led_path
        self.is_dev = is_dev
        self.progress = ProgressUpdater()

    def _get_input_files(self) -> list:
        input_files = []

        if os.path.isdir(self.input_folder):
            for root, dirs, files in os.walk(self.input_folder):
                for file in files:
                    if file.endswith(".mp4"):
                        input_files.append(os.path.join(root, file))
        if os.path.isfile(self.input_folder) and self.input_folder.endswith(".mp4"):
            input_files.append(self.input_folder)

        return input_files

    def _create_output_path(self, input_path: str) -> str:
        assert self.data_root in input_path
        save_path = os.path.join(self.output_folder, create_date_path(), input_path.split(self.data_root)[-1])
        save_path = save_path[:-4] + "_" + os.path.basename(self.led_path)[:-4] + ".mp4"
        os.makedirs(os.path.dirname(save_path), exist_ok=True)
        return save_path

    def run(
        self,
        device: str = "cuda:0",
        weights_folder: str = "checkpoints",
        max_interval_duration: int = 2500,
        num_overlap_frames: int = 0,
        threshold: int = 9000,
        cleanup_mode: str = "all",
    ):
        input_files = self._get_input_files()
        if len(input_files) == 0:
            logger.warning(f"Input files: {input_files} is empty, no .mp4 file found in input folder: {self.input_folder}")
            return

        performances = []

        for input_file in tqdm(input_files):
            save_path = self._create_output_path(input_file)

            # VSC.preprocess
            video_led_pairs, init_information = VSC_Preprocess.run(
                video_path=input_file,
                led_path=self.led_path,
                output_folder=os.path.join(self.output_folder, "segments"),
                max_interval_duration=max_interval_duration,
                num_overlap_frames=num_overlap_frames,
                ckpt_folder=weights_folder,
                threshold=threshold,
            )

            processed_segments = []
            for segment_file, led_file in video_led_pairs:
                if len(video_led_pairs) > 1:
                    segment_name = os.path.basename(segment_file).replace(".mp4", "")
                    led_name = os.path.basename(led_file).replace(".mp4", "")
                    intermediate_folder = os.path.join(os.path.split(save_path)[0], "intermediates")
                    os.makedirs(intermediate_folder, exist_ok=True)
                    output_path = os.path.join(intermediate_folder, f"{segment_name}_{led_name}.mp4")
                else:
                    output_path = save_path

                instance = CompositeLEDInference(device=device, weights_folder=weights_folder)
                instance.run(
                    video_path=segment_file,
                    led_path=led_file,
                    save_path=output_path,
                    is_dev=self.is_dev,
                    init_information=init_information,
                )
                processed_segments.append(output_path)

            VSC_Postprocess.run(
                video_path=input_file,
                led_path=self.led_path,
                processed_segments=processed_segments,
                video_led_pairs=video_led_pairs,
                num_overlap_frames=num_overlap_frames,
                final_output_path=save_path,
                cleanup_mode=cleanup_mode,
            )

            perf = Performance.log_metrics(input_file, save_path)
            performances.append(perf)

        return performances


if __name__ == "__main__":
    performances = Performance(input_folder="data/led/demo_lectures")
    performances.run(
        device="cuda:2",
        weights_folder="checkpoints/",
        max_interval_duration=2500,
        num_overlap_frames=0,
        threshold=5000,
        cleanup_mode="all",
        times=1,
        is_dev=False,
    )
