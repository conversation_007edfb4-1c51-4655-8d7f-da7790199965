import os
from glob import glob

import cv2
import numpy as np

from vsc.composite_led.core import CompositeLEDInference


class Evaluation:
    def __init__(self, data_validate):
        self.composite_led_inference = CompositeLEDInference()
        self.data_validate = data_validate
        self.data = os.path.join(data_validate, "data")
        self.output = os.path.join(data_validate, "output")
        self.result = os.path.join(data_validate, "result")
        self.video_path_lists = []
        self.led_path = "data/led/LED_1.jpg"
        self._get_path()

    def _get_path(self):
        for root, dirs, files in os.walk(self.data):
            for file in files:
                if file.endswith(file):
                    self.video_path_lists.append(os.path.join(root, file))

    def _compare_csv(self):
        pass

    def _get_result(self):
        for video_path in self.video_path_lists:
            self.composite_led_inference = CompositeLEDInference()
            save_path = video_path.replace(self.data, self.result)
            save_path = save_path.replace(".mp4", f"_{os.path.basename(self.led_path)[:-4]}.mp4")
            self.composite_led_inference = CompositeLEDInference()
            self.composite_led_inference.run(video_path=video_path, led_path=self.led_path, save_path=save_path, is_dev=True)

    def _compare_video(self, video1_path, video2_path, similarity_threshold=0.95):
        matching_frames = 0
        total_frames = 0

        video1 = cv2.VideoCapture(video1_path)
        video2 = cv2.VideoCapture(video2_path)

        # Đảm bảo rằng cả hai video đều mở thành công
        if not video1.isOpened() or not video2.isOpened():
            print(f"Cannot open {video1_path}")
            return

        if not not video2.isOpened():
            print(f"Cannot open {video2_path}")
            return

        while True:
            ret1, frame1 = video1.read()
            ret2, frame2 = video2.read()

            if ret1 and ret2:
                total_frames += 1

                gray_frame1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
                gray_frame2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)

                difference = cv2.absdiff(gray_frame1, gray_frame2)
                similarity = 1 - (difference.sum() / (gray_frame1.shape[0] * gray_frame1.shape[1] * 255))

                print(similarity)

                if similarity > similarity_threshold:
                    matching_frames += 1

            else:
                break
        # Đóng hai video
        video1.release()
        video2.release()

        # Tính toán độ tương đồng
        if total_frames == 0:
            print("Error: No frames found in one or both of the videos.")
            return
        similarity_ratio = matching_frames / total_frames
        return similarity_ratio

    def run(self):
        # self._get_result()
        for video_path in self.video_path_lists:
            video_output = video_path.replace(self.data, self.output)
            video_result = video_path.replace(self.data, self.result)
            # video_result = video_result.replace("_LED_1", "")
            similarity = self._compare_video(video_output, video_result)
            print(similarity)


if __name__ == "__main__":
    data_eval = "data/led/data_for_eval"
    eval = Evaluation(data_eval)
    eval.run()
    print("..")
