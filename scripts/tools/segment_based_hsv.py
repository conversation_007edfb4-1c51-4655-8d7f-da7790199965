import os.path
import random

import cv2
import numpy as np


def nothing(x):
    pass


cv2.namedWindow("Trackbars")

cv2.createTrackbar("L-H", "Trackbars", 50, 255, nothing)
cv2.createTrackbar("L-S", "Trackbars", 210, 255, nothing)
cv2.createTrackbar("L-V", "Trackbars", 138, 255, nothing)
cv2.createTrackbar("U-H", "Trackbars", 80, 255, nothing)
cv2.createTrackbar("U-S", "Trackbars", 255, 255, nothing)
cv2.createTrackbar("U-V", "Trackbars", 255, 255, nothing)

folder = "data/led/hsv_sample"
image_list = [os.path.join(folder, file_name) for file_name in os.listdir(folder)]
frame = cv2.imread(random.choice(image_list))
frame = cv2.resize(frame, (640, 480))
hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)

while True:
    l_h = cv2.getTrackbarPos("L-H", "Trackbars")
    l_s = cv2.getTrackbarPos("L-S", "Trackbars")
    l_v = cv2.getTrackbarPos("L-V", "Trackbars")
    u_h = cv2.getTrackbarPos("U-H", "Trackbars")
    u_s = cv2.getTrackbarPos("U-S", "Trackbars")
    u_v = cv2.getTrackbarPos("U-V", "Trackbars")

    lower = np.array([l_h, l_s, l_v])
    upper = np.array([u_h, u_s, u_v])

    mask = cv2.inRange(hsv, lower, upper)
    result = cv2.bitwise_and(frame, frame, mask=mask)

    cv2.imshow("Frame", frame)
    cv2.imshow("Mask", mask)
    cv2.imshow("Result", result)

    key = cv2.waitKey(1)
    if key == ord("q"):
        break

cv2.destroyAllWindows()
