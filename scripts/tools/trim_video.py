import argparse
import os
import subprocess


def trim_video(video_path, output_path, mode=None, start=None, end=None):
    if mode == 'duration' and start is not None and end is not None:
        start_time = start
        end_time = end
    elif mode == 'frame_id' and start is not None and end is not None:
        start_time = start / get_fps(video_path)
        end_time = end / get_fps(video_path)
    else:
        print("Invalid mode or missing start/end time.")
        return

    # Convert start and end times to string format
    start_time_str = format_time(start_time)
    end_time_str = format_time(end_time)

    # Build ffmpeg command
    command = [
        "ffmpeg",
        "-i",
        video_path,
        "-ss",
        start_time_str,
        "-to",
        end_time_str,
        "-c",
        "copy",
        "-avoid_negative_ts",
        "1",
        output_path,
    ]

    # Execute ffmpeg command
    subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE)


def get_fps(video_path):
    result = subprocess.run(
        [
            "ffprobe",
            "-v",
            "error",
            "-select_streams",
            "v:0",
            "-show_entries",
            "stream=r_frame_rate",
            "-of",
            "default=noprint_wrappers=1:nokey=1",
            video_path,
        ],
        stdout=subprocess.PIPE,
    )
    fps = result.stdout.decode("utf-8").split('/')
    return int(fps[0]) / int(fps[1])


def format_time(seconds):
    hours = int(seconds // 3600)
    minutes = int((seconds % 3600) // 60)
    seconds = int(seconds % 60)
    return "{:02d}:{:02d}:{:02d}".format(hours, minutes, seconds)


def main():
    # Create an ArgumentParser
    parser = argparse.ArgumentParser(description='Trim video based on duration or frame ID')

    # Add arguments for video path, output path, mode, start, and end
    parser.add_argument('video_path', type=str, help='Path to the video')
    parser.add_argument('output_path', type=str, help='Output path for trimmed video')
    parser.add_argument('--mode', type=str, choices=['duration', 'frame_id'], help='Mode for trimming')
    parser.add_argument('--start', type=int, help='Start time or frame ID')
    parser.add_argument('--end', type=int, help='End time or frame ID')

    # Parse arguments from the command line
    args = parser.parse_args()

    # Call the function to trim the video
    trim_video(args.video_path, args.output_path, args.mode, args.start, args.end)


if __name__ == "__main__":
    main()
