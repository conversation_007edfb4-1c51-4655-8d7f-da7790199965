import os
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from typing import Dict, List

import cv2
import numpy as np
from skimage.metrics import structural_similarity as compare_ssim
from tqdm import tqdm


def compute_nrmse(img1: np.ndarray, img2: np.ndarray):
    """
    Compute the Normalized Root Mean Squared Error (NRMSE) between two images.

    NRMSE is calculated as the square root of the mean squared error (MSE) between the
    images, normalized by the range of pixel values in the first image.

    Args:
        img1 (np.ndarray): The first image (reference image).
        img2 (np.ndarray): The second image (comparison image).

    Returns:
        float: The NRMSE value between the two images. Lower values indicate better similarity.
    """
    mse = np.mean((img1.astype("float") - img2.astype("float")) ** 2)
    nrmse = np.sqrt(mse) / (img1.max() - img1.min())
    return nrmse


def compute_edge_discrepancy(image1: np.ndarray, image2: np.ndarray):
    '''
    Compute the scaled edge discrepancy between 2 images using Sobel filters.

    Parameters:
        image1 (np.ndarray): First image array of shape (H, W, C).
        image2 (np.ndarray): Second image array of the same shape as image1.

    Returns:
        float: Scaled edge discrepancy value (0 to 1)
    '''

    sobel_x = np.array([[-1, 0, 1], [-2, 0, 2], [-1, 0, 1]], dtype=np.float32)
    sobel_y = np.array([[-1, -2, -1], [0, 0, 0], [1, 2, 1]], dtype=np.float32)

    assert image1.shape == image2.shape

    Ix1 = cv2.filter2D(image1, -1, sobel_x)
    Ix2 = cv2.filter2D(image2, -1, sobel_x)
    Iy1 = cv2.filter2D(image1, -1, sobel_y)
    Iy2 = cv2.filter2D(image2, -1, sobel_y)

    edge_img1 = Ix1 + Iy1
    edge_img2 = Ix2 + Iy2

    return compute_nrmse(edge_img1, edge_img2)


def calculate_image_difference(image1: np.ndarray, image2: np.ndarray):
    """
    Calculate the differences between two images using NRMSE, SSIM, Gradient Difference.

    The function computes the following metrics:
        - NRMSE (Normalized Root Mean Squared Error): Measures overall pixel intensity differences.
        - SSIM (Structural Similarity Index): Measures structural similarity between the images.
        - Gradient Difference: Computes edge discrepancy between the images.

    Args:
        image1 (np.ndarray): The first image (reference image).
        image2 (np.ndarray): The second image (comparison image).

    Returns:
        tuple: A tuple containing:
            - nrmse (float): The NRMSE value between the two images.
            - ssim (float): The SSIM value between the two images. Higher values indicate better similarity.
            - gradient_difference (float): The gradient difference (edge discrepancy) between the images.

    Raises:
        AssertionError: If the two images do not have the same shape.
    """
    assert image1.shape == image2.shape, "Images must have the same shape."

    nrmse = compute_nrmse(image1, image2)
    gradient_difference = compute_edge_discrepancy(image1, image2)
    if image1.ndim == 3:
        ssim, _ = compare_ssim(cv2.cvtColor(image1, cv2.COLOR_BGR2GRAY), cv2.cvtColor(image2, cv2.COLOR_BGR2GRAY), full=True)
    else:
        ssim, _ = compare_ssim(image1, image2, full=True)
    return nrmse, ssim, gradient_difference


def process_frame(frame1: np.ndarray, frame2: np.ndarray, frame_id: int, do_resize: bool, results: List[Dict]):
    """
    Process a single frame and calculate the differences.
    This is the worker function for multithreading.

    Parameters:
        frame1 (ndarray): Frame from the first video.
        frame2 (ndarray): Frame from the second video.
        frame_id (int): Frame ID being processed.
        results (list): Shared list to store results.
    """
    if do_resize:
        frame1, frame2 = cv2.resize(frame1, (800, 800)), cv2.resize(frame2, (800, 800))
    nrmse, ssim, gradient_difference = calculate_image_difference(frame1, frame2)

    results.append(
        {
            "frame_id": frame_id,
            "nrmse": nrmse,
            "ssim": ssim,
            "gradient_difference": gradient_difference,
        }
    )


def check_similarity_multithreaded(vid1_dir: str, vid2_dir: str, sampling_factor: int = 7, num_threads: int = 4):
    """
    Compare two videos by sampling frames and calculating differences using multi-threading.
    The output of the function is mimumun or maximum difference of 3 criteria: NRMSE, SSIM, Gradient discrepancy.

    Parameters:
        vid1_dir (str): Path to the first video file.
        vid2_dir (str): Path to the second video file.
        sampling_factor (int): Fraction of frames to sample (default: 7, i.e., 1/7th).
        num_threads (int): Number of threads for processing.

    Returns:
        tuple: (nmrse_max, ssim_min, gradient_difference_max, peculiar_frame_ids)
    """
    cap1 = cv2.VideoCapture(vid1_dir)
    cap2 = cv2.VideoCapture(vid2_dir)

    total_frames1, total_frames2 = int(cap1.get(cv2.CAP_PROP_FRAME_COUNT)), int(cap2.get(cv2.CAP_PROP_FRAME_COUNT))
    frame_width1, frame_height1 = int(cap1.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap1.get(cv2.CAP_PROP_FRAME_HEIGHT))
    frame_width2, frame_height2 = int(cap2.get(cv2.CAP_PROP_FRAME_WIDTH)), int(cap2.get(cv2.CAP_PROP_FRAME_HEIGHT))

    if total_frames1 == 0 or total_frames2 == 0 or not cap2.isOpened():
        raise ValueError(
            f"One or both video files are invalid or empty: \n"
            f"Video 1: {vid1_dir} (Frames: {total_frames1}, Opened: {cap1.isOpened()})\n"
            f"Video 2: {vid2_dir} (Frames: {total_frames2}, Opened: {cap2.isOpened()})"
        )

    if total_frames1 != total_frames2:
        raise ValueError(
            f"Number of frames in video 1 and video 2 do not match: \n"
            f"Video 1: {vid1_dir} (Frames: {total_frames1})\n"
            f"Video 2: {vid2_dir} (Frames: {total_frames2})"
        )
    if frame_width1 != frame_width2 or frame_height1 != frame_height2:
        raise ValueError(
            f"Resolution mismatch between videos: \n"
            f"Video 1: {vid1_dir} (Resolution: {frame_width1}x{frame_height1})\n"
            f"Video 2: {vid2_dir} (Resolution: {frame_width2}x{frame_height2})"
        )

    do_resize = frame_width1 > 800 or frame_height1 > 800  # If true, resize the frames to speed up the code.
    sampled_frame_ids = np.random.choice(total_frames1, total_frames1 // sampling_factor, replace=False)
    results = []
    with ThreadPoolExecutor(max_workers=num_threads) as executor:
        futures = []
        frame_id = 0

        while True:
            ret1, frame1 = cap1.read()
            ret2, frame2 = cap2.read()

            if not ret1 or not ret2:
                break

            if frame_id in sampled_frame_ids:
                futures.append(executor.submit(process_frame, frame1, frame2, frame_id, do_resize, results))

            frame_id += 1

        # Wait for all threads to complete
        for future in tqdm(as_completed(futures), total=len(futures)):
            future.result()

    # Aggregate results
    nmrse_max = max([r["nrmse"] for r in results], default=0)
    ssim_min = min([r["ssim"] for r in results], default=1)
    gradient_difference_max = max([r["gradient_difference"] for r in results], default=0)
    peculiar_frame_ids = [r["frame_id"] for r in results if r["ssim"] < 0.8]

    cap1.release()
    cap2.release()

    return results, nmrse_max, ssim_min, gradient_difference_max, peculiar_frame_ids


def find_files_with_tail(folder_path, file_tail="_LED_1.mp4"):
    """
    Finds all files in the given folder (including subfolders) that end with the specified tail.

    Parameters:
        folder_path (str): Path to the folder to search in.
        file_tail (str): The file suffix to search for (default: "_LED_1.mp4").

    Returns:
        list: A list of file paths that match the specified suffix.
    """
    matching_files = []
    # Walk through the folder and its subfolders
    for root, _, files in os.walk(folder_path):
        for file in files:
            if file.endswith(file_tail):
                matching_files.append(os.path.join(root, file))

    return matching_files


def extract_relative_path_and_basename(file_path):
    """
    Extracts the relative path (from the subfolder) and the basename from the file path.

    Parameters:
        file_path (str): The full file path.

    Returns:
        tuple: The relative subfolder (excluding the main folder) and the basename of the file.
    """
    parts = os.path.normpath(file_path).split(os.sep)

    if len(parts) >= 3:
        subfolder = parts[-3]  # The parent folder before the shot folder
        basename = parts[-1]  # The file name (e.g., 1.mp4)
        return subfolder, basename
    elif len(parts) == 2:
        return None, parts[-1]
    else:
        return None, None


def find_pairs(folder1_dir: str, folder2_dir: str, file_tail: str = "_LED_1.mp4"):
    """
    Find matching pairs of files between two folders based on their relative subdirectory path and basename.

    Parameters:
        folder1_dir (str): Path to the first folder.
        folder2_dir (str): Path to the second folder.
        file_tail (str): Suffix of the files to match (default: "_LED_1.mp4").

    Returns:
        list: A list of tuples containing matching file pairs from folder1 and folder2.
    """
    pairs = []

    # Find all files in both folders
    files_in_folder1 = find_files_with_tail(folder1_dir, file_tail=file_tail)
    files_in_folder2 = find_files_with_tail(folder2_dir, file_tail=file_tail)

    # Create a dictionary for files in folder2 with relative paths as keys
    folder2_files_map = {}
    for file_path in files_in_folder2:
        # Compute the relative path (excluding the root folder) and basename
        relative_path = os.path.relpath(file_path, folder2_dir)
        folder2_files_map[relative_path] = file_path

    # Match files in folder1 with those in folder2
    for file_in_folder1 in files_in_folder1:
        # Compute the relative path (excluding the root folder) and basename
        relative_path = os.path.relpath(file_in_folder1, folder1_dir)

        # Check if the same relative path exists in folder2
        if relative_path in folder2_files_map:
            pairs.append((file_in_folder1, folder2_files_map[relative_path]))

    return pairs
