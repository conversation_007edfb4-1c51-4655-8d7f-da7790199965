import argparse
import os

import pandas as pd
from tqdm import tqdm

from scripts.tools.compare_video_utils import check_similarity_multithreaded, find_pairs


def compare_videos(
    folder1: str, folder2: str, file_tail: str = "_LED_1.mp4", sampling_factor: int = 7, output_csv="Comparison.csv"
):
    """
    Compares videos between two folders and outputs similarity metrics to a CSV file.

    Parameters:
        folder1 (str): Path to the first folder containing video files.
        folder2 (str): Path to the second folder containing video files.
        file_tail (str): _ + name of the LED path.
        sampling_factor (int): Divide the whole video into sampling_factor for efficiency.
        output_csv (str): Path to save the output CSV file (default: 'Comparison.csv').
    """

    data = {"Video_dir": [], "NMRSE_Max": [], "SSIM_min": [], "Gradient_difference_max": [], "Peculiar_frame_ids": []}

    # Find matching video pairs
    print("Finding matching video pairs...")
    pairs = find_pairs(folder1, folder2, file_tail=file_tail)
    if not pairs:
        print("No matching video pairs found.")
        return

    print(f"Found {len(pairs)} matching pairs. Starting comparison...")
    # Process each pair
    for vid1_dir, vid2_dir in tqdm(pairs, desc="Comparing videos"):
        print(f"Processing pair: \n  Video 1: {vid1_dir}\n  Video 2: {vid2_dir}")
        try:
            # Compute similarity metrics
            nmrse_max, ssim_min, gradient_diff_max, peculiar_frame_ids = check_similarity_multithreaded(
                vid1_dir, vid2_dir, sampling_factor
            )

            # Append metrics to data
            data["Video_dir"].append(vid1_dir)
            data["NMRSE_Max"].append(nmrse_max)
            data["SSIM_min"].append(ssim_min)
            data["Gradient_difference_max"].append(gradient_diff_max)
            data["Peculiar_frame_ids"].append(peculiar_frame_ids)

        except Exception as e:
            print(f"Error processing {vid1_dir} and {vid2_dir}: {e}")

    # Save results to CSV
    print(f"Saving results to {output_csv}...")
    df = pd.DataFrame(data)
    df.to_csv(output_csv, index=False)
    print("Comparison completed successfully.")


if __name__ == "__main__":
    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Compare video files between two folders and compute similarity metrics.")
    parser.add_argument("--folder1", type=str, help="Path to the first folder containing video files.")
    parser.add_argument("--folder2", type=str, help="Path to the second folder containing video files.")
    parser.add_argument(
        "--output_csv",
        type=str,
        default="Comparison.csv",
        help="Path to save the output CSV file (default: 'Comparison.csv').",
    )
    parser.add_argument(
        "--file_tail", type=str, default="_LED_1.mp4", help="The tail of the processed videos, e.g. _LED_1.mp4, _LED_2.mp4, "
    )
    parser.add_argument(
        "--sampling_factor",
        type=int,
        default=7,
        help="Random sampling frames to speed up the process, sampling_factor=1 means the whole video.",
    )
    args = parser.parse_args()

    # Normalize paths for cross-platform compatibility
    folder1 = os.path.normpath(args.folder1)
    folder2 = os.path.normpath(args.folder2)
    output_csv = os.path.normpath(args.output_csv)
    file_tail = args.file_tail
    sampling_factor = args.sampling_factor

    # Run the comparison
    compare_videos(
        folder1=folder1, folder2=folder2, file_tail=file_tail, sampling_factor=sampling_factor, output_csv=output_csv
    )
