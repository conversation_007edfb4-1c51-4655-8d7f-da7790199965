import argparse
import os

import cv2


def extract_frame(video_path, frame_id, save=True, show=True):
    # Read the video
    video_capture = cv2.VideoCapture(video_path)

    # Check if the video is opened successfully
    if not video_capture.isOpened():
        print("Unable to open the video.")
        return

    # Set the frame ID you want to extract
    video_capture.set(cv2.CAP_PROP_POS_FRAMES, frame_id)

    # Read the frame
    ret, frame = video_capture.read()

    # Check if the frame is read successfully
    if not ret:
        print("Unable to read the frame.")
        return

    video_name = os.path.basename(video_path)
    save_path = f"{video_name}_frame_{frame_id}.jpg"

    # Save the frame
    if save:
        cv2.imwrite(save_path, frame)
        print(f"Saved result in: {os.path.abspath(save_path)}")

    # Show the frame
    if show:
        cv2.imshow(save_path, frame)
        cv2.waitKey(0)

    # Release resources
    video_capture.release()
    cv2.destroyAllWindows()


def main():
    # Create an ArgumentParser
    parser = argparse.ArgumentParser(description='Extract frame from video')

    # Add arguments for video path and frame ID
    parser.add_argument('video_path', type=str, help='Path to the video')
    parser.add_argument('frame_id', type=int, help='ID of the frame to extract')

    # Parse arguments from the command line
    args = parser.parse_args()

    # Call the function to extract the frame
    extract_frame(args.video_path, args.frame_id)


if __name__ == "__main__":
    main()
