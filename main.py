import os

from vsc import CompositeLEDInference


input_path = "/data/dwg/datasets/vsc_datasets/videos/small_refactor_check/C14_Shot1.mp4"
# input_path = "/data/dwg/datasets/vsc_datasets/videos/capcut/21.mp4"
# led_path = "data/test/issue_220/leds/LED_Start_reverse.mp4"
led_path = "data/test/daophuquy.jpg"
output_path = "data/test/issue_166/v1_output/output.mp4"

os.makedirs(os.path.dirname(output_path), exist_ok=True)

instance = CompositeLEDInference(
    device="cuda:0",
)
instance.run(video_path=input_path, led_path=led_path, save_path=output_path, is_dev=True)
