import cv2
from fvutils.videoio import FVideoCapture, PixelFormat


# cap_led = FVideoCapture("data/test/issue_220/leds/LED_Start.mp4", output_pixel_format=PixelFormat.BGR24)
# i = 1
# for ret, frame in cap_led.read():
#     if not ret:
#         break
#
#     assert cv2.imwrite(f"data/test/issue_220/leds/images/{i:4d}.jpg", frame)
#     i += 1
#     print(i)


cap_led = cv2.VideoCapture("data/test/issue_220/leds/LED_Start.mp4")
i = 1
while True:
    ret, frame = cap_led.read()
    if not ret:
        break

    assert cv2.imwrite(f"data/test/issue_220/leds/images/{i:4d}.jpg", frame)
    i += 1
    print(i)
